<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('workflows', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->enum('project_type', ['music', 'podcast', 'video', 'live_session', 'mixing', 'mastering', 'other']);
            $table->json('stages'); // workflow stages definition
            $table->json('automation_rules')->nullable(); // automation rules
            $table->boolean('is_default')->default(false);
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            
            $table->index(['project_type', 'is_default']);
            $table->index(['is_active', 'created_by']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('workflows');
    }
};
