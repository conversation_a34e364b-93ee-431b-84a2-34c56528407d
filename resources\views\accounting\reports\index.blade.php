@extends('layouts.dashboard')

@section('title', 'Accounting Reports')

@section('dashboard-content')
<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Accounting Reports</h1>
</div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Profit & Loss Report -->
        <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Profit & Loss</h3>
                        <p class="text-sm text-gray-600">Revenue and expense summary</p>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">View detailed profit and loss statements for any period.</p>
                <div class="flex space-x-2">
                    <a href="{{ route('accounting.reports.profit-loss') }}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        View Report
                    </a>
                    <button onclick="openExportModal('profit-loss')" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Balance Sheet Report -->
        <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-balance-scale text-blue-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Balance Sheet</h3>
                        <p class="text-sm text-gray-600">Assets, liabilities, and equity</p>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">View your company's financial position at any point in time.</p>
                <div class="flex space-x-2">
                    <a href="{{ route('accounting.reports.balance-sheet') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        View Report
                    </a>
                    <button onclick="openExportModal('balance-sheet')" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Cash Flow Report -->
        <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-money-bill-wave text-purple-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Cash Flow</h3>
                        <p class="text-sm text-gray-600">Money in and out</p>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">Track cash flow from operations, investments, and financing.</p>
                <div class="flex space-x-2">
                    <a href="{{ route('financial.cash-flow') }}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        View Report
                    </a>
                    <button onclick="openExportModal('cash-flow')" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Invoice Summary -->
        <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-invoice text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Invoice Summary</h3>
                        <p class="text-sm text-gray-600">Invoice status and aging</p>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">View invoice summaries and aging reports.</p>
                <a href="{{ route('accounting.invoices.index') }}" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    View Invoices
                </a>
            </div>
        </div>

        <!-- Expense Summary -->
        <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-receipt text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Expense Summary</h3>
                        <p class="text-sm text-gray-600">Expense tracking and analysis</p>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">View expense summaries and category breakdowns.</p>
                <a href="{{ route('accounting.expenses.index') }}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    View Expenses
                </a>
            </div>
        </div>

        <!-- Tax Reports -->
        <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calculator text-indigo-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Tax Reports</h3>
                        <p class="text-sm text-gray-600">Tax preparation and filing</p>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">Generate reports for tax preparation and compliance.</p>
                <button class="bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium cursor-not-allowed" disabled>
                    Coming Soon
                </button>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="mt-8 bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Quick Financial Overview</h2>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">$0.00</div>
                <div class="text-sm text-gray-600">Total Revenue (MTD)</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-red-600">$0.00</div>
                <div class="text-sm text-gray-600">Total Expenses (MTD)</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">$0.00</div>
                <div class="text-sm text-gray-600">Net Profit (MTD)</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">$0.00</div>
                <div class="text-sm text-gray-600">Outstanding Invoices</div>
            </div>
        </div>
    </div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Export Report</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeExportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Export Format</label>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="pdf" class="mr-2" checked>
                        <i class="fas fa-file-pdf text-red-500 mr-2"></i>
                        PDF Document
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="csv" class="mr-2">
                        <i class="fas fa-file-csv text-green-500 mr-2"></i>
                        CSV Spreadsheet
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="zip" class="mr-2">
                        <i class="fas fa-file-archive text-blue-500 mr-2"></i>
                        ZIP Archive (All Formats)
                    </label>
                </div>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                <div class="grid grid-cols-2 gap-2">
                    <input type="date" id="export_start_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <input type="date" id="export_end_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors" onclick="closeExportModal()">Cancel</button>
                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" onclick="exportReport()">
                    <i class="fas fa-download mr-2"></i>Export
                </button>
            </div>
        </div>
    </div>
</div>
</div>

@push('scripts')
<script>
let currentReportType = '';

function openExportModal(reportType) {
    currentReportType = reportType;

    // Set default date range (last 30 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    document.getElementById('export_start_date').value = startDate.toISOString().split('T')[0];
    document.getElementById('export_end_date').value = endDate.toISOString().split('T')[0];

    document.getElementById('exportModal').classList.remove('hidden');
}

function closeExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
    currentReportType = '';
}

function exportReport() {
    const format = document.querySelector('input[name="export_format"]:checked').value;
    const startDate = document.getElementById('export_start_date').value;
    const endDate = document.getElementById('export_end_date').value;

    if (!startDate || !endDate) {
        alert('Please select both start and end dates');
        return;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('Start date cannot be after end date');
        return;
    }

    // Show loading state
    const exportBtn = document.querySelector('#exportModal button[onclick="exportReport()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Exporting...';
    exportBtn.disabled = true;

    // Create download URL
    const params = new URLSearchParams({
        report_type: currentReportType,
        format: format,
        start_date: startDate,
        end_date: endDate
    });

    // Create a temporary link to trigger download
    const downloadUrl = `/accounting/reports/export?${params.toString()}`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${currentReportType}-report-${startDate}-to-${endDate}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button state and close modal
    setTimeout(() => {
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
        closeExportModal();

        // Show success notification
        showNotification('Report export started. Download will begin shortly.', 'success');
    }, 1000);
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation-triangle'} mr-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 3000);
}
</script>
@endpush
@endsection
