@extends('layouts.dashboard')

@section('title', 'User Management')

@section('dashboard-content')
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">User Management</h1>
        <p class="mt-1 text-sm text-gray-600">Manage studio team members and their access</p>
    </div>
    <div class="flex space-x-3">
        <a href="{{ route('users.create') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-plus"></i>
            <span>Add User</span>
        </a>
    </div>
</div>

<!-- Quick Stats -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-users text-blue-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Users</p>
                <p class="text-2xl font-bold text-gray-900">{{ $users->total() }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-user-check text-green-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Active Users</p>
                <p class="text-2xl font-bold text-gray-900">{{ $users->where('status', 'active')->count() }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-user-clock text-purple-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Available Now</p>
                <p class="text-2xl font-bold text-gray-900">{{ $users->where('is_available', true)->count() }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <i class="fas fa-user-plus text-yellow-600"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">New This Month</p>
                <p class="text-2xl font-bold text-gray-900">{{ $users->where('created_at', '>=', now()->startOfMonth())->count() }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}" 
                               placeholder="Search users..." 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="department" class="block text-sm font-medium text-gray-700">Department</label>
                        <select name="department" id="department" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Departments</option>
                            @foreach($departments as $dept)
                                <option value="{{ $dept->id }}" {{ request('department') == $dept->id ? 'selected' : '' }}>
                                    {{ $dept->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
                        <select name="role" id="role" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Roles</option>
                            @foreach($roles as $role)
                                <option value="{{ $role->id }}" {{ request('role') == $role->id ? 'selected' : '' }}>
                                    {{ $role->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Team Members ({{ $users->total() }})</h3>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Active</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse($users as $user)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                            @if($user->avatar)
                                                <img src="{{ $user->avatar }}" alt="{{ $user->first_name }}" class="w-10 h-10 rounded-full">
                                            @else
                                                <span class="text-sm font-medium text-gray-600">
                                                    {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
                                                </span>
                                            @endif
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $user->first_name }} {{ $user->last_name }}
                                            </div>
                                            <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($user->department)
                                        <div class="flex items-center">
                                            <i class="{{ $user->department->icon ?? 'fas fa-building' }} text-{{ $user->department->color_code ?? 'blue' }}-600 mr-2"></i>
                                            <span class="text-sm text-gray-900">{{ $user->department->name }}</span>
                                        </div>
                                    @else
                                        <span class="text-sm text-gray-500">No Department</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @if($user->role)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ $user->role->name }}
                                        </span>
                                    @else
                                        <span class="text-sm text-gray-500">No Role</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($user->status === 'active') bg-green-100 text-green-800
                                        @elseif($user->status === 'inactive') bg-red-100 text-red-800
                                        @else bg-yellow-100 text-yellow-800 @endif">
                                        {{ ucfirst($user->status) }}
                                    </span>
                                    @if($user->is_available)
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            Available
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    @if($user->last_active_at)
                                        {{ $user->last_active_at->diffForHumans() }}
                                    @else
                                        Never
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewUser('{{ $user->id }}')" class="text-blue-600 hover:text-blue-900">View</button>
                                        <button onclick="editUser('{{ $user->id }}')" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                        @if($user->id !== auth()->id())
                                            <button onclick="confirmUserAction('{{ $user->id }}', '{{ $user->status === 'active' ? 'deactivate' : 'activate' }}')" class="text-{{ $user->status === 'active' ? 'red' : 'green' }}-600 hover:text-{{ $user->status === 'active' ? 'red' : 'green' }}-900">
                                                {{ $user->status === 'active' ? 'Deactivate' : 'Activate' }}
                                            </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="6" class="px-6 py-12 text-center">
                                    <i class="fas fa-users text-gray-400 text-3xl mb-4"></i>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                                    <p class="text-gray-500 mb-4">No users match your current filters.</p>
                                    <a href="{{ route('users.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                        Add First User
                                    </a>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($users->hasPages())
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $users->links() }}
                </div>
            @endif
        </div>
<!-- View User Modal -->
<div id="viewUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">User Details</h3>
            <button onclick="closeViewUserModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="userDetails">
            <!-- User details will be loaded here -->
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div id="editUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Edit User</h3>
            <button onclick="closeEditUserModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="editUserForm">
            <!-- Edit form will be loaded here -->
        </div>
    </div>
</div>

<!-- Confirm Action Modal -->
<div id="confirmActionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-md shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900" id="confirmActionTitle">Confirm Action</h3>
            <button onclick="closeConfirmActionModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="confirmActionContent">
            <!-- Confirmation content will be loaded here -->
        </div>
        <div class="flex justify-end space-x-4 mt-6">
            <button onclick="closeConfirmActionModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
            <button onclick="executeUserAction()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg" id="confirmActionButton">Confirm</button>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Modal functions
let currentUserId = null;
let currentAction = null;

function viewUser(userId) {
    $.ajax({
        url: `/api/users/${userId}`,
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            displayUserDetails(response.data);
            $('#viewUserModal').removeClass('hidden');
        } else {
            alert('Error loading user details: ' + response.message);
        }
    })
    .fail(function(xhr) {
        console.error('View user error:', xhr);
        alert('Failed to load user details');
    });
}

function editUser(userId) {
    $.ajax({
        url: `/api/users/${userId}`,
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            displayEditForm(response.data);
            $('#editUserModal').removeClass('hidden');
        } else {
            alert('Error loading user details: ' + response.message);
        }
    })
    .fail(function(xhr) {
        console.error('Edit user error:', xhr);
        alert('Failed to load user details');
    });
}

function confirmUserAction(userId, action) {
    currentUserId = userId;
    currentAction = action;

    const actionText = action === 'deactivate' ? 'deactivate' : 'activate';
    const actionColor = action === 'deactivate' ? 'red' : 'green';

    $('#confirmActionTitle').text(`Confirm ${actionText.charAt(0).toUpperCase() + actionText.slice(1)}`);
    $('#confirmActionContent').html(`
        <p class="text-gray-600">Are you sure you want to ${actionText} this user?</p>
        <p class="text-sm text-gray-500 mt-2">This action can be reversed later.</p>
    `);
    $('#confirmActionButton').removeClass('bg-red-600 hover:bg-red-700 bg-green-600 hover:bg-green-700')
        .addClass(`bg-${actionColor}-600 hover:bg-${actionColor}-700`);

    $('#confirmActionModal').removeClass('hidden');
}

function closeViewUserModal() {
    $('#viewUserModal').addClass('hidden');
}

function closeEditUserModal() {
    $('#editUserModal').addClass('hidden');
}

function closeConfirmActionModal() {
    $('#confirmActionModal').addClass('hidden');
    currentUserId = null;
    currentAction = null;
}

function displayUserDetails(user) {
    const detailsHtml = `
        <div class="space-y-4">
            <div class="flex items-center space-x-4">
                <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                    ${user.avatar ?
                        `<img src="${user.avatar}" alt="${user.first_name}" class="w-16 h-16 rounded-full">` :
                        `<span class="text-lg font-medium text-gray-600">${user.first_name.charAt(0)}${user.last_name.charAt(0)}</span>`
                    }
                </div>
                <div>
                    <h4 class="text-lg font-medium text-gray-900">${user.first_name} ${user.last_name}</h4>
                    <p class="text-gray-600">${user.email}</p>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Department</label>
                    <p class="text-sm text-gray-900">${user.department ? user.department.name : 'No Department'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Role</label>
                    <p class="text-sm text-gray-900">${user.role ? user.role.name : 'No Role'}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Status</label>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                        ${user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                    </span>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Last Active</label>
                    <p class="text-sm text-gray-900">${user.last_active_at || 'Never'}</p>
                </div>
            </div>
        </div>
    `;
    $('#userDetails').html(detailsHtml);
}

function executeUserAction() {
    if (!currentUserId || !currentAction) return;

    toggleUserStatus(currentUserId);
    closeConfirmActionModal();
}

    // Auto-submit form on filter change
    document.querySelectorAll('select[name="department"], select[name="role"], select[name="status"]').forEach(select => {
        select.addEventListener('change', function() {
            this.form.submit();
        });
    });

    function toggleUserStatus(userId) {
        if (confirm('Are you sure you want to change this user\'s status?')) {
            fetch(`/users/${userId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the user status');
            });
        }
    }
</script>
@endpush
