<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class StudioRoom extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'type',
        'capacity',
        'hourly_rate',
        'equipment',
        'features',
        'image',
        'images',
        'is_available',
        'requires_engineer',
        'availability_schedule'
    ];

    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'equipment' => 'array',
        'features' => 'array',
        'images' => 'array',
        'is_available' => 'boolean',
        'requires_engineer' => 'boolean',
        'availability_schedule' => 'array'
    ];

    // relationships
    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    // scopes
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeWithCapacity($query, $minCapacity)
    {
        return $query->where('capacity', '>=', $minCapacity);
    }

    // methods
    public function isAvailableAt(\DateTime $startTime, \DateTime $endTime): bool
    {
        if (!$this->is_available) {
            return false;
        }

        // check for conflicting bookings
        $conflictingBookings = $this->bookings()
            ->where(function ($query) use ($startTime, $endTime) {
                $query->where(function ($q) use ($startTime, $endTime) {
                    $q->where('start_time', '<', $endTime)
                      ->where('end_time', '>', $startTime);
                });
            })
            ->whereIn('status', ['confirmed', 'in_progress'])
            ->count();

        if ($conflictingBookings > 0) {
            return false;
        }

        // check availability schedule
        return $this->isWithinSchedule($startTime, $endTime);
    }

    private function isWithinSchedule(\DateTime $startTime, \DateTime $endTime): bool
    {
        if (!$this->availability_schedule) {
            return true; // no schedule restrictions
        }

        $dayOfWeek = strtolower($startTime->format('l'));
        $schedule = $this->availability_schedule[$dayOfWeek] ?? null;

        if (!$schedule) {
            return false; // not available on this day
        }

        foreach ($schedule as $timeSlot) {
            [$slotStart, $slotEnd] = explode('-', $timeSlot);
            
            $slotStartTime = \DateTime::createFromFormat('H:i', $slotStart);
            $slotEndTime = \DateTime::createFromFormat('H:i', $slotEnd);
            
            $requestStartTime = \DateTime::createFromFormat('H:i', $startTime->format('H:i'));
            $requestEndTime = \DateTime::createFromFormat('H:i', $endTime->format('H:i'));

            if ($requestStartTime >= $slotStartTime && $requestEndTime <= $slotEndTime) {
                return true;
            }
        }

        return false;
    }

    public function getBookingRate(\DateTime $startTime, \DateTime $endTime): float
    {
        $hours = $startTime->diff($endTime)->h + ($startTime->diff($endTime)->i / 60);
        return $this->hourly_rate * $hours;
    }

    public function getUtilizationRate(int $days = 30): float
    {
        $totalHours = $days * 24; // total possible hours
        
        $bookedHours = $this->bookings()
            ->where('start_time', '>=', now()->subDays($days))
            ->whereIn('status', ['confirmed', 'completed'])
            ->get()
            ->sum(function ($booking) {
                return $booking->start_time->diffInHours($booking->end_time);
            });

        return $totalHours > 0 ? ($bookedHours / $totalHours) * 100 : 0;
    }

    public function getRevenueForPeriod(\DateTime $startDate, \DateTime $endDate): float
    {
        return $this->bookings()
            ->where('start_time', '>=', $startDate)
            ->where('start_time', '<=', $endDate)
            ->whereIn('status', ['confirmed', 'completed'])
            ->sum('total_cost');
    }
}
