<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\StudioRoomController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\FileUploadController;
use App\Http\Controllers\WorkflowController;
use App\Http\Controllers\FinancialController;
use App\Http\Controllers\AccountingController;
use App\Http\Controllers\FileManagerController;

// Public routes
Route::post('/auth/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Authentication
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/me', [AuthController::class, 'me']);
    Route::put('/auth/profile', [AuthController::class, 'updateProfile']);
    Route::put('/auth/password', [AuthController::class, 'changePassword']);

    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index']);

    // Projects
    Route::apiResource('projects', ProjectController::class);
    Route::post('/projects/{project}/team-members', [ProjectController::class, 'addTeamMember']);
    Route::delete('/projects/{project}/team-members/{user}', [ProjectController::class, 'removeTeamMember']);
    Route::post('/projects/{project}/update-progress', [ProjectController::class, 'updateProgress']);

    // Tasks
    Route::apiResource('tasks', TaskController::class);
    Route::post('/tasks/{task}/start', [TaskController::class, 'start']);
    Route::post('/tasks/{task}/complete', [TaskController::class, 'complete']);
    Route::put('/tasks/{task}/progress', [TaskController::class, 'updateProgress']);

    // Bookings
    Route::apiResource('bookings', BookingController::class);
    Route::post('/bookings/{booking}/confirm', [BookingController::class, 'confirm']);
    Route::post('/bookings/{booking}/check-in', [BookingController::class, 'checkIn']);
    Route::post('/bookings/{booking}/check-out', [BookingController::class, 'checkOut']);
    Route::post('/bookings/{booking}/cancel', [BookingController::class, 'cancel']);
    Route::get('/studio-rooms/availability', [BookingController::class, 'availability']);

    // Clients
    Route::apiResource('clients', ClientController::class);
    Route::get('/clients/{client}/stats', [ClientController::class, 'stats']);
    Route::get('/clients/{client}/projects', [ClientController::class, 'projects']);
    Route::get('/clients/{client}/bookings', [ClientController::class, 'bookings']);
    Route::post('/clients/{client}/update-stats', [ClientController::class, 'updateStats']);

    // Studio Rooms
    Route::apiResource('studio-rooms', StudioRoomController::class);
    Route::get('/studio-rooms/{room}/availability', [StudioRoomController::class, 'availability']);
    Route::get('/studio-rooms/{room}/stats', [StudioRoomController::class, 'stats']);
    Route::get('/studio-rooms/{room}/schedule', [StudioRoomController::class, 'schedule']);

    // Chat & Communication
    Route::get('/chat/channels', [ChatController::class, 'channels']);
    Route::post('/chat/channels', [ChatController::class, 'createChannel']);
    Route::get('/chat/channels/{channel}/messages', [ChatController::class, 'messages']);
    Route::post('/chat/channels/{channel}/messages', [ChatController::class, 'sendMessage']);
    Route::put('/chat/messages/{message}', [ChatController::class, 'editMessage']);
    Route::delete('/chat/messages/{message}', [ChatController::class, 'deleteMessage']);
    Route::post('/chat/messages/{message}/reactions', [ChatController::class, 'addReaction']);
    Route::delete('/chat/messages/{message}/reactions', [ChatController::class, 'removeReaction']);
    Route::post('/chat/channels/{channel}/join', [ChatController::class, 'joinChannel']);
    Route::post('/chat/channels/{channel}/leave', [ChatController::class, 'leaveChannel']);
    Route::get('/chat/channels/{channel}/members', [ChatController::class, 'channelMembers']);

    // Analytics & Reporting
    Route::get('/analytics/overview', [AnalyticsController::class, 'overview']);
    Route::get('/analytics/departments', [AnalyticsController::class, 'departmentPerformance']);
    Route::get('/analytics/users', [AnalyticsController::class, 'userPerformance']);
    Route::get('/analytics/projects', [AnalyticsController::class, 'projectAnalytics']);
    Route::get('/analytics/revenue', [AnalyticsController::class, 'revenueAnalytics']);

    // File Management
    Route::post('/files/upload', [FileUploadController::class, 'upload']);
    Route::get('/files', [FileUploadController::class, 'list']);
    Route::get('/files/{file}/download', [FileUploadController::class, 'download']);
    Route::get('/files/{file}/stream', [FileUploadController::class, 'stream']);
    Route::delete('/files/{file}', [FileUploadController::class, 'delete']);
    Route::post('/files/{file}/versions', [FileUploadController::class, 'createVersion']);

    // Workflow Automation
    Route::apiResource('workflows', WorkflowController::class);
    Route::post('/workflows/{workflow}/toggle', [WorkflowController::class, 'toggle']);
    Route::post('/workflows/{workflow}/execute', [WorkflowController::class, 'execute']);
    Route::get('/workflows/templates/list', [WorkflowController::class, 'templates']);
    Route::post('/workflows/from-template', [WorkflowController::class, 'createFromTemplate']);

    // Financial Management
    Route::get('/financial/dashboard', [FinancialController::class, 'dashboard']);
    Route::get('/financial/revenue-analysis', [FinancialController::class, 'revenueAnalysis']);
    Route::get('/financial/expense-tracking', [FinancialController::class, 'expenseTracking']);
    Route::get('/financial/profitability', [FinancialController::class, 'profitabilityAnalysis']);
    Route::get('/financial/cash-flow', [FinancialController::class, 'cashFlowAnalysis']);
    Route::get('/financial/clients/{client}/profile', [FinancialController::class, 'clientFinancialProfile']);

    // Accounting
    Route::get('/accounting/dashboard', [AccountingController::class, 'dashboard']);
    Route::get('/accounting/invoices', [AccountingController::class, 'invoices']);
    Route::post('/accounting/invoices', [AccountingController::class, 'createInvoice']);
    Route::post('/accounting/invoices/{invoice}/send', [AccountingController::class, 'sendInvoice']);
    Route::post('/accounting/invoices/{invoice}/payments', [AccountingController::class, 'recordPayment']);
    Route::get('/accounting/expenses', [AccountingController::class, 'expenses']);
    Route::post('/accounting/expenses', [AccountingController::class, 'createExpense']);
    Route::get('/accounting/reports/profit-loss', [AccountingController::class, 'profitLossReport']);
    Route::get('/accounting/reports/balance-sheet', [AccountingController::class, 'balanceSheet']);

    // File Manager
    Route::get('/file-manager', [FileManagerController::class, 'index']);
    Route::post('/file-manager/upload-multiple', [FileManagerController::class, 'uploadMultiple']);
    Route::post('/file-manager/create-folder', [FileManagerController::class, 'createFolder']);
    Route::post('/file-manager/move', [FileManagerController::class, 'move']);
    Route::post('/file-manager/copy', [FileManagerController::class, 'copy']);
    Route::post('/file-manager/bulk-delete', [FileManagerController::class, 'bulkDelete']);
    Route::post('/file-manager/download-zip', [FileManagerController::class, 'downloadZip']);
    Route::get('/file-manager/search', [FileManagerController::class, 'search']);

    // Health check
    Route::get('/health', function () {
        return response()->json([
            'success' => true,
            'message' => 'Glitch Africa Studio Management API is running',
            'data' => [
                'status' => 'ok',
                'timestamp' => now(),
                'version' => '1.0.0'
            ]
        ]);
    });
});
