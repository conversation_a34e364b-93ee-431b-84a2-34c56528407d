<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Client extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'company',
        'type',
        'bio',
        'avatar',
        'social_links',
        'status',
        'total_spent',
        'total_projects',
        'rating',
        'preferences',
        'last_project_at'
    ];

    protected $casts = [
        'social_links' => 'array',
        'preferences' => 'array',
        'total_spent' => 'decimal:2',
        'rating' => 'decimal:2',
        'last_project_at' => 'datetime'
    ];

    // relationships
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    // scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeHighValue($query, $threshold = 10000)
    {
        return $query->where('total_spent', '>=', $threshold);
    }

    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_project_at', '>=', now()->subDays($days));
    }

    // methods
    public function getAverageProjectValue(): float
    {
        if ($this->total_projects === 0) {
            return 0;
        }
        
        return $this->total_spent / $this->total_projects;
    }

    public function updateProjectStats(): void
    {
        $stats = $this->projects()
            ->selectRaw('COUNT(*) as count, SUM(total_cost) as total')
            ->first();

        $this->update([
            'total_projects' => $stats->count ?? 0,
            'total_spent' => $stats->total ?? 0,
            'last_project_at' => $this->projects()->latest()->value('created_at')
        ]);
    }

    public function isHighValueClient(): bool
    {
        return $this->total_spent >= 10000;
    }

    public function getClientTier(): string
    {
        if ($this->total_spent >= 50000) {
            return 'platinum';
        } elseif ($this->total_spent >= 25000) {
            return 'gold';
        } elseif ($this->total_spent >= 10000) {
            return 'silver';
        }
        
        return 'bronze';
    }
}
