@extends('layouts.app')

@section('title', 'Role Details - ' . $role->name)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $role->name }}</h1>
                    <p class="mt-1 text-sm text-gray-600">Role details and permissions</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('roles.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Back to Roles
                    </a>
                    @if(!$role->is_system_role || auth()->user()->hasPermission('*'))
                        <a href="{{ route('roles.edit', $role) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Edit Role
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Role Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Info -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Role Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Name</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $role->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Department</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    <span class="inline-flex items-center">
                                        <i class="{{ $role->department->icon ?? 'fas fa-building' }} text-{{ $role->department->color_code ?? 'blue' }}-600 mr-2"></i>
                                        {{ $role->department->name }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Authority Level</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    Level {{ $role->level }}
                                    @if($role->level >= 8)
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Senior</span>
                                    @elseif($role->level >= 5)
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Mid-level</span>
                                    @else
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Junior</span>
                                    @endif
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $role->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $role->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Type</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $role->is_system_role ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $role->is_system_role ? 'System Role' : 'Custom Role' }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Users Count</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $role->users->count() }} users</dd>
                            </div>
                        </dl>
                        
                        @if($role->description)
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $role->description }}</dd>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Permissions -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Permissions</h3>
                        <p class="mt-1 text-sm text-gray-600">{{ count($role->permissions ?? []) }} permissions assigned</p>
                    </div>
                    <div class="p-6">
                        @if($role->permissions && count($role->permissions) > 0)
                            @php
                                $groupedPermissions = [];
                                foreach($role->permissions as $permission) {
                                    $parts = explode('_', $permission);
                                    $category = implode('_', array_slice($parts, 1));
                                    if (!isset($groupedPermissions[$category])) {
                                        $groupedPermissions[$category] = [];
                                    }
                                    $groupedPermissions[$category][] = $permission;
                                }
                            @endphp
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                @foreach($groupedPermissions as $category => $perms)
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900 mb-2 capitalize">
                                            {{ str_replace('_', ' ', $category) }}
                                        </h4>
                                        <ul class="space-y-1">
                                            @foreach($perms as $perm)
                                                <li class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-check text-green-500 mr-2"></i>
                                                    {{ str_replace('_', ' ', $perm) }}
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-lock text-gray-400 text-3xl mb-4"></i>
                                <p class="text-gray-500">No permissions assigned to this role</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Users with this role -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Users ({{ $role->users->count() }})</h3>
                    </div>
                    <div class="p-6">
                        @if($role->users->count() > 0)
                            <div class="space-y-3">
                                @foreach($role->users->take(10) as $user)
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                            @if($user->avatar)
                                                <img src="{{ $user->avatar }}" alt="{{ $user->first_name }}" class="w-8 h-8 rounded-full">
                                            @else
                                                <span class="text-xs font-medium text-gray-600">
                                                    {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
                                                </span>
                                            @endif
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">
                                                {{ $user->first_name }} {{ $user->last_name }}
                                            </p>
                                            <p class="text-xs text-gray-500">{{ $user->department->name ?? 'No Department' }}</p>
                                        </div>
                                    </div>
                                @endforeach
                                
                                @if($role->users->count() > 10)
                                    <p class="text-sm text-gray-500 mt-3">
                                        And {{ $role->users->count() - 10 }} more users...
                                    </p>
                                @endif
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-users text-gray-400 text-2xl mb-2"></i>
                                <p class="text-sm text-gray-500">No users assigned</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        @if(!$role->is_system_role || auth()->user()->hasPermission('*'))
                            <a href="{{ route('roles.edit', $role) }}" class="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                                Edit Role
                            </a>
                        @endif
                        
                        @if(!$role->is_system_role && $role->users->count() === 0)
                            <button onclick="deleteRole()" class="w-full bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md text-sm font-medium">
                                Delete Role
                            </button>
                        @endif
                        
                        <a href="{{ route('roles.index') }}" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Back to Roles
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Delete Confirmation Modal -->
@if(!$role->is_system_role && $role->users->count() === 0)
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Role</h3>
        <p class="text-sm text-gray-600 mb-6">
            Are you sure you want to delete this role? This action cannot be undone.
        </p>
        <div class="flex justify-end space-x-3">
            <button onclick="closeDeleteModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium">
                Cancel
            </button>
            <form action="{{ route('roles.destroy', $role) }}" method="POST" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
    function deleteRole() {
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.add('flex');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        document.getElementById('deleteModal').classList.remove('flex');
    }
</script>
@endpush
