@extends('layouts.app')

@section('title', 'File Manager')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">File Manager</h1>
                <div class="btn-group">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fas fa-upload"></i> Upload Files
                    </button>
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#createFolderModal">
                        <i class="fas fa-folder-plus"></i> New Folder
                    </button>
                </div>
            </div>

            <!-- File Actions Bar -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchFiles" placeholder="Search files...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end gap-2">
                                <select class="form-select form-select-sm" style="width: auto;" id="filterType">
                                    <option value="">All Types</option>
                                    <option value="image">Images</option>
                                    <option value="audio">Audio</option>
                                    <option value="video">Video</option>
                                    <option value="document">Documents</option>
                                </select>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary active" onclick="setView('grid')">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="setView('list')">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('files.index') }}">Home</a></li>
                    @if(request('folder'))
                    <li class="breadcrumb-item active">{{ request('folder') }}</li>
                    @endif
                </ol>
            </nav>

            <!-- Files Grid View -->
            <div id="grid-view">
                <div class="row">
                    @forelse($files as $file)
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-4">
                        <div class="card h-100 file-card" data-file-id="{{ $file->id }}">
                            <div class="card-body text-center p-2">
                                <div class="file-icon mb-2">
                                    @if($file->type == 'image')
                                    <img src="{{ Storage::url($file->path) }}" alt="{{ $file->name }}" class="img-fluid rounded" style="max-height: 80px;">
                                    @else
                                    <i class="fas fa-{{ $file->icon }} fa-3x text-{{ $file->color }}"></i>
                                    @endif
                                </div>
                                <h6 class="card-title small mb-1" title="{{ $file->name }}">{{ Str::limit($file->name, 15) }}</h6>
                                <small class="text-muted">{{ $file->size_formatted }}</small>
                            </div>
                            <div class="card-footer p-1">
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-outline-primary btn-sm" onclick="previewFile({{ $file->id }})" title="Preview">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="downloadFile({{ $file->id }})" title="Download">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteFile({{ $file->id }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    @empty
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">No files found</h4>
                            <p class="text-muted">Upload your first file to get started.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                Upload Files
                            </button>
                        </div>
                    </div>
                    @endforelse
                </div>
            </div>

            <!-- Files List View -->
            <div id="list-view" style="display: none;">
                <div class="card shadow">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Size</th>
                                        <th>Modified</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($files as $file)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-{{ $file->icon }} text-{{ $file->color }} me-2"></i>
                                                {{ $file->name }}
                                            </div>
                                        </td>
                                        <td>{{ strtoupper($file->extension) }}</td>
                                        <td>{{ $file->size_formatted }}</td>
                                        <td>{{ $file->updated_at->format('M d, Y g:i A') }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="previewFile({{ $file->id }})">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="downloadFile({{ $file->id }})">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteFile({{ $file->id }})">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            @if($files->hasPages())
            <div class="d-flex justify-content-center mt-4">
                {{ $files->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Files</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="upload-area border-dashed border-2 border-primary rounded p-4 text-center" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                    <h5>Drag & Drop Files Here</h5>
                    <p class="text-muted">or click to browse files</p>
                    <input type="file" id="fileInput" multiple class="d-none">
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                        Choose Files
                    </button>
                </div>
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
                <div id="uploadedFiles" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="uploadBtn" onclick="uploadFiles()" disabled>
                    Upload Files
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewTitle">File Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="downloadFromPreview">Download</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let selectedFiles = [];

function setView(view) {
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');
    const buttons = document.querySelectorAll('.btn-group .btn');
    
    buttons.forEach(btn => btn.classList.remove('active'));
    
    if (view === 'grid') {
        gridView.style.display = 'block';
        listView.style.display = 'none';
        buttons[0].classList.add('active');
    } else {
        gridView.style.display = 'none';
        listView.style.display = 'block';
        buttons[1].classList.add('active');
    }
}

// File upload handling
document.getElementById('fileInput').addEventListener('change', function(e) {
    selectedFiles = Array.from(e.target.files);
    displaySelectedFiles();
    document.getElementById('uploadBtn').disabled = selectedFiles.length === 0;
});

// Drag and drop
const uploadArea = document.getElementById('uploadArea');
uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    this.classList.add('border-success');
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    this.classList.remove('border-success');
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    this.classList.remove('border-success');
    selectedFiles = Array.from(e.dataTransfer.files);
    displaySelectedFiles();
    document.getElementById('uploadBtn').disabled = selectedFiles.length === 0;
});

function displaySelectedFiles() {
    const container = document.getElementById('uploadedFiles');
    container.innerHTML = '';
    
    selectedFiles.forEach((file, index) => {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'alert alert-info d-flex justify-content-between align-items-center';
        fileDiv.innerHTML = `
            <div>
                <i class="fas fa-file me-2"></i>
                ${file.name} (${formatFileSize(file.size)})
            </div>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        container.appendChild(fileDiv);
    });
}

function removeFile(index) {
    selectedFiles.splice(index, 1);
    displaySelectedFiles();
    document.getElementById('uploadBtn').disabled = selectedFiles.length === 0;
}

function uploadFiles() {
    if (selectedFiles.length === 0) return;
    
    const formData = new FormData();
    selectedFiles.forEach(file => {
        formData.append('files[]', file);
    });
    
    const progressBar = document.querySelector('#uploadProgress .progress-bar');
    document.getElementById('uploadProgress').style.display = 'block';
    
    fetch('{{ route("files.upload") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Upload failed: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        alert('Upload failed');
    });
}

function previewFile(fileId) {
    fetch(`/files/${fileId}`)
        .then(response => response.json())
        .then(data => {
            const modal = new bootstrap.Modal(document.getElementById('previewModal'));
            document.getElementById('previewTitle').textContent = data.name;
            
            let content = '';
            if (data.type === 'image') {
                content = `<img src="${data.url}" class="img-fluid" alt="${data.name}">`;
            } else if (data.type === 'audio') {
                content = `<audio controls class="w-100"><source src="${data.url}" type="${data.mime_type}"></audio>`;
            } else if (data.type === 'video') {
                content = `<video controls class="w-100" style="max-height: 400px;"><source src="${data.url}" type="${data.mime_type}"></video>`;
            } else {
                content = `<div class="text-center py-4"><i class="fas fa-file fa-3x text-muted mb-3"></i><p>Preview not available for this file type</p></div>`;
            }
            
            document.getElementById('previewContent').innerHTML = content;
            document.getElementById('downloadFromPreview').onclick = () => downloadFile(fileId);
            modal.show();
        });
}

function downloadFile(fileId) {
    window.open(`/files/${fileId}/download`, '_blank');
}

function deleteFile(fileId) {
    if (confirm('Are you sure you want to delete this file?')) {
        fetch(`/files/${fileId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Delete failed: ' + data.message);
            }
        });
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Search functionality
document.getElementById('searchFiles').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    const fileCards = document.querySelectorAll('.file-card');
    
    fileCards.forEach(card => {
        const fileName = card.querySelector('.card-title').textContent.toLowerCase();
        if (fileName.includes(searchTerm)) {
            card.parentElement.style.display = 'block';
        } else {
            card.parentElement.style.display = 'none';
        }
    });
});
</script>
@endpush

@push('styles')
<style>
.border-dashed {
    border-style: dashed !important;
}

.upload-area {
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    background-color: #f8f9fc;
}

.file-card {
    transition: transform 0.2s;
    cursor: pointer;
}

.file-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.file-icon {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
@endpush
