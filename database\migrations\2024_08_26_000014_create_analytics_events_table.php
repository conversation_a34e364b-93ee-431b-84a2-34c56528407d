<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('analytics_events', function (Blueprint $table) {
            $table->id();
            $table->string('event_type'); // task_completed, project_started, etc.
            $table->string('event_category'); // project, task, booking, etc.
            $table->morphs('trackable'); // the entity being tracked
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->json('properties')->nullable(); // event properties
            $table->decimal('value', 12, 2)->nullable(); // monetary value if applicable
            $table->string('session_id')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamp('occurred_at');
            $table->timestamps();
            
            $table->index(['event_type', 'event_category']);
            // morphs already creates this index
            $table->index(['user_id', 'occurred_at']);
            $table->index('occurred_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('analytics_events');
    }
};
