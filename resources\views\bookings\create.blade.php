@extends('layouts.app')

@section('title', 'Create Booking')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Create Studio Booking</h1>
                <div class="btn-group">
                    <a href="{{ route('bookings.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Bookings
                    </a>
                    <a href="{{ route('bookings.availability') }}" class="btn btn-outline-info">
                        <i class="fas fa-calendar-check"></i> Check Availability
                    </a>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-body">
                    <form action="{{ route('bookings.store') }}" method="POST" id="bookingForm">
                        @csrf
                        
                        <div class="row">
                            <!-- Booking Details -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Booking Details</h5>
                                
                                <div class="mb-3">
                                    <label for="studio_room_id" class="form-label">Studio Room <span class="text-danger">*</span></label>
                                    <select class="form-select @error('studio_room_id') is-invalid @enderror" id="studio_room_id" name="studio_room_id" required onchange="updateRoomInfo()">
                                        <option value="">Select Studio Room</option>
                                        @foreach($studioRooms as $room)
                                        <option value="{{ $room->id }}" 
                                                data-rate="{{ $room->hourly_rate }}" 
                                                data-capacity="{{ $room->capacity }}"
                                                data-type="{{ $room->type }}"
                                                {{ old('studio_room_id') == $room->id ? 'selected' : '' }}>
                                            {{ $room->name }} - ${{ $room->hourly_rate }}/hr ({{ ucfirst($room->type) }})
                                        </option>
                                        @endforeach
                                    </select>
                                    @error('studio_room_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="booking_date" class="form-label">Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('booking_date') is-invalid @enderror" 
                                                   id="booking_date" name="booking_date" value="{{ old('booking_date', date('Y-m-d')) }}" 
                                                   min="{{ date('Y-m-d') }}" required onchange="checkAvailability()">
                                            @error('booking_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="duration_hours" class="form-label">Duration (hours) <span class="text-danger">*</span></label>
                                            <select class="form-select @error('duration_hours') is-invalid @enderror" id="duration_hours" name="duration_hours" required onchange="calculateCost()">
                                                <option value="">Select Duration</option>
                                                <option value="1" {{ old('duration_hours') == '1' ? 'selected' : '' }}>1 hour</option>
                                                <option value="2" {{ old('duration_hours') == '2' ? 'selected' : '' }}>2 hours</option>
                                                <option value="3" {{ old('duration_hours') == '3' ? 'selected' : '' }}>3 hours</option>
                                                <option value="4" {{ old('duration_hours') == '4' ? 'selected' : '' }}>4 hours</option>
                                                <option value="6" {{ old('duration_hours') == '6' ? 'selected' : '' }}>6 hours</option>
                                                <option value="8" {{ old('duration_hours') == '8' ? 'selected' : '' }}>8 hours</option>
                                                <option value="12" {{ old('duration_hours') == '12' ? 'selected' : '' }}>12 hours</option>
                                            </select>
                                            @error('duration_hours')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="start_time" class="form-label">Start Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control @error('start_time') is-invalid @enderror" 
                                                   id="start_time" name="start_time" value="{{ old('start_time', '09:00') }}" 
                                                   required onchange="calculateEndTime()">
                                            @error('start_time')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="end_time" class="form-label">End Time</label>
                                            <input type="time" class="form-control" id="end_time" name="end_time" readonly>
                                            <div class="form-text">Automatically calculated based on duration</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="project_id" class="form-label">Related Project</label>
                                    <select class="form-select @error('project_id') is-invalid @enderror" id="project_id" name="project_id">
                                        <option value="">Select Project (Optional)</option>
                                        @foreach($projects as $project)
                                        <option value="{{ $project->id }}" {{ old('project_id') == $project->id ? 'selected' : '' }}>
                                            {{ $project->name }} - {{ $project->client->name ?? 'No client' }}
                                        </option>
                                        @endforeach
                                    </select>
                                    @error('project_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="purpose" class="form-label">Session Purpose</label>
                                    <textarea class="form-control @error('purpose') is-invalid @enderror" 
                                              id="purpose" name="purpose" rows="3" 
                                              placeholder="Describe what will be recorded/produced in this session">{{ old('purpose') }}</textarea>
                                    @error('purpose')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Client & Additional Info -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Client Information</h5>
                                
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <select class="form-select @error('client_id') is-invalid @enderror" id="client_id" name="client_id" required>
                                            <option value="">Select Client</option>
                                            @foreach($clients as $client)
                                            <option value="{{ $client->id }}" {{ old('client_id') == $client->id ? 'selected' : '' }}>
                                                {{ $client->name }} - {{ $client->email }}
                                            </option>
                                            @endforeach
                                        </select>
                                        <button type="button" class="btn btn-outline-primary" onclick="openCreateClientModal()">
                                            <i class="fas fa-plus"></i> New
                                        </button>
                                    </div>
                                    @error('client_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">
                                        Search for existing clients or create a new one
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="engineer_id" class="form-label">Sound Engineer</label>
                                    <select class="form-select @error('engineer_id') is-invalid @enderror" id="engineer_id" name="engineer_id">
                                        <option value="">No engineer required</option>
                                        @foreach($engineers as $engineer)
                                        <option value="{{ $engineer->id }}" {{ old('engineer_id') == $engineer->id ? 'selected' : '' }}>
                                            {{ $engineer->name }}
                                        </option>
                                        @endforeach
                                    </select>
                                    @error('engineer_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="session_type" class="form-label">Session Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('session_type') is-invalid @enderror" id="session_type" name="session_type" required>
                                        <option value="">Select Session Type</option>
                                        <option value="recording" {{ old('session_type') == 'recording' ? 'selected' : '' }}>Recording</option>
                                        <option value="mixing" {{ old('session_type') == 'mixing' ? 'selected' : '' }}>Mixing</option>
                                        <option value="mastering" {{ old('session_type') == 'mastering' ? 'selected' : '' }}>Mastering</option>
                                        <option value="rehearsal" {{ old('session_type') == 'rehearsal' ? 'selected' : '' }}>Rehearsal</option>
                                        <option value="other" {{ old('session_type') == 'other' ? 'selected' : '' }}>Other</option>
                                    </select>
                                    @error('session_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="equipment_needed" class="form-label">Additional Equipment Needed</label>
                                    <div id="equipment-container">
                                        <div class="input-group mb-2">
                                            <input type="text" class="form-control" name="equipment_needed[]" placeholder="Enter equipment item">
                                            <button type="button" class="btn btn-outline-danger" onclick="removeEquipment(this)">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEquipment()">
                                        <i class="fas fa-plus"></i> Add Equipment
                                    </button>
                                </div>

                                <div class="mb-3">
                                    <label for="special_requirements" class="form-label">Special Requirements</label>
                                    <textarea class="form-control @error('special_requirements') is-invalid @enderror" 
                                              id="special_requirements" name="special_requirements" rows="3" 
                                              placeholder="Any special setup, dietary requirements, accessibility needs, etc.">{{ old('special_requirements') }}</textarea>
                                    @error('special_requirements')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Cost Calculation -->
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">Cost Breakdown</h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <div class="mb-2">
                                                    <small class="text-muted">Room Rate:</small>
                                                    <div id="room-rate">$0.00/hour</div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-muted">Duration:</small>
                                                    <div id="duration-display">0 hours</div>
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <div class="mb-2">
                                                    <small class="text-muted">Subtotal:</small>
                                                    <div id="subtotal">$0.00</div>
                                                </div>
                                                <div class="mb-2">
                                                    <small class="text-muted">Tax (10%):</small>
                                                    <div id="tax-amount">$0.00</div>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row">
                                            <div class="col-6">
                                                <h5><strong>Total Cost:</strong></h5>
                                            </div>
                                            <div class="col-6">
                                                <h5><strong id="total-cost">$0.00</strong></h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" id="total_cost_input" name="total_cost" value="0">

                                <div class="mt-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_confirmation" 
                                               name="send_confirmation" value="1" {{ old('send_confirmation', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="send_confirmation">
                                            Send confirmation email to client
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('bookings.index') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" name="action" value="draft" class="btn btn-outline-primary">Save as Draft</button>
                                    <button type="submit" name="action" value="confirm" class="btn btn-primary">Create & Confirm</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Availability Check Modal -->
<div class="modal fade" id="availabilityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Room Availability</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="availability-result">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Checking availability...</span>
                        </div>
                        <p class="mt-2">Checking availability...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateRoomInfo() {
    const roomSelect = document.getElementById('studio_room_id');
    const selectedOption = roomSelect.options[roomSelect.selectedIndex];
    
    if (selectedOption.value) {
        const rate = selectedOption.dataset.rate;
        document.getElementById('room-rate').textContent = `$${parseFloat(rate).toFixed(2)}/hour`;
        calculateCost();
    } else {
        document.getElementById('room-rate').textContent = '$0.00/hour';
        resetCost();
    }
}

function calculateEndTime() {
    const startTime = document.getElementById('start_time').value;
    const duration = parseInt(document.getElementById('duration_hours').value) || 0;
    
    if (startTime && duration) {
        const start = new Date(`2000-01-01T${startTime}:00`);
        start.setHours(start.getHours() + duration);
        
        const endTime = start.toTimeString().slice(0, 5);
        document.getElementById('end_time').value = endTime;
        
        calculateCost();
    }
}

function calculateCost() {
    const roomSelect = document.getElementById('studio_room_id');
    const selectedOption = roomSelect.options[roomSelect.selectedIndex];
    const duration = parseInt(document.getElementById('duration_hours').value) || 0;
    
    if (selectedOption.value && duration) {
        const rate = parseFloat(selectedOption.dataset.rate);
        const subtotal = rate * duration;
        const tax = subtotal * 0.1; // 10% tax
        const total = subtotal + tax;
        
        document.getElementById('duration-display').textContent = `${duration} hour${duration !== 1 ? 's' : ''}`;
        document.getElementById('subtotal').textContent = `$${subtotal.toFixed(2)}`;
        document.getElementById('tax-amount').textContent = `$${tax.toFixed(2)}`;
        document.getElementById('total-cost').textContent = `$${total.toFixed(2)}`;
        document.getElementById('total_cost_input').value = total.toFixed(2);
    } else {
        resetCost();
    }
}

function resetCost() {
    document.getElementById('duration-display').textContent = '0 hours';
    document.getElementById('subtotal').textContent = '$0.00';
    document.getElementById('tax-amount').textContent = '$0.00';
    document.getElementById('total-cost').textContent = '$0.00';
    document.getElementById('total_cost_input').value = '0';
}

function addEquipment() {
    const container = document.getElementById('equipment-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="equipment_needed[]" placeholder="Enter equipment item">
        <button type="button" class="btn btn-outline-danger" onclick="removeEquipment(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeEquipment(button) {
    const container = document.getElementById('equipment-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function checkAvailability() {
    const roomId = document.getElementById('studio_room_id').value;
    const date = document.getElementById('booking_date').value;
    const startTime = document.getElementById('start_time').value;
    const duration = document.getElementById('duration_hours').value;
    
    if (roomId && date && startTime && duration) {
        // You can implement AJAX call to check availability here
        console.log('Checking availability for:', { roomId, date, startTime, duration });
    }
}

// Auto-calculate end time when duration changes
document.getElementById('duration_hours').addEventListener('change', function() {
    calculateEndTime();
});

// Form validation
document.getElementById('bookingForm').addEventListener('submit', function(e) {
    const roomId = document.getElementById('studio_room_id').value;
    const clientId = document.getElementById('client_id').value;
    const date = document.getElementById('booking_date').value;
    const startTime = document.getElementById('start_time').value;
    const duration = document.getElementById('duration_hours').value;
    const sessionType = document.getElementById('session_type').value;

    console.log('Form validation:', { roomId, clientId, date, startTime, duration, sessionType });

    if (!roomId || !clientId || !date || !startTime || !duration || !sessionType) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }

    // Check if end time is valid (not past midnight)
    const start = new Date(`2000-01-01T${startTime}:00`);
    const end = new Date(start);
    end.setHours(end.getHours() + parseInt(duration));

    if (end.getDate() !== start.getDate()) {
        e.preventDefault();
        alert('Booking cannot extend past midnight. Please adjust the start time or duration.');
        return false;
    }

    console.log('Form validation passed, submitting...');
});

// Initialize calculations on page load
document.addEventListener('DOMContentLoaded', function() {
    updateRoomInfo();
    calculateEndTime();

    // Initialize client search
    initializeClientSearch();
});

// Client search functionality
function initializeClientSearch() {
    const clientSelect = document.getElementById('client_id');

    // Add search functionality to select
    $(clientSelect).select2({
        placeholder: 'Search for a client...',
        allowClear: true,
        width: '100%',
        ajax: {
            url: '/clients/search',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                return {
                    results: data.data.map(client => ({
                        id: client.id,
                        text: `${client.name} - ${client.email}${client.company ? ` (${client.company})` : ''}`
                    })),
                    pagination: {
                        more: (params.page * 30) < data.total
                    }
                };
            },
            cache: true
        },
        minimumInputLength: 2
    });
}

// Open create client modal
function openCreateClientModal() {
    $('#createClientModal').modal('show');
}

// Close create client modal
function closeCreateClientModal() {
    $('#createClientModal').modal('hide');
    document.getElementById('createClientForm').reset();
}

// Handle create client form submission
function submitCreateClient() {
    const form = document.getElementById('createClientForm');
    const formData = new FormData(form);

    fetch('{{ route("clients.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add new client to select dropdown
            const clientSelect = document.getElementById('client_id');
            const option = new Option(data.client.name + ' - ' + data.client.email, data.client.id, true, true);
            clientSelect.add(option);

            // Trigger change event for select2
            $(clientSelect).trigger('change');

            // Close modal
            closeCreateClientModal();

            // Show success message
            alert('Client created successfully!');
        } else {
            alert('Error creating client: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating client. Please try again.');
    });
}
</script>

<!-- Create Client Modal -->
<div class="modal fade" id="createClientModal" tabindex="-1" aria-labelledby="createClientModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createClientModalLabel">Create New Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createClientForm">
                    @csrf
                    <div class="mb-3">
                        <label for="client_name" class="form-label">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="client_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="client_email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="client_email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="client_phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="client_phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="client_company" class="form-label">Company</label>
                        <input type="text" class="form-control" id="client_company" name="company">
                    </div>
                    <div class="mb-3">
                        <label for="client_type" class="form-label">Type</label>
                        <select class="form-select" id="client_type" name="type">
                            <option value="individual">Individual</option>
                            <option value="artist">Artist</option>
                            <option value="band">Band</option>
                            <option value="label">Record Label</option>
                            <option value="producer">Producer</option>
                            <option value="corporate">Corporate</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeCreateClientModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateClient()">Create Client</button>
            </div>
        </div>
    </div>
</div>
@endpush
