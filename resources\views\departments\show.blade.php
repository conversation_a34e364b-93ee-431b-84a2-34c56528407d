@extends('layouts.app')

@section('title', 'Department - ' . $department->name)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <div class="w-16 h-16 bg-{{ $department->color_code ?? 'blue' }}-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="{{ $department->icon ?? 'fas fa-building' }} text-{{ $department->color_code ?? 'blue' }}-600 text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">{{ $department->name }}</h1>
                        <p class="mt-1 text-sm text-gray-600">{{ $department->description ?: 'Department overview and management' }}</p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('departments.edit', $department) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Edit Department
                    </a>
                    <a href="{{ route('departments.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Back to Departments
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Statistics Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Members</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $department->users->count() }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-check text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Members</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $department->users->where('status', 'active')->count() }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-tag text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Roles</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $department->roles->count() }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Available Now</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $department->users->where('is_available', true)->count() }}</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Department Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Department Details -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Department Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Name</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $department->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Slug</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $department->slug }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $department->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $department->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $department->created_at->format('M j, Y') }}</dd>
                            </div>
                        </dl>
                        
                        @if($department->description)
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $department->description }}</dd>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Team Members -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Team Members ({{ $department->users->count() }})</h3>
                        <a href="{{ route('users.create') }}?department={{ $department->id }}" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm font-medium">
                            Add Member
                        </a>
                    </div>
                    <div class="p-6">
                        @if($department->users->count() > 0)
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                @foreach($department->users as $user)
                                    <div class="flex items-center p-3 border border-gray-200 rounded-lg">
                                        <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                            @if($user->avatar)
                                                <img src="{{ $user->avatar }}" alt="{{ $user->first_name }}" class="w-10 h-10 rounded-full">
                                            @else
                                                <span class="text-sm font-medium text-gray-600">
                                                    {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
                                                </span>
                                            @endif
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm font-medium text-gray-900">
                                                <a href="{{ route('users.show', $user) }}" class="hover:text-blue-600">
                                                    {{ $user->first_name }} {{ $user->last_name }}
                                                </a>
                                            </p>
                                            <p class="text-xs text-gray-500">{{ $user->role->name ?? 'No Role' }}</p>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $user->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ ucfirst($user->status) }}
                                            </span>
                                            @if($user->is_available)
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                    Available
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-users text-gray-400 text-3xl mb-4"></i>
                                <p class="text-gray-500 mb-4">No team members in this department yet</p>
                                <a href="{{ route('users.create') }}?department={{ $department->id }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                    Add First Member
                                </a>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Department Roles -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">Department Roles ({{ $department->roles->count() }})</h3>
                        <a href="{{ route('roles.create') }}?department={{ $department->id }}" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md text-sm font-medium">
                            Create Role
                        </a>
                    </div>
                    <div class="p-6">
                        @if($department->roles->count() > 0)
                            <div class="space-y-3">
                                @foreach($department->roles as $role)
                                    <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-user-tag text-purple-600"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">
                                                    <a href="{{ route('roles.show', $role) }}" class="hover:text-purple-600">
                                                        {{ $role->name }}
                                                    </a>
                                                </p>
                                                <p class="text-xs text-gray-500">Level {{ $role->level }} • {{ $role->users->count() }} users</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            @if($role->is_system_role)
                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    System
                                                </span>
                                            @endif
                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $role->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $role->is_active ? 'Active' : 'Inactive' }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-user-tag text-gray-400 text-3xl mb-4"></i>
                                <p class="text-gray-500 mb-4">No roles defined for this department yet</p>
                                <a href="{{ route('roles.create') }}?department={{ $department->id }}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                    Create First Role
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="{{ route('departments.edit', $department) }}" class="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Edit Department
                        </a>
                        
                        <a href="{{ route('users.create') }}?department={{ $department->id }}" class="w-full bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Add Member
                        </a>
                        
                        <a href="{{ route('roles.create') }}?department={{ $department->id }}" class="w-full bg-purple-100 hover:bg-purple-200 text-purple-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Create Role
                        </a>
                        
                        @if($department->users->count() === 0 && $department->roles->count() === 0)
                            <button onclick="deleteDepartment()" class="w-full bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md text-sm font-medium">
                                Delete Department
                            </button>
                        @endif
                        
                        <a href="{{ route('departments.index') }}" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Back to Departments
                        </a>
                    </div>
                </div>

                <!-- Department Settings -->
                @if($department->settings && count($department->settings) > 0)
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Settings</h3>
                        </div>
                        <div class="p-6 space-y-3">
                            @foreach($department->settings as $key => $value)
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                    <span class="text-sm font-medium text-gray-900">
                                        @if(is_bool($value))
                                            {{ $value ? 'Yes' : 'No' }}
                                        @else
                                            {{ $value }}
                                        @endif
                                    </span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Recent Activity -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
                    </div>
                    <div class="p-6">
                        @if($department->tasks && $department->tasks->count() > 0)
                            <div class="space-y-3">
                                @foreach($department->tasks->take(5) as $task)
                                    <div class="flex items-center text-sm">
                                        <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                                        <div class="flex-1">
                                            <p class="text-gray-900">{{ $task->title }}</p>
                                            <p class="text-gray-500">{{ $task->created_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-history text-gray-400 text-2xl mb-2"></i>
                                <p class="text-sm text-gray-500">No recent activity</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Delete Confirmation Modal -->
@if($department->users->count() === 0 && $department->roles->count() === 0)
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Department</h3>
        <p class="text-sm text-gray-600 mb-6">
            Are you sure you want to delete this department? This action cannot be undone.
        </p>
        <div class="flex justify-end space-x-3">
            <button onclick="closeDeleteModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium">
                Cancel
            </button>
            <form action="{{ route('departments.destroy', $department) }}" method="POST" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>
@endif
@endsection

@push('scripts')
<script>
    function deleteDepartment() {
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.add('flex');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        document.getElementById('deleteModal').classList.remove('flex');
    }
</script>
@endpush
