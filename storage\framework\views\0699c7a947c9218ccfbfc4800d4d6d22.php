<?php $__env->startSection('title', 'Department Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Department Management</h1>
                    <p class="mt-1 text-sm text-gray-600">Organize your studio teams and departments</p>
                </div>
                <div class="flex space-x-3">
                    <a href="<?php echo e(route('departments.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Create Department
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-building text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Departments</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($departments->count()); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Departments</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($departments->where('is_active', true)->count()); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Members</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($departments->sum(function($dept) { return $dept->users->count(); })); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-tie text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Roles</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo e($departments->sum(function($dept) { return $dept->roles->count(); })); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Departments Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__empty_1 = true; $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-<?php echo e($department->color_code ?? 'blue'); ?>-100 rounded-lg flex items-center justify-center">
                                    <i class="<?php echo e($department->icon ?? 'fas fa-building'); ?> text-<?php echo e($department->color_code ?? 'blue'); ?>-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900"><?php echo e($department->name); ?></h3>
                                    <p class="text-sm text-gray-500"><?php echo e($department->slug); ?></p>
                                </div>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($department->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                <?php echo e($department->is_active ? 'Active' : 'Inactive'); ?>

                            </span>
                        </div>

                        <p class="text-gray-600 text-sm mb-6"><?php echo e($department->description ?: 'No description provided'); ?></p>

                        <!-- Department Stats -->
                        <div class="grid grid-cols-3 gap-4 mb-6">
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900"><?php echo e($department->users->count()); ?></p>
                                <p class="text-xs text-gray-500">Members</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900"><?php echo e($department->roles->count()); ?></p>
                                <p class="text-xs text-gray-500">Roles</p>
                            </div>
                            <div class="text-center">
                                <p class="text-2xl font-bold text-gray-900"><?php echo e($department->tasks->count() ?? 0); ?></p>
                                <p class="text-xs text-gray-500">Tasks</p>
                            </div>
                        </div>

                        <!-- Recent Members -->
                        <?php if($department->users->count() > 0): ?>
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Recent Members</h4>
                                <div class="flex -space-x-2">
                                    <?php $__currentLoopData = $department->users->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center" title="<?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?>">
                                            <?php if($user->avatar): ?>
                                                <img src="<?php echo e($user->avatar); ?>" alt="<?php echo e($user->first_name); ?>" class="w-8 h-8 rounded-full">
                                            <?php else: ?>
                                                <span class="text-xs font-medium text-gray-600">
                                                    <?php echo e(substr($user->first_name, 0, 1)); ?><?php echo e(substr($user->last_name, 0, 1)); ?>

                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($department->users->count() > 5): ?>
                                        <div class="w-8 h-8 bg-gray-100 rounded-full border-2 border-white flex items-center justify-center">
                                            <span class="text-xs font-medium text-gray-600">+<?php echo e($department->users->count() - 5); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="flex space-x-2">
                            <a href="<?php echo e(route('departments.show', $department)); ?>" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium text-center">
                                View
                            </a>
                            <a href="<?php echo e(route('departments.edit', $department)); ?>" class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded-md text-sm font-medium text-center">
                                Edit
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <div class="col-span-full">
                    <div class="text-center py-12">
                        <i class="fas fa-building text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No departments found</h3>
                        <p class="text-gray-500 mb-4">Get started by creating your first department.</p>
                        <a href="<?php echo e(route('departments.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Create Department
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Quick Setup Templates -->
        <?php if($departments->isEmpty()): ?>
            <div class="mt-12">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Quick Setup Templates</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createDepartment('Audio Engineering', 'fas fa-headphones', 'blue')">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-headphones text-blue-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Audio Engineering</h4>
                        <p class="text-sm text-gray-600 mt-1">Sound engineers and technicians</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createDepartment('Music Production', 'fas fa-music', 'purple')">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-music text-purple-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Music Production</h4>
                        <p class="text-sm text-gray-600 mt-1">Producers and composers</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createDepartment('Video Production', 'fas fa-video', 'red')">
                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-video text-red-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Video Production</h4>
                        <p class="text-sm text-gray-600 mt-1">Video editors and directors</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createDepartment('Client Services', 'fas fa-users', 'green')">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-users text-green-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Client Services</h4>
                        <p class="text-sm text-gray-600 mt-1">Client relations and support</p>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </main>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function createDepartment(name, icon, color) {
        const url = new URL('<?php echo e(route("departments.create")); ?>');
        url.searchParams.set('name', name);
        url.searchParams.set('icon', icon);
        url.searchParams.set('color', color);
        window.location.href = url.toString();
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\workspace\.php\glitchafrica\resources\views/departments/index.blade.php ENDPATH**/ ?>