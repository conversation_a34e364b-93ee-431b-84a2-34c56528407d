<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('automation_workflows', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('trigger_type', ['event', 'schedule', 'manual'])->default('event');
            $table->json('trigger_config'); // trigger configuration
            $table->json('conditions')->nullable(); // conditions to check
            $table->json('actions'); // actions to execute
            $table->boolean('is_active')->default(true);
            $table->integer('execution_count')->default(0);
            $table->timestamp('last_executed_at')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
            
            $table->index(['trigger_type', 'is_active']);
            $table->index(['created_by', 'last_executed_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('automation_workflows');
    }
};
