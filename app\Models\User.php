<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'password',
        'avatar',
        'department_id',
        'role_id',
        'status',
        'preferences',
        'skills',
        'hourly_rate',
        'is_available',
        'last_active_at'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'preferences' => 'array',
            'skills' => 'array',
            'hourly_rate' => 'decimal:2',
            'is_available' => 'boolean',
            'last_active_at' => 'datetime'
        ];
    }

    // Accessors
    public function getNameAttribute(): string
    {
        return $this->getFullNameAttribute();
    }

    // relationships
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class);
    }

    public function assignedTasks(): HasMany
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }

    public function createdTasks(): HasMany
    {
        return $this->hasMany(Task::class, 'assigned_by');
    }

    public function managedProjects(): HasMany
    {
        return $this->hasMany(Project::class, 'project_manager_id');
    }

    public function projects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class, 'project_users')
                    ->withPivot('role', 'joined_at', 'left_at')
                    ->withTimestamps();
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'engineer_id');
    }

    public function chatMessages(): HasMany
    {
        return $this->hasMany(ChatMessage::class);
    }

    public function chatChannels(): BelongsToMany
    {
        return $this->belongsToMany(ChatChannel::class, 'chat_channel_users', 'user_id', 'channel_id')
                    ->withPivot('role', 'joined_at', 'last_read_at', 'is_muted')
                    ->withTimestamps();
    }

    public function fileUploads(): HasMany
    {
        return $this->hasMany(FileUpload::class, 'uploaded_by');
    }

    // scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    public function scopeByDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    // methods
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function hasPermission(string $permission): bool
    {
        return $this->role?->hasPermission($permission) ?? false;
    }

    public function canAccessProject(Project $project): bool
    {
        // project manager can access
        if ($project->project_manager_id === $this->id) {
            return true;
        }

        // team members can access
        if ($this->projects->contains($project)) {
            return true;
        }

        // admin roles can access all
        return $this->hasPermission('*');
    }

    public function updateLastActive(): void
    {
        $this->update(['last_active_at' => now()]);
    }
}
