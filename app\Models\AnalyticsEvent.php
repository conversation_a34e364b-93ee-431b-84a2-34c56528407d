<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AnalyticsEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_type',
        'event_category',
        'trackable_type',
        'trackable_id',
        'user_id',
        'properties',
        'value',
        'session_id',
        'ip_address',
        'user_agent',
        'occurred_at'
    ];

    protected $casts = [
        'properties' => 'array',
        'value' => 'decimal:2',
        'occurred_at' => 'datetime'
    ];

    // relationships
    public function trackable(): MorphTo
    {
        return $this->morphTo();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // scopes
    public function scopeByType($query, $type)
    {
        return $query->where('event_type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('event_category', $category);
    }

    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeInPeriod($query, $startDate, $endDate)
    {
        return $query->whereBetween('occurred_at', [$startDate, $endDate]);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('occurred_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('occurred_at', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereBetween('occurred_at', [
            now()->startOfMonth(),
            now()->endOfMonth()
        ]);
    }

    // static methods for tracking events
    public static function track(string $eventType, string $category, $trackable = null, array $properties = [], ?float $value = null, ?int $userId = null): self
    {
        return self::create([
            'event_type' => $eventType,
            'event_category' => $category,
            'trackable_type' => $trackable ? get_class($trackable) : null,
            'trackable_id' => $trackable?->id,
            'user_id' => $userId ?? auth()->id(),
            'properties' => $properties,
            'value' => $value,
            'session_id' => session()->getId(),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'occurred_at' => now()
        ]);
    }

    public static function trackProjectEvent(string $eventType, Project $project, array $properties = [], ?float $value = null): self
    {
        return self::track($eventType, 'project', $project, $properties, $value);
    }

    public static function trackTaskEvent(string $eventType, Task $task, array $properties = [], ?float $value = null): self
    {
        return self::track($eventType, 'task', $task, $properties, $value);
    }

    public static function trackBookingEvent(string $eventType, Booking $booking, array $properties = [], ?float $value = null): self
    {
        return self::track($eventType, 'booking', $booking, $properties, $value);
    }

    public static function trackUserEvent(string $eventType, User $user, array $properties = [], ?float $value = null): self
    {
        return self::track($eventType, 'user', $user, $properties, $value);
    }

    // analytics helper methods
    public static function getEventCounts(string $eventType, $startDate = null, $endDate = null): int
    {
        $query = self::where('event_type', $eventType);
        
        if ($startDate && $endDate) {
            $query->whereBetween('occurred_at', [$startDate, $endDate]);
        }
        
        return $query->count();
    }

    public static function getEventValue(string $eventType, $startDate = null, $endDate = null): float
    {
        $query = self::where('event_type', $eventType);
        
        if ($startDate && $endDate) {
            $query->whereBetween('occurred_at', [$startDate, $endDate]);
        }
        
        return $query->sum('value') ?? 0;
    }

    public static function getTopEvents(int $limit = 10, $startDate = null, $endDate = null): array
    {
        $query = self::selectRaw('event_type, COUNT(*) as count')
            ->groupBy('event_type')
            ->orderByDesc('count')
            ->limit($limit);
            
        if ($startDate && $endDate) {
            $query->whereBetween('occurred_at', [$startDate, $endDate]);
        }
        
        return $query->get()->toArray();
    }
}
