<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('project_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('studio_room_id')->constrained()->onDelete('cascade');
            $table->foreignId('engineer_id')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('start_time');
            $table->datetime('end_time');
            $table->enum('status', ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])->default('pending');
            $table->decimal('hourly_rate', 10, 2);
            $table->decimal('total_cost', 10, 2);
            $table->text('notes')->nullable();
            $table->json('equipment_requests')->nullable();
            $table->json('special_requirements')->nullable();
            $table->boolean('requires_setup')->default(false);
            $table->integer('setup_time_minutes')->default(0);
            $table->datetime('confirmed_at')->nullable();
            $table->datetime('checked_in_at')->nullable();
            $table->datetime('checked_out_at')->nullable();
            $table->timestamps();
            
            $table->index(['studio_room_id', 'start_time']);
            $table->index(['client_id', 'status']);
            $table->index(['start_time', 'end_time']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
