<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('file_uploads', function (Blueprint $table) {
            $table->id();
            $table->string('filename');
            $table->string('original_name');
            $table->string('path');
            $table->string('disk')->default('local');
            $table->string('mime_type');
            $table->bigInteger('size'); // file size in bytes
            $table->morphs('uploadable'); // project, task, message, etc.
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->enum('type', ['audio', 'video', 'image', 'document', 'other'])->default('other');
            $table->json('metadata')->nullable(); // file metadata
            $table->boolean('is_processed')->default(false);
            $table->json('versions')->nullable(); // different versions/formats
            $table->timestamps();
            
            // morphs already creates this index
            $table->index(['type', 'uploaded_by']);
            $table->index(['created_at', 'size']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('file_uploads');
    }
};
