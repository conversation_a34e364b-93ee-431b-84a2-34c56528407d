@extends('layouts.dashboard')

@section('title', 'File Manager')

@section('dashboard-content')
<!-- Header -->
<div class="flex justify-between items-center mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">File Manager</h1>
                        <p class="text-gray-600">Organize and manage your studio files</p>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="openUploadModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Upload Files
                        </button>
                        <button onclick="openNewFolderModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            New Folder
                        </button>
                    </div>
                </div>

                <!-- Storage Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Total Files</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($storage_stats['total_files'] ?? 0) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Storage Used</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ formatFileSize($storage_stats['total_size'] ?? 0) }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Recent Uploads</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ $storage_stats['recent_uploads'] ?? 0 }}</p>
                                    <p class="text-sm text-gray-500">Last 7 days</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Storage Usage</p>
                                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($storage_stats['storage_usage']['percentage'] ?? 0, 1) }}%</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                        <div class="bg-orange-500 h-2 rounded-full" style="width: {{ $storage_stats['storage_usage']['percentage'] ?? 0 }}%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Actions -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6">
                        <form method="GET" action="{{ route('file-manager.index') }}" class="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
                            <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
                                <input type="text" 
                                       name="search" 
                                       value="{{ $filters['search'] ?? '' }}"
                                       placeholder="Search files..." 
                                       class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                
                                <select name="type" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">All Types</option>
                                    <option value="audio" {{ ($filters['type'] ?? '') === 'audio' ? 'selected' : '' }}>Audio</option>
                                    <option value="video" {{ ($filters['type'] ?? '') === 'video' ? 'selected' : '' }}>Video</option>
                                    <option value="image" {{ ($filters['type'] ?? '') === 'image' ? 'selected' : '' }}>Images</option>
                                    <option value="document" {{ ($filters['type'] ?? '') === 'document' ? 'selected' : '' }}>Documents</option>
                                    <option value="other" {{ ($filters['type'] ?? '') === 'other' ? 'selected' : '' }}>Other</option>
                                </select>
                                
                                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                    Search
                                </button>
                            </div>

                            <div class="flex items-center space-x-4">
                                <div id="bulk-actions" class="hidden flex space-x-2">
                                    <span class="text-sm text-gray-600" id="selected-count">0 selected</span>
                                    <button type="button" onclick="downloadSelected()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                        Download
                                    </button>
                                    <button type="button" onclick="deleteSelected()" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                        Delete
                                    </button>
                                </div>

                                <div class="flex bg-gray-100 rounded-lg p-1">
                                    <button type="button" onclick="setViewMode('grid')" id="grid-view-btn" class="px-3 py-1 text-sm rounded-md bg-white text-gray-900 shadow-sm">
                                        Grid
                                    </button>
                                    <button type="button" onclick="setViewMode('list')" id="list-view-btn" class="px-3 py-1 text-sm rounded-md text-gray-600 hover:text-gray-900">
                                        List
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Files Display -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <!-- Grid View -->
                        <div id="grid-view" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            @forelse($files['data'] as $file)
                                <div class="border-2 rounded-lg p-4 cursor-pointer hover:bg-gray-50 file-item" 
                                     data-file-id="{{ $file['id'] }}"
                                     onclick="toggleFileSelection({{ $file['id'] }})">
                                    <div class="flex flex-col items-center text-center">
                                        @php
                                            $iconClass = match($file['type']) {
                                                'image' => 'text-green-500',
                                                'audio' => 'text-purple-500',
                                                'video' => 'text-red-500',
                                                'document' => 'text-blue-500',
                                                default => 'text-gray-500'
                                            };
                                        @endphp
                                        
                                        <svg class="w-8 h-8 {{ $iconClass }} mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            @if($file['type'] === 'image')
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            @elseif($file['type'] === 'audio')
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                                            @elseif($file['type'] === 'video')
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                            @elseif($file['type'] === 'document')
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            @else
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                            @endif
                                        </svg>
                                        
                                        <p class="text-sm font-medium text-gray-900 truncate w-full" title="{{ $file['original_name'] }}">
                                            {{ $file['original_name'] }}
                                        </p>
                                        <p class="text-xs text-gray-500 mt-1">
                                            {{ formatFileSize($file['size']) }}
                                        </p>
                                        <p class="text-xs text-gray-400 mt-1">
                                            {{ \Carbon\Carbon::parse($file['created_at'])->format('M j, g:i A') }}
                                        </p>
                                    </div>
                                </div>
                            @empty
                                <div class="col-span-full text-center py-12">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">No files found</h3>
                                    <p class="mt-1 text-sm text-gray-500">Get started by uploading your first file.</p>
                                    <div class="mt-6">
                                        <button onclick="openUploadModal()" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                            Upload Files
                                        </button>
                                    </div>
                                </div>
                            @endforelse
                        </div>

                        <!-- List View -->
                        <div id="list-view" class="hidden overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" id="select-all" onchange="toggleSelectAll()" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Uploaded By</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @forelse($files['data'] as $file)
                                        <tr class="hover:bg-gray-50 file-row" data-file-id="{{ $file['id'] }}">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <input type="checkbox" class="file-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" 
                                                       value="{{ $file['id'] }}" onchange="updateBulkActions()">
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 mr-3">
                                                        @php
                                                            $iconClass = match($file['type']) {
                                                                'image' => 'text-green-500',
                                                                'audio' => 'text-purple-500',
                                                                'video' => 'text-red-500',
                                                                'document' => 'text-blue-500',
                                                                default => 'text-gray-500'
                                                            };
                                                        @endphp
                                                        <svg class="w-6 h-6 {{ $iconClass }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <!-- Same icon logic as grid view -->
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <div class="text-sm font-medium text-gray-900">{{ $file['original_name'] }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 capitalize">{{ $file['type'] }}</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatFileSize($file['size']) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $file['uploaded_by']['first_name'] }} {{ $file['uploaded_by']['last_name'] }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ \Carbon\Carbon::parse($file['created_at'])->format('M j, Y g:i A') }}
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No files found</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        @if($files['last_page'] > 1)
                            <div class="mt-6 flex justify-center">
                                <div class="flex space-x-2">
                                    @for($page = 1; $page <= $files['last_page']; $page++)
                                        <a href="{{ request()->fullUrlWithQuery(['page' => $page]) }}" 
                                           class="px-3 py-2 text-sm rounded-md {{ $page === $files['current_page'] ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300' }}">
                                            {{ $page }}
                                        </a>
                                    @endfor
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Enhanced Upload Modal -->
<div id="upload-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-medium text-gray-900">Upload Files</h3>
            <button onclick="closeUploadModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="upload-form" enctype="multipart/form-data">
            @csrf
            <!-- Drag and Drop Area -->
            <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-indigo-500 transition-colors duration-200 mb-6">
                <div class="space-y-4">
                    <div class="mx-auto w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-cloud-upload-alt text-indigo-600 text-2xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-medium text-gray-900">Drop files here to upload</h4>
                        <p class="text-gray-600">or click to browse files</p>
                    </div>
                    <div class="flex justify-center">
                        <button type="button" onclick="document.getElementById('fileInput').click()"
                                class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium">
                            Choose Files
                        </button>
                    </div>
                    <p class="text-sm text-gray-500">
                        Supports: Audio, Video, Images, Documents (Max 500MB per file)
                    </p>
                </div>
                <input type="file" id="fileInput" multiple class="hidden" accept="*/*">
            </div>

            <!-- File List -->
            <div id="fileList" class="space-y-3 mb-6 hidden">
                <h4 class="text-lg font-medium text-gray-900">Selected Files</h4>
                <div id="selectedFiles" class="space-y-2 max-h-60 overflow-y-auto">
                    <!-- Selected files will be displayed here -->
                </div>
            </div>

            <!-- Upload Progress -->
            <div id="uploadProgress" class="mb-6 hidden">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">Uploading files...</span>
                    <span id="progressText" class="text-sm text-gray-500">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="bg-indigo-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
            </div>

            <!-- Upload Options -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Folder (Optional)</label>
                    <input type="text" name="folder" placeholder="e.g., Audio Files, Project Assets"
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">File Type</label>
                    <select name="file_type" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="">Auto-detect</option>
                        <option value="audio">Audio</option>
                        <option value="video">Video</option>
                        <option value="image">Image</option>
                        <option value="document">Document</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeUploadModal()"
                        class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium">
                    Cancel
                </button>
                <button type="submit" id="uploadBtn" disabled
                        class="bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium">
                    Upload Files
                </button>
            </div>
        </form>
    </div>
</div>

<!-- New Folder Modal -->
<div id="new-folder-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Create New Folder</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeNewFolderModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <label for="folder-name-input" class="block text-sm font-medium text-gray-700 mb-2">Folder Name</label>
                <input type="text" id="folder-name-input" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" placeholder="Enter folder name" onkeypress="if(event.key==='Enter') createFolderFromModal()">
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors" onclick="closeNewFolderModal()">Cancel</button>
                <button type="button" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors" onclick="createFolderFromModal()">
                    <i class="fas fa-folder-plus mr-2"></i>Create Folder
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    let selectedFiles = [];
    let viewMode = 'grid';
    let uploadQueue = [];

    $(document).ready(function() {
        initializeFileUpload();
    });

    function initializeFileUpload() {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');

        // Drag and drop handlers
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragleave', handleDragLeave);
        dropZone.addEventListener('drop', handleDrop);

        // File input change handler
        fileInput.addEventListener('change', handleFileSelect);

        // Form submission handler
        document.getElementById('upload-form').addEventListener('submit', handleUploadSubmit);
    }

    function handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.add('border-indigo-500', 'bg-indigo-50');
    }

    function handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('border-indigo-500', 'bg-indigo-50');
    }

    function handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        e.currentTarget.classList.remove('border-indigo-500', 'bg-indigo-50');

        const files = Array.from(e.dataTransfer.files);
        addFilesToQueue(files);
    }

    function handleFileSelect(e) {
        const files = Array.from(e.target.files);
        addFilesToQueue(files);
    }

    function addFilesToQueue(files) {
        // Filter out files that are too large or invalid
        const validFiles = files.filter(file => {
            if (file.size > 500 * 1024 * 1024) { // 500MB limit
                showNotification(`File "${file.name}" is too large (max 500MB)`, 'error');
                return false;
            }
            return true;
        });

        uploadQueue = [...uploadQueue, ...validFiles];
        displaySelectedFiles();
        updateUploadButton();
    }

    function displaySelectedFiles() {
        const fileList = document.getElementById('fileList');
        const selectedFilesContainer = document.getElementById('selectedFiles');

        if (uploadQueue.length === 0) {
            fileList.classList.add('hidden');
            return;
        }

        fileList.classList.remove('hidden');

        const filesHtml = uploadQueue.map((file, index) => `
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <i class="${getFileIcon(file.type)} text-gray-600"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">${file.name}</p>
                        <p class="text-xs text-gray-500">${formatFileSize(file.size)} • ${file.type || 'Unknown type'}</p>
                    </div>
                </div>
                <button onclick="removeFileFromQueue(${index})" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `).join('');

        selectedFilesContainer.innerHTML = filesHtml;
    }

    function removeFileFromQueue(index) {
        uploadQueue.splice(index, 1);
        displaySelectedFiles();
        updateUploadButton();
    }

    function updateUploadButton() {
        const uploadBtn = document.getElementById('uploadBtn');
        uploadBtn.disabled = uploadQueue.length === 0;
    }

    function getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return 'fas fa-image';
        if (mimeType.startsWith('video/')) return 'fas fa-video';
        if (mimeType.startsWith('audio/')) return 'fas fa-music';
        if (mimeType.includes('pdf')) return 'fas fa-file-pdf';
        if (mimeType.includes('word') || mimeType.includes('document')) return 'fas fa-file-word';
        if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return 'fas fa-file-excel';
        if (mimeType.includes('powerpoint') || mimeType.includes('presentation')) return 'fas fa-file-powerpoint';
        return 'fas fa-file';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function handleUploadSubmit(e) {
        e.preventDefault();

        if (uploadQueue.length === 0) {
            showNotification('Please select files to upload', 'warning');
            return;
        }

        uploadFiles();
    }

    function uploadFiles() {
        const formData = new FormData();
        const form = document.getElementById('upload-form');

        // Add files to form data
        uploadQueue.forEach(file => {
            formData.append('files[]', file);
        });

        // Add other form fields
        const folder = form.querySelector('[name="folder"]').value;
        const fileType = form.querySelector('[name="file_type"]').value;

        if (folder) formData.append('folder', folder);
        if (fileType) formData.append('type', fileType);

        formData.append('uploadable_type', 'App\\Models\\Project');
        formData.append('uploadable_id', 1); // Default project

        // Show progress
        showUploadProgress();

        // Upload with progress tracking
        const xhr = new XMLHttpRequest();

        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                updateUploadProgress(percentComplete);
            }
        });

        xhr.addEventListener('load', function() {
            hideUploadProgress();

            if (xhr.status === 200) {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    showNotification('Files uploaded successfully!', 'success');
                    closeUploadModal();
                    window.location.reload();
                } else {
                    showNotification('Upload failed: ' + response.message, 'error');
                }
            } else {
                showNotification('Upload failed. Please try again.', 'error');
            }
        });

        xhr.addEventListener('error', function() {
            hideUploadProgress();
            showNotification('Upload failed. Please check your connection.', 'error');
        });

        xhr.open('POST', '/api/file-manager/upload-multiple');
        xhr.setRequestHeader('X-CSRF-TOKEN', window.Laravel.csrfToken);
        xhr.send(formData);
    }

    function showUploadProgress() {
        document.getElementById('uploadProgress').classList.remove('hidden');
        document.getElementById('uploadBtn').disabled = true;
    }

    function updateUploadProgress(percent) {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        progressBar.style.width = percent + '%';
        progressText.textContent = Math.round(percent) + '%';
    }

    function hideUploadProgress() {
        document.getElementById('uploadProgress').classList.add('hidden');
        document.getElementById('uploadBtn').disabled = false;
    }

    function setViewMode(mode) {
        viewMode = mode;
        const gridView = document.getElementById('grid-view');
        const listView = document.getElementById('list-view');
        const gridBtn = document.getElementById('grid-view-btn');
        const listBtn = document.getElementById('list-view-btn');

        if (mode === 'grid') {
            gridView.classList.remove('hidden');
            listView.classList.add('hidden');
            gridBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
            gridBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
            listBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
            listBtn.classList.add('text-gray-600', 'hover:text-gray-900');
        } else {
            gridView.classList.add('hidden');
            listView.classList.remove('hidden');
            listBtn.classList.add('bg-white', 'text-gray-900', 'shadow-sm');
            listBtn.classList.remove('text-gray-600', 'hover:text-gray-900');
            gridBtn.classList.remove('bg-white', 'text-gray-900', 'shadow-sm');
            gridBtn.classList.add('text-gray-600', 'hover:text-gray-900');
        }
    }

    function toggleFileSelection(fileId) {
        const index = selectedFiles.indexOf(fileId);
        if (index > -1) {
            selectedFiles.splice(index, 1);
        } else {
            selectedFiles.push(fileId);
        }
        updateBulkActions();
        updateFileSelection();
    }

    function updateBulkActions() {
        const checkboxes = document.querySelectorAll('.file-checkbox:checked');
        selectedFiles = Array.from(checkboxes).map(cb => parseInt(cb.value));
        
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');
        
        if (selectedFiles.length > 0) {
            bulkActions.classList.remove('hidden');
            bulkActions.classList.add('flex');
            selectedCount.textContent = `${selectedFiles.length} selected`;
        } else {
            bulkActions.classList.add('hidden');
            bulkActions.classList.remove('flex');
        }
    }

    function updateFileSelection() {
        // Update grid view selection
        document.querySelectorAll('.file-item').forEach(item => {
            const fileId = parseInt(item.dataset.fileId);
            if (selectedFiles.includes(fileId)) {
                item.classList.add('border-blue-500', 'bg-blue-50');
            } else {
                item.classList.remove('border-blue-500', 'bg-blue-50');
            }
        });

        // Update list view checkboxes
        document.querySelectorAll('.file-checkbox').forEach(checkbox => {
            const fileId = parseInt(checkbox.value);
            checkbox.checked = selectedFiles.includes(fileId);
        });
    }

    function toggleSelectAll() {
        const selectAll = document.getElementById('select-all');
        const checkboxes = document.querySelectorAll('.file-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
        
        updateBulkActions();
    }

    function downloadSelected() {
        if (selectedFiles.length === 0) return;
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/api/file-manager/download-zip';
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = window.Laravel.csrfToken;
        form.appendChild(csrfToken);
        
        selectedFiles.forEach(fileId => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'file_ids[]';
            input.value = fileId;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    function deleteSelected() {
        if (selectedFiles.length === 0) return;
        
        if (!confirm('Are you sure you want to delete the selected files?')) return;
        
        fetch('/api/file-manager/bulk-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.Laravel.csrfToken
            },
            body: JSON.stringify({
                file_ids: selectedFiles
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Files deleted successfully', 'success');
                window.location.reload();
            } else {
                showNotification('Error deleting files', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting files', 'error');
        });
    }

    function openUploadModal() {
        document.getElementById('upload-modal').classList.remove('hidden');
    }

    function closeUploadModal() {
        document.getElementById('upload-modal').classList.add('hidden');
    }

    function openNewFolderModal() {
        document.getElementById('new-folder-modal').classList.remove('hidden');
        document.getElementById('folder-name-input').focus();
    }

    function closeNewFolderModal() {
        document.getElementById('new-folder-modal').classList.add('hidden');
        document.getElementById('folder-name-input').value = '';
    }

    function createFolderFromModal() {
        const folderName = document.getElementById('folder-name-input').value.trim();
        if (folderName) {
            createFolder(folderName);
            closeNewFolderModal();
        }
    }

    function createFolder(name) {
        fetch('/api/file-manager/create-folder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.Laravel.csrfToken
            },
            body: JSON.stringify({
                name: name,
                uploadable_type: 'App\\Models\\Project',
                uploadable_id: 1 // Default project
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Folder created successfully', 'success');
                window.location.reload();
            } else {
                showNotification('Error creating folder', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error creating folder', 'error');
        });
    }

    // Upload form handler
    document.getElementById('upload-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        formData.append('uploadable_type', 'App\\Models\\Project');
        formData.append('uploadable_id', 1); // Default project
        
        showLoading();
        
        fetch('/api/file-manager/upload-multiple', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': window.Laravel.csrfToken
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification('Files uploaded successfully', 'success');
                closeUploadModal();
                window.location.reload();
            } else {
                showNotification('Error uploading files', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error:', error);
            showNotification('Error uploading files', 'error');
        });
    });
</script>
@endpush

@php
function formatFileSize($bytes) {
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if ($bytes == 0) return '0 Bytes';
    $i = floor(log($bytes) / log(1024));
    return round($bytes / pow(1024, $i), 2) . ' ' . $sizes[$i];
}
@endphp
