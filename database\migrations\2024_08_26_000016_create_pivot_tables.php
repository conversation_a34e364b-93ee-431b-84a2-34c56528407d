<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Project team members
        Schema::create('project_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('role')->nullable(); // role in this project
            $table->timestamp('joined_at')->useCurrent();
            $table->timestamp('left_at')->nullable();
            
            $table->unique(['project_id', 'user_id']);
            $table->index(['user_id', 'joined_at']);
        });

        // Chat channel members
        Schema::create('chat_channel_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('channel_id')->constrained('chat_channels')->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('role', ['member', 'admin', 'moderator'])->default('member');
            $table->timestamp('joined_at')->useCurrent();
            $table->timestamp('last_read_at')->nullable();
            $table->boolean('is_muted')->default(false);
            
            $table->unique(['channel_id', 'user_id']);
            $table->index(['user_id', 'last_read_at']);
        });

        // Task dependencies
        Schema::create('task_dependencies', function (Blueprint $table) {
            $table->id();
            $table->foreignId('task_id')->constrained()->onDelete('cascade');
            $table->foreignId('depends_on_task_id')->constrained('tasks')->onDelete('cascade');
            $table->enum('type', ['finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish'])->default('finish_to_start');
            $table->timestamps();
            
            $table->unique(['task_id', 'depends_on_task_id']);
            $table->index(['depends_on_task_id', 'type']);
        });

        // Equipment assignments
        Schema::create('equipment_bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->string('equipment_name');
            $table->string('equipment_type');
            $table->integer('quantity')->default(1);
            $table->decimal('additional_cost', 10, 2)->default(0);
            $table->text('notes')->nullable();
            
            $table->index(['booking_id', 'equipment_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('equipment_bookings');
        Schema::dropIfExists('task_dependencies');
        Schema::dropIfExists('chat_channel_users');
        Schema::dropIfExists('project_users');
    }
};
