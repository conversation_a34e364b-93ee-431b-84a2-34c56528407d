<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Client;
use App\Models\Workflow;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ProjectController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        
        $query = Project::with(['client', 'projectManager', 'teamMembers'])
            ->orderBy('created_at', 'desc');

        // filter based on user permissions
        if (!$user->hasPermission('*')) {
            $query->where(function ($q) use ($user) {
                $q->where('project_manager_id', $user->id)
                  ->orWhereHas('teamMembers', function ($teamQuery) use ($user) {
                      $teamQuery->where('user_id', $user->id);
                  });
            });
        }

        // apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $projects = $query->paginate($request->get('per_page', 15));

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => [
                    'projects' => $projects->items(),
                    'pagination' => [
                        'current_page' => $projects->currentPage(),
                        'last_page' => $projects->lastPage(),
                        'per_page' => $projects->perPage(),
                        'total' => $projects->total()
                    ]
                ]
            ]);
        }

        // Get clients for filter dropdown
        $clients = Client::orderBy('name')->get();

        // Calculate summary statistics
        $summary = [
            'total' => Project::count(),
            'in_progress' => Project::where('status', 'active')->count(),
            'completed' => Project::where('status', 'completed')->count(),
            'total_value' => Project::sum('budget')
        ];

        // Return JSON for AJAX requests
        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'projects' => $projects->items(),
                    'pagination' => [
                        'current_page' => $projects->currentPage(),
                        'last_page' => $projects->lastPage(),
                        'per_page' => $projects->perPage(),
                        'total' => $projects->total()
                    ],
                    'summary' => $summary
                ]
            ]);
        }

        // Return view for web requests
        return view('projects.index', compact('projects', 'clients', 'summary'));
    }

    public function create()
    {
        $clients = Client::orderBy('name')->get();
        $users = \App\Models\User::orderBy('first_name')->get();

        return view('projects.create', compact('clients', 'users'));
    }

    public function edit(Project $project)
    {
        $clients = Client::orderBy('name')->get();
        $users = \App\Models\User::orderBy('first_name')->get();

        return view('projects.edit', compact('project', 'clients', 'users'));
    }



    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:music,podcast,video,live_session,mixing,mastering,other',
            'client_id' => 'required|exists:clients,id',
            'project_manager_id' => 'nullable|exists:users,id',
            'budget' => 'nullable|numeric|min:0',
            'start_date' => 'nullable|date',
            'deadline' => 'nullable|date|after:start_date',
            'priority' => 'in:low,medium,high,urgent',
            'requirements' => 'nullable|array',
            'deliverables' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['slug'] = Str::slug($data['title']) . '-' . time();
        $data['status'] = 'draft';
        $data['project_manager_id'] = $data['project_manager_id'] ?? $request->user()->id;

        $project = Project::create($data);

        // create default workflow tasks
        $workflow = Workflow::forProjectType($data['type'])->default()->first();
        if ($workflow) {
            $workflow->createTasksForProject($project);
        }

        // track project creation
        AnalyticsEvent::trackProjectEvent('project_created', $project);

        return response()->json([
            'success' => true,
            'message' => 'Project created successfully',
            'data' => ['project' => $project->load(['client', 'projectManager'])]
        ], 201);
    }

    public function show(Request $request, Project $project)
    {
        $user = $request->user();

        if (!$user->canAccessProject($project)) {
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            abort(403, 'Access denied');
        }

        $project->load([
            'client',
            'projectManager',
            'teamMembers',
            'tasks.assignedUser',
            'tasks.department',
            'bookings.studioRoom',
            'fileUploads'
        ]);

        // Return JSON for API requests, view for web requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => ['project' => $project]
            ]);
        }

        return view('projects.show', compact('project'));
    }

    public function update(Request $request, Project $project)
    {
        $user = $request->user();

        if (!$user->canAccessProject($project)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'status' => 'sometimes|in:draft,active,on_hold,completed,cancelled',
            'priority' => 'sometimes|in:low,medium,high,urgent',
            'budget' => 'nullable|numeric|min:0',
            'start_date' => 'nullable|date',
            'deadline' => 'nullable|date',
            'requirements' => 'nullable|array',
            'deliverables' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // update slug if title changed
        if (isset($data['title'])) {
            $data['slug'] = Str::slug($data['title']) . '-' . $project->id;
        }

        // set completion date if status changed to completed
        if (isset($data['status']) && $data['status'] === 'completed' && $project->status !== 'completed') {
            $data['completed_at'] = now();
            $data['progress_percentage'] = 100;
        }

        $project->update($data);

        // track project update
        AnalyticsEvent::trackProjectEvent('project_updated', $project, $data);

        return response()->json([
            'success' => true,
            'message' => 'Project updated successfully',
            'data' => ['project' => $project->fresh(['client', 'projectManager'])]
        ]);
    }

    public function destroy(Request $request, Project $project)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $project->project_manager_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // track project deletion
        AnalyticsEvent::trackProjectEvent('project_deleted', $project);

        $project->delete();

        return response()->json([
            'success' => true,
            'message' => 'Project deleted successfully'
        ]);
    }

    public function addTeamMember(Request $request, Project $project)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'role' => 'nullable|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $userId = $request->user_id;
        $role = $request->role ?? 'member';

        // check if user is already a team member
        if ($project->teamMembers()->where('user_id', $userId)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'User is already a team member'
            ], 422);
        }

        $project->teamMembers()->attach($userId, [
            'role' => $role,
            'joined_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Team member added successfully'
        ]);
    }

    public function removeTeamMember(Request $request, Project $project, $userId)
    {
        if (!$project->teamMembers()->where('user_id', $userId)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'User is not a team member'
            ], 422);
        }

        $project->teamMembers()->updateExistingPivot($userId, [
            'left_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Team member removed successfully'
        ]);
    }

    public function updateProgress(Request $request, Project $project)
    {
        $user = $request->user();

        if (!$user->canAccessProject($project)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // If progress value is provided, use it; otherwise calculate from tasks
        if ($request->has('progress')) {
            $request->validate([
                'progress' => 'required|integer|min:0|max:100',
                'notes' => 'nullable|string|max:1000'
            ]);

            $project->update([
                'progress_percentage' => $request->progress
            ]);

            // Log progress update if notes provided
            if ($request->notes) {
                // You can add a project_logs table to track progress updates
                // ProjectLog::create([
                //     'project_id' => $project->id,
                //     'user_id' => auth()->id(),
                //     'action' => 'progress_update',
                //     'notes' => $request->notes,
                //     'data' => ['progress' => $request->progress]
                // ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Progress updated successfully',
                'data' => ['progress' => $project->fresh()->progress_percentage]
            ]);
        } else {
            // Calculate progress from tasks
            $project->updateProgress();

            return response()->json([
                'success' => true,
                'message' => 'Project progress updated',
                'data' => [
                    'progress_percentage' => $project->fresh()->progress_percentage
                ]
            ]);
        }
    }
}
