@extends('layouts.dashboard')

@section('title', 'Balance Sheet')

@section('dashboard-content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Balance Sheet</h1>
            <p class="text-gray-600">As of {{ \Carbon\Carbon::parse($asOfDate)->format('M d, Y') }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('accounting.reports.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Back to Reports
            </a>
            <button onclick="openExportModal('balance-sheet')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                <i class="fas fa-download mr-2"></i>Export
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Assets -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Assets</h2>
            </div>
            <div class="px-6 py-4">
                <!-- Current Assets -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-3">Current Assets</h3>
                    <div class="space-y-2 ml-4">
                        @foreach($balanceSheet['assets']['current_assets'] as $key => $value)
                            @if($key !== 'total')
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700">{{ ucwords(str_replace('_', ' ', $key)) }}</span>
                                <span class="font-medium">${{ number_format($value, 2) }}</span>
                            </div>
                            @endif
                        @endforeach
                        <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between items-center font-semibold">
                                <span>Total Current Assets</span>
                                <span>${{ number_format($balanceSheet['assets']['current_assets']['total'], 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fixed Assets -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-3">Fixed Assets</h3>
                    <div class="space-y-2 ml-4">
                        @foreach($balanceSheet['assets']['fixed_assets'] as $key => $value)
                            @if($key !== 'total')
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700">{{ ucwords(str_replace('_', ' ', $key)) }}</span>
                                <span class="font-medium">${{ number_format($value, 2) }}</span>
                            </div>
                            @endif
                        @endforeach
                        <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between items-center font-semibold">
                                <span>Total Fixed Assets</span>
                                <span>${{ number_format($balanceSheet['assets']['fixed_assets']['total'], 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Assets -->
                <div class="border-t-2 pt-4">
                    <div class="flex justify-between items-center text-xl font-bold">
                        <span class="text-gray-900">Total Assets</span>
                        <span class="text-blue-600">${{ number_format($balanceSheet['assets']['total_assets'], 2) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Liabilities & Equity -->
        <div class="bg-white rounded-lg shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-gray-900">Liabilities & Equity</h2>
            </div>
            <div class="px-6 py-4">
                <!-- Current Liabilities -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-3">Current Liabilities</h3>
                    <div class="space-y-2 ml-4">
                        @foreach($balanceSheet['liabilities']['current_liabilities'] as $key => $value)
                            @if($key !== 'total')
                            <div class="flex justify-between items-center">
                                <span class="text-gray-700">{{ ucwords(str_replace('_', ' ', $key)) }}</span>
                                <span class="font-medium">${{ number_format($value, 2) }}</span>
                            </div>
                            @endif
                        @endforeach
                        <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between items-center font-semibold">
                                <span>Total Current Liabilities</span>
                                <span>${{ number_format($balanceSheet['liabilities']['current_liabilities']['total'], 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Liabilities -->
                <div class="mb-6">
                    <div class="flex justify-between items-center font-semibold text-lg">
                        <span class="text-gray-900">Total Liabilities</span>
                        <span class="text-red-600">${{ number_format($balanceSheet['liabilities']['total_liabilities'], 2) }}</span>
                    </div>
                </div>

                <!-- Equity -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-800 mb-3">Equity</h3>
                    <div class="space-y-2 ml-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-700">Owner's Equity</span>
                            <span class="font-medium">${{ number_format($balanceSheet['equity']['total_equity'], 2) }}</span>
                        </div>
                        <div class="border-t pt-2 mt-2">
                            <div class="flex justify-between items-center font-semibold">
                                <span>Total Equity</span>
                                <span>${{ number_format($balanceSheet['equity']['total_equity'], 2) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Liabilities & Equity -->
                <div class="border-t-2 pt-4">
                    <div class="flex justify-between items-center text-xl font-bold">
                        <span class="text-gray-900">Total Liabilities & Equity</span>
                        <span class="text-blue-600">${{ number_format($balanceSheet['liabilities']['total_liabilities'] + $balanceSheet['equity']['total_equity'], 2) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Export Report</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeExportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Export Format</label>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="pdf" class="mr-2" checked>
                        <i class="fas fa-file-pdf text-red-500 mr-2"></i>
                        PDF Document
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="csv" class="mr-2">
                        <i class="fas fa-file-csv text-green-500 mr-2"></i>
                        CSV Spreadsheet
                    </label>
                </div>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors" onclick="closeExportModal()">Cancel</button>
                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" onclick="exportReport()">
                    <i class="fas fa-download mr-2"></i>Export
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let currentReportType = 'balance-sheet';

function openExportModal(reportType) {
    currentReportType = reportType;
    document.getElementById('exportModal').classList.remove('hidden');
}

function closeExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

function exportReport() {
    const format = document.querySelector('input[name="export_format"]:checked').value;
    const asOfDate = '{{ \Carbon\Carbon::parse($asOfDate)->format("Y-m-d") }}';
    
    // Show loading state
    const exportBtn = document.querySelector('#exportModal button[onclick="exportReport()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Exporting...';
    exportBtn.disabled = true;
    
    // Create download URL
    const params = new URLSearchParams({
        report_type: currentReportType,
        format: format,
        start_date: asOfDate,
        end_date: asOfDate
    });
    
    // Create a temporary link to trigger download
    const downloadUrl = `/accounting/reports/export?${params.toString()}`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${currentReportType}-report-${asOfDate}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Reset button state and close modal
    setTimeout(() => {
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
        closeExportModal();
    }, 1000);
}
</script>
@endpush
@endsection
