# MySQL XAMPP Fix Guide

## Quick Fix Steps:

### 1. Stop All XAMPP Services
- Open XAMPP Control Panel
- Stop Apache and MySQL if running
- Close XAMPP completely

### 2. Check for Port Conflicts
```bash
# Open Command Prompt as Administrator
netstat -ano | findstr :3306
```
If port 3306 is in use by another process, kill it:
```bash
taskkill /PID [PID_NUMBER] /F
```

### 3. Fix MySQL Data Directory
1. Navigate to `C:\xampp\mysql\data\`
2. Look for files: `ibdata1`, `ib_logfile0`, `ib_logfile1`
3. If corrupted, backup and delete them
4. Restart MySQL

### 4. Alternative: Reset MySQL Data
```bash
# Backup your databases first!
# Navigate to C:\xampp\mysql\data\
# Copy your database folders (not mysql, performance_schema, phpmyadmin)

# Delete these files:
- ibdata1
- ib_logfile0  
- ib_logfile1
- auto.cnf

# Restart XAMPP MySQL
```

### 5. Use Alternative Database (SQLite)
If MySQL continues to fail, update `.env`:
```env
DB_CONNECTION=sqlite
DB_DATABASE=database/database.sqlite
# Comment out MySQL settings
```

Create SQLite database:
```bash
touch database/database.sqlite
php artisan migrate
php artisan db:seed
```

### 6. Docker Alternative (Recommended)
```bash
# Install Docker Desktop
# Create docker-compose.yml in project root:

version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: glitchafrica
      MYSQL_USER: glitch
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:

# Run: docker-compose up -d
```

### 7. Check Windows Event Viewer
1. Press `Win + R`, type `eventvwr.msc`
2. Navigate to Windows Logs > Application
3. Look for MySQL errors
4. Common issues:
   - Insufficient disk space
   - Corrupted data files
   - Permission issues

### 8. Reinstall MySQL Service
```bash
# Open Command Prompt as Administrator
cd C:\xampp\mysql\bin
mysqld --remove
mysqld --install
net start mysql
```

## Quick Laravel Setup with SQLite (Fastest Solution)

1. Update `.env`:
```env
DB_CONNECTION=sqlite
DB_DATABASE=database/database.sqlite
```

2. Create database file:
```bash
touch database/database.sqlite
```

3. Run migrations:
```bash
php artisan migrate
php artisan db:seed
```

4. Start Laravel:
```bash
php artisan serve
```

This will get you running immediately while you fix MySQL separately.
