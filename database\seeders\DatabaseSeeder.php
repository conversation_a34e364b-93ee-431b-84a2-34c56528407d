<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Run department and role seeders first
        $this->call([
            DepartmentSeeder::class,
            RoleSeeder::class,
            StudioRoomSeeder::class,
        ]);

        // Create default admin user
        DB::table('users')->insert([
            'first_name' => 'System',
            'last_name' => 'Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'department_id' => 1, // Administration
            'role_id' => 1, // Super Admin
            'status' => 'active',
            'is_available' => true,
            'email_verified_at' => now(),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        // Create sample users for different departments
        $sampleUsers = [
            [
                'first_name' => 'Best',
                'last_name' => 'Amakhian',
                'email' => '<EMAIL>',
                'department_id' => 1,
                'role_id' => 2, // Studio Manager
                'hourly_rate' => 100.00
            ],
            [
                'first_name' => 'Gift',
                'last_name' => 'Israel',
                'email' => '<EMAIL>',
                'department_id' => 2,
                'role_id' => 3, // Project Manager
                'hourly_rate' => 80.00
            ],
            [
                'first_name' => 'Tosin',
                'last_name' => 'Akinbo',
                'email' => '<EMAIL>',
                'department_id' => 8,
                'role_id' => 3, // Project Manager
                'hourly_rate' => 85.00
            ],
            [
                'first_name' => 'Emmanuel',
                'last_name' => 'Okafor',
                'email' => '<EMAIL>',
                'department_id' => 3,
                'role_id' => 5, // Lead Audio Engineer
                'hourly_rate' => 90.00
            ],
            [
                'first_name' => 'Chioma',
                'last_name' => 'Adebayo',
                'email' => '<EMAIL>',
                'department_id' => 4,
                'role_id' => 8, // Music Producer
                'hourly_rate' => 95.00
            ],
            [
                'first_name' => 'Kemi',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'department_id' => 7,
                'role_id' => 12, // Client Manager
                'hourly_rate' => 70.00
            ]
        ];

        foreach ($sampleUsers as $user) {
            DB::table('users')->insert(array_merge($user, [
                'password' => Hash::make('password'),
                'status' => 'active',
                'is_available' => true,
                'email_verified_at' => now(),
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }

        // Create sample clients
        $sampleClients = [
            [
                'name' => 'Rema',
                'email' => '<EMAIL>',
                'phone' => '+***********-5678',
                'company' => 'Mavin Records',
                'type' => 'artist',
                'bio' => 'Afrobeats superstar and global sensation',
                'total_spent' => 25000.00,
                'total_projects' => 8,
                'rating' => 4.9
            ],
            [
                'name' => 'Asake',
                'email' => '<EMAIL>',
                'phone' => '+***********-6789',
                'company' => 'YBNL Nation',
                'type' => 'artist',
                'bio' => 'Rising Afrobeats star',
                'total_spent' => 18000.00,
                'total_projects' => 6,
                'rating' => 4.8
            ],
            [
                'name' => 'Coca-Cola Nigeria',
                'email' => '<EMAIL>',
                'phone' => '+***********-7890',
                'company' => 'Coca-Cola Nigeria',
                'type' => 'brand',
                'bio' => 'Leading beverage brand in Nigeria',
                'total_spent' => 45000.00,
                'total_projects' => 12,
                'rating' => 4.7
            ]
        ];

        foreach ($sampleClients as $client) {
            DB::table('clients')->insert(array_merge($client, [
                'status' => 'active',
                'last_project_at' => now()->subDays(rand(1, 30)),
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }

        // Create default chat channels
        $channels = [
            [
                'name' => 'General',
                'slug' => 'general',
                'description' => 'General studio discussions',
                'type' => 'general',
                'created_by' => 1
            ],
            [
                'name' => 'Audio Engineering',
                'slug' => 'audio-engineering',
                'description' => 'Audio engineering team discussions',
                'type' => 'department',
                'department_id' => 3,
                'created_by' => 1
            ],
            [
                'name' => 'Project Management',
                'slug' => 'project-management',
                'description' => 'Project management discussions',
                'type' => 'department',
                'department_id' => 2,
                'created_by' => 1
            ]
        ];

        foreach ($channels as $channel) {
            DB::table('chat_channels')->insert(array_merge($channel, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }

        // Run workflow seeder after users are created
        $this->call([
            WorkflowSeeder::class,
        ]);
    }
}
