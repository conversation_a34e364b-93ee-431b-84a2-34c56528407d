<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['music', 'podcast', 'video', 'live_session', 'mixing', 'mastering', 'other'])->default('music');
            $table->enum('status', ['draft', 'active', 'on_hold', 'completed', 'cancelled'])->default('draft');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('project_manager_id')->nullable()->constrained('users')->onDelete('set null');
            $table->decimal('budget', 12, 2)->nullable();
            $table->decimal('total_cost', 12, 2)->default(0);
            $table->date('start_date')->nullable();
            $table->date('deadline')->nullable();
            $table->date('completed_at')->nullable();
            $table->integer('progress_percentage')->default(0);
            $table->json('requirements')->nullable(); // project requirements
            $table->json('deliverables')->nullable(); // expected deliverables
            $table->json('metadata')->nullable(); // additional project data
            $table->timestamps();
            
            $table->index(['status', 'priority']);
            $table->index(['client_id', 'project_manager_id']);
            $table->index(['start_date', 'deadline']);
            $table->index('progress_percentage');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
