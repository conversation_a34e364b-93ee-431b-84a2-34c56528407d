<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Department;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        // Get departments and roles
        $adminDept = Department::where('name', 'Administration')->first();
        $projectDept = Department::where('name', 'Project Management')->first();
        $audioDept = Department::where('name', 'Audio Engineering')->first();
        $musicDept = Department::where('name', 'Music Production')->first();
        $videoDept = Department::where('name', 'Video Production')->first();
        $clientDept = Department::where('name', 'Client Relations')->first();

        $superAdminRole = Role::where('name', 'Super Admin')->first();
        $managerRole = Role::where('name', 'Studio Manager')->first();
        $pmRole = Role::where('name', 'Project Manager')->first();
        $leadRole = Role::where('name', 'Lead Audio Engineer')->first();
        $producerRole = Role::where('name', 'Music Producer')->first();
        $clientRole = Role::where('name', 'Client Manager')->first();

        $users = [
            [
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'department_id' => $adminDept->id,
                'role_id' => $superAdminRole->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['management', 'administration', 'leadership'],
                'hourly_rate' => 100000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'Best',
                'last_name' => 'Amakhian',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => $adminDept->id,
                'role_id' => $managerRole->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['studio_management', 'team_leadership', 'client_relations'],
                'hourly_rate' => 75000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'Gift',
                'last_name' => 'Israel',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => $audioDept->id,
                'role_id' => $leadRole->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['mixing', 'mastering', 'recording', 'pro_tools', 'logic_pro'],
                'hourly_rate' => 50000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'Tosin',
                'last_name' => 'Akinbo',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => $musicDept->id,
                'role_id' => $producerRole->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['beat_making', 'arrangement', 'fl_studio', 'ableton', 'composition'],
                'hourly_rate' => 45000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'Kemi',
                'last_name' => 'Adebayo',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => $projectDept->id,
                'role_id' => $pmRole->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['project_management', 'scheduling', 'client_communication', 'budgeting'],
                'hourly_rate' => 40000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'David',
                'last_name' => 'Okafor',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => $videoDept->id,
                'role_id' => Role::where('name', 'Video Producer')->first()->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['video_editing', 'cinematography', 'premiere_pro', 'after_effects'],
                'hourly_rate' => 42000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'Funmi',
                'last_name' => 'Ogundimu',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => $clientDept->id,
                'role_id' => $clientRole->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['client_relations', 'sales', 'communication', 'negotiation'],
                'hourly_rate' => 35000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'Emmanuel',
                'last_name' => 'Adebisi',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => $audioDept->id,
                'role_id' => Role::where('name', 'Audio Engineer')->first()->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['recording', 'editing', 'mixing', 'pro_tools'],
                'hourly_rate' => 30000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'Chioma',
                'last_name' => 'Nwankwo',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => Department::where('name', 'Marketing')->first()->id,
                'role_id' => Role::where('name', 'Marketing Specialist')->first()->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['digital_marketing', 'social_media', 'content_creation', 'analytics'],
                'hourly_rate' => 32000.00,
                'email_verified_at' => now(),
            ],
            [
                'first_name' => 'Segun',
                'last_name' => 'Babatunde',
                'email' => '<EMAIL>',
                'phone' => '+234-************',
                'password' => Hash::make('password'),
                'department_id' => Department::where('name', 'Post Production')->first()->id,
                'role_id' => Role::where('name', 'Post Production Specialist')->first()->id,
                'status' => 'active',
                'is_available' => true,
                'skills' => ['video_editing', 'color_grading', 'sound_design', 'final_cut_pro'],
                'hourly_rate' => 38000.00,
                'email_verified_at' => now(),
            ]
        ];

        foreach ($users as $userData) {
            User::create($userData);
        }

        $this->command->info('Users seeded successfully!');
    }
}
