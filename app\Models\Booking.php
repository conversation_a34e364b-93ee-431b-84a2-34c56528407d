<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Booking extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'project_id',
        'studio_room_id',
        'engineer_id',
        'start_time',
        'end_time',
        'status',
        'hourly_rate',
        'total_cost',
        'notes',
        'equipment_requests',
        'special_requirements',
        'requires_setup',
        'setup_time_minutes',
        'confirmed_at',
        'checked_in_at',
        'checked_out_at'
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'hourly_rate' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'equipment_requests' => 'array',
        'special_requirements' => 'array',
        'requires_setup' => 'boolean',
        'confirmed_at' => 'datetime',
        'checked_in_at' => 'datetime',
        'checked_out_at' => 'datetime'
    ];

    // relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function studioRoom(): BelongsTo
    {
        return $this->belongsTo(StudioRoom::class);
    }

    public function engineer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'engineer_id');
    }

    public function equipmentBookings(): HasMany
    {
        return $this->hasMany(EquipmentBooking::class);
    }

    // scopes
    public function scopeConfirmed($query)
    {
        return $query->where('status', 'confirmed');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('start_time', today());
    }

    public function scopeUpcoming($query, $days = 7)
    {
        return $query->where('start_time', '>=', now())
                    ->where('start_time', '<=', now()->addDays($days));
    }

    public function scopeByRoom($query, $roomId)
    {
        return $query->where('studio_room_id', $roomId);
    }

    public function scopeByClient($query, $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    // methods
    public function confirm(): void
    {
        $this->update([
            'status' => 'confirmed',
            'confirmed_at' => now()
        ]);
    }

    public function checkIn(): void
    {
        $this->update([
            'status' => 'in_progress',
            'checked_in_at' => now()
        ]);
    }

    public function checkOut(): void
    {
        $this->update([
            'status' => 'completed',
            'checked_out_at' => now()
        ]);

        // update client stats
        $this->client->updateProjectStats();
    }

    public function cancel(): void
    {
        $this->update(['status' => 'cancelled']);
    }

    public function getDurationInHours(): float
    {
        return $this->start_time->diffInHours($this->end_time);
    }

    public function calculateTotalCost(): void
    {
        $hours = $this->getDurationInHours();
        $baseCost = $hours * $this->hourly_rate;
        
        // add equipment costs
        $equipmentCost = $this->equipmentBookings()->sum('additional_cost');
        
        $this->update(['total_cost' => $baseCost + $equipmentCost]);
    }

    public function isOverlapping(Booking $otherBooking): bool
    {
        return $this->studio_room_id === $otherBooking->studio_room_id &&
               $this->start_time < $otherBooking->end_time &&
               $this->end_time > $otherBooking->start_time;
    }

    public function canBeModified(): bool
    {
        return in_array($this->status, ['pending', 'confirmed']) &&
               $this->start_time->isFuture();
    }

    public function getSetupEndTime(): \DateTime
    {
        return $this->start_time->copy()->subMinutes($this->setup_time_minutes);
    }

    public function requiresEngineer(): bool
    {
        return $this->studioRoom->requires_engineer || $this->engineer_id !== null;
    }
}
