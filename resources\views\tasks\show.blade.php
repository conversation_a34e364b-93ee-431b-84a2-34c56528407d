@extends('layouts.app')

@section('title', 'Task Details - ' . $task->title)

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $task->title }}</h1>
                <p class="mt-2 text-gray-600">{{ $task->description ?: 'No description available' }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('tasks.edit', $task) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-edit mr-2"></i>Edit Task
                </a>
                <a href="{{ route('tasks.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Tasks
                </a>
            </div>
        </div>
    </div>

    <!-- Status and Progress -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Status</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {{ $task->status == 'completed' ? 'bg-green-100 text-green-800' : 
                                       ($task->status == 'in_progress' ? 'bg-blue-100 text-blue-800' : 
                                       ($task->status == 'cancelled' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800')) }}">
                                    {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                </span>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Progress</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ $task->progress_percentage ?? 0 }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Priority</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ ucfirst($task->priority ?? 'Medium') }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-calendar text-2xl text-red-600"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Due Date</dt>
                            <dd class="text-lg font-medium text-gray-900">
                                {{ $task->due_date ? $task->due_date->format('M d, Y') : 'Not set' }}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Details -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Task Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Task Information</h3>
                </div>
                <div class="px-6 py-4">
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Project</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                @if($task->project)
                                    <a href="{{ route('projects.show', $task->project) }}" class="text-indigo-600 hover:text-indigo-900">
                                        {{ $task->project->title }}
                                    </a>
                                @else
                                    No project assigned
                                @endif
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Assigned To</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {{ $task->assignedUser ? $task->assignedUser->first_name . ' ' . $task->assignedUser->last_name : 'Unassigned' }}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Department</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $task->department->name ?? 'Not specified' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Assigned By</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {{ $task->assignedBy ? $task->assignedBy->first_name . ' ' . $task->assignedBy->last_name : 'System' }}
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Estimated Hours</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $task->estimated_hours ?? 'Not set' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Actual Hours</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $task->actual_hours ?? 'Not tracked' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Started At</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $task->started_at ? $task->started_at->format('M d, Y H:i') : 'Not started' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Completed At</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $task->completed_at ? $task->completed_at->format('M d, Y H:i') : 'Not completed' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Requirements & Deliverables -->
            @if($task->requirements || $task->deliverables)
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Requirements & Deliverables</h3>
                </div>
                <div class="px-6 py-4 space-y-4">
                    @if($task->requirements)
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Requirements</h4>
                        <div class="text-sm text-gray-600 whitespace-pre-wrap">{{ $task->requirements }}</div>
                    </div>
                    @endif
                    
                    @if($task->deliverables)
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Deliverables</h4>
                        <div class="text-sm text-gray-600 whitespace-pre-wrap">{{ $task->deliverables }}</div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- File Uploads -->
            @if($task->fileUploads && $task->fileUploads->count() > 0)
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Files ({{ $task->fileUploads->count() }})</h3>
                </div>
                <div class="px-6 py-4">
                    <ul class="divide-y divide-gray-200">
                        @foreach($task->fileUploads as $file)
                        <li class="py-3 flex justify-between items-center">
                            <div class="flex items-center">
                                <i class="fas fa-file text-gray-400 mr-3"></i>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $file->original_name }}</p>
                                    <p class="text-sm text-gray-500">{{ $file->file_size ? number_format($file->file_size / 1024, 2) . ' KB' : '' }}</p>
                                </div>
                            </div>
                            <a href="{{ $file->file_path }}" class="text-indigo-600 hover:text-indigo-900" download>
                                <i class="fas fa-download"></i>
                            </a>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-8">
            <!-- Progress Chart -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Progress Overview</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{{ $task->progress_percentage ?? 0 }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-3">
                            <div class="bg-indigo-600 h-3 rounded-full" style="width: {{ $task->progress_percentage ?? 0 }}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dependencies -->
            @if($task->dependencies && $task->dependencies->count() > 0)
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Dependencies</h3>
                </div>
                <div class="px-6 py-4">
                    <ul class="space-y-2">
                        @foreach($task->dependencies as $dependency)
                        <li class="flex items-center justify-between">
                            <span class="text-sm text-gray-900">{{ $dependency->title }}</span>
                            <span class="text-xs px-2 py-1 rounded-full 
                                {{ $dependency->status == 'completed' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                {{ ucfirst($dependency->status) }}
                            </span>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                </div>
                <div class="px-6 py-4 space-y-3">
                    @if($task->status != 'in_progress')
                    <button class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm">
                        <i class="fas fa-play mr-2"></i>Start Task
                    </button>
                    @endif
                    
                    @if($task->status != 'completed')
                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                        <i class="fas fa-check mr-2"></i>Mark Complete
                    </button>
                    @endif
                    
                    <button class="w-full bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg text-sm">
                        <i class="fas fa-percentage mr-2"></i>Update Progress
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
