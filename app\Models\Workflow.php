<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Workflow extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'project_type',
        'stages',
        'automation_rules',
        'is_default',
        'is_active',
        'created_by'
    ];

    protected $casts = [
        'stages' => 'array',
        'automation_rules' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean'
    ];

    // relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeForProjectType($query, $projectType)
    {
        return $query->where('project_type', $projectType);
    }

    // methods
    public function createTasksForProject(Project $project): void
    {
        if (!$this->stages) {
            return;
        }

        foreach ($this->stages as $stage) {
            foreach ($stage['tasks'] ?? [] as $taskTitle) {
                Task::create([
                    'title' => $taskTitle,
                    'description' => "Task from {$stage['name']} stage",
                    'project_id' => $project->id,
                    'workflow_stage_id' => $stage['id'],
                    'department_id' => $stage['department_id'] ?? null,
                    'status' => 'pending',
                    'priority' => 'medium',
                    'estimated_hours' => $stage['estimated_hours'] ?? null,
                    'assigned_by' => $project->project_manager_id
                ]);
            }
        }
    }

    public function getStageById(int $stageId): ?array
    {
        foreach ($this->stages as $stage) {
            if ($stage['id'] === $stageId) {
                return $stage;
            }
        }
        
        return null;
    }

    public function getNextStage(int $currentStageId): ?array
    {
        $stages = $this->stages;
        $currentIndex = null;
        
        foreach ($stages as $index => $stage) {
            if ($stage['id'] === $currentStageId) {
                $currentIndex = $index;
                break;
            }
        }
        
        if ($currentIndex !== null && isset($stages[$currentIndex + 1])) {
            return $stages[$currentIndex + 1];
        }
        
        return null;
    }

    public function getPreviousStage(int $currentStageId): ?array
    {
        $stages = $this->stages;
        $currentIndex = null;
        
        foreach ($stages as $index => $stage) {
            if ($stage['id'] === $currentStageId) {
                $currentIndex = $index;
                break;
            }
        }
        
        if ($currentIndex !== null && $currentIndex > 0) {
            return $stages[$currentIndex - 1];
        }
        
        return null;
    }

    public function getTotalEstimatedHours(): int
    {
        $total = 0;
        
        foreach ($this->stages as $stage) {
            $total += $stage['estimated_hours'] ?? 0;
        }
        
        return $total;
    }

    public function executeAutomationRules(string $trigger, array $context = []): void
    {
        if (!$this->automation_rules) {
            return;
        }

        foreach ($this->automation_rules as $rule) {
            if ($rule['trigger'] === $trigger) {
                $this->executeRule($rule, $context);
            }
        }
    }

    private function executeRule(array $rule, array $context): void
    {
        // check conditions
        if (isset($rule['condition']) && !$this->evaluateCondition($rule['condition'], $context)) {
            return;
        }

        // execute action
        switch ($rule['action']) {
            case 'notify_department':
                $this->notifyDepartment($rule['params'], $context);
                break;
            case 'escalate_to_manager':
                $this->escalateToManager($rule['params'], $context);
                break;
            case 'create_task':
                $this->createAutomatedTask($rule['params'], $context);
                break;
        }
    }

    private function evaluateCondition(string $condition, array $context): bool
    {
        // simple condition evaluation - can be expanded
        foreach ($context as $key => $value) {
            $condition = str_replace($key, $value, $condition);
        }
        
        // basic evaluation - in production, use a proper expression evaluator
        return eval("return $condition;");
    }

    private function notifyDepartment(array $params, array $context): void
    {
        // implementation for department notification
    }

    private function escalateToManager(array $params, array $context): void
    {
        // implementation for manager escalation
    }

    private function createAutomatedTask(array $params, array $context): void
    {
        // implementation for automated task creation
    }
}
