<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectWorkflow extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'project_type',
        'stages',
        'automation_rules',
        'is_default',
        'is_active',
        'created_by'
    ];

    protected $casts = [
        'stages' => 'array',
        'automation_rules' => 'array',
        'is_default' => 'boolean',
        'is_active' => 'boolean'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // Relationships
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function projects()
    {
        return $this->hasMany(Project::class, 'workflow_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    public function scopeByProjectType($query, $projectType)
    {
        return $query->where('project_type', $projectType);
    }

    // Accessors
    public function getStageCountAttribute()
    {
        return count($this->stages ?? []);
    }

    public function getAutomationRuleCountAttribute()
    {
        return count($this->automation_rules ?? []);
    }

    public function getTotalEstimatedDurationAttribute()
    {
        if (!$this->stages) {
            return 0;
        }

        return collect($this->stages)->sum('estimated_duration');
    }

    // Methods
    public function getStageByOrder($order)
    {
        if (!$this->stages) {
            return null;
        }

        return collect($this->stages)->firstWhere('order', $order);
    }

    public function getStagesByDepartment($departmentId)
    {
        if (!$this->stages) {
            return collect();
        }

        return collect($this->stages)->where('department_id', $departmentId);
    }

    public function getRequiredSkills()
    {
        if (!$this->stages) {
            return collect();
        }

        $skills = collect($this->stages)
            ->pluck('required_skills')
            ->flatten()
            ->filter()
            ->unique()
            ->values();

        return $skills;
    }

    public function createProjectFromWorkflow($projectData)
    {
        // Create the project
        $project = Project::create(array_merge($projectData, [
            'workflow_id' => $this->id
        ]));

        // Create stages as tasks
        if ($this->stages) {
            foreach ($this->stages as $stage) {
                Task::create([
                    'project_id' => $project->id,
                    'title' => $stage['name'],
                    'description' => $stage['description'] ?? null,
                    'department_id' => $stage['department_id'] ?? null,
                    'priority' => 'medium',
                    'status' => 'pending',
                    'order' => $stage['order'],
                    'estimated_duration' => $stage['estimated_duration'] ?? null,
                    'required_skills' => $stage['required_skills'] ?? [],
                    'due_date' => $this->calculateDueDate($project->start_date, $stage),
                    'created_by' => $project->created_by
                ]);
            }
        }

        return $project;
    }

    private function calculateDueDate($startDate, $stage)
    {
        if (!$stage['estimated_duration'] || !$startDate) {
            return null;
        }

        // Calculate based on previous stages
        $previousStages = collect($this->stages)
            ->where('order', '<', $stage['order'])
            ->sum('estimated_duration');

        return \Carbon\Carbon::parse($startDate)
            ->addDays($previousStages + $stage['estimated_duration']);
    }

    public function duplicate($newName = null)
    {
        $newWorkflow = $this->replicate();
        $newWorkflow->name = $newName ?: $this->name . ' (Copy)';
        $newWorkflow->slug = \Illuminate\Support\Str::slug($newWorkflow->name);
        $newWorkflow->is_default = false;
        $newWorkflow->created_by = auth()->id();
        $newWorkflow->save();

        return $newWorkflow;
    }

    public function getProjectTypeDisplayAttribute()
    {
        $types = [
            'music_production' => 'Music Production',
            'video_production' => 'Video Production',
            'podcast' => 'Podcast',
            'mixing_mastering' => 'Mixing & Mastering',
            'voice_over' => 'Voice Over',
            'sound_design' => 'Sound Design',
            'live_recording' => 'Live Recording'
        ];

        return $types[$this->project_type] ?? ucfirst(str_replace('_', ' ', $this->project_type));
    }

    public function validateStages()
    {
        if (!$this->stages || empty($this->stages)) {
            return ['error' => 'Workflow must have at least one stage'];
        }

        $orders = collect($this->stages)->pluck('order')->sort()->values();
        $expectedOrders = range(1, count($this->stages));

        if ($orders->toArray() !== $expectedOrders) {
            return ['error' => 'Stage orders must be sequential starting from 1'];
        }

        foreach ($this->stages as $stage) {
            if (empty($stage['name'])) {
                return ['error' => 'All stages must have a name'];
            }
        }

        return ['success' => true];
    }

    public function getAutomationRulesByTrigger($trigger)
    {
        if (!$this->automation_rules) {
            return collect();
        }

        return collect($this->automation_rules)->where('trigger', $trigger);
    }

    // Static methods
    public static function getDefaultForProjectType($projectType)
    {
        return static::where('project_type', $projectType)
            ->where('is_default', true)
            ->where('is_active', true)
            ->first();
    }

    public static function getProjectTypes()
    {
        return [
            'music_production' => 'Music Production',
            'video_production' => 'Video Production',
            'podcast' => 'Podcast',
            'mixing_mastering' => 'Mixing & Mastering',
            'voice_over' => 'Voice Over',
            'sound_design' => 'Sound Design',
            'live_recording' => 'Live Recording'
        ];
    }

    public static function createDefaultWorkflows()
    {
        $defaultWorkflows = [
            [
                'name' => 'Standard Music Production',
                'project_type' => 'music_production',
                'description' => 'Complete music production workflow from pre-production to mastering',
                'is_default' => true,
                'stages' => [
                    ['name' => 'Pre-Production', 'order' => 1, 'estimated_duration' => 2, 'description' => 'Planning and preparation'],
                    ['name' => 'Recording', 'order' => 2, 'estimated_duration' => 5, 'description' => 'Audio recording sessions'],
                    ['name' => 'Editing', 'order' => 3, 'estimated_duration' => 3, 'description' => 'Audio editing and cleanup'],
                    ['name' => 'Mixing', 'order' => 4, 'estimated_duration' => 4, 'description' => 'Audio mixing and balancing'],
                    ['name' => 'Mastering', 'order' => 5, 'estimated_duration' => 2, 'description' => 'Final mastering and polish'],
                    ['name' => 'Delivery', 'order' => 6, 'estimated_duration' => 1, 'description' => 'Final delivery to client']
                ]
            ],
            [
                'name' => 'Standard Video Production',
                'project_type' => 'video_production',
                'description' => 'Complete video production workflow from planning to final export',
                'is_default' => true,
                'stages' => [
                    ['name' => 'Pre-Production', 'order' => 1, 'estimated_duration' => 3, 'description' => 'Planning and scripting'],
                    ['name' => 'Filming', 'order' => 2, 'estimated_duration' => 5, 'description' => 'Video recording sessions'],
                    ['name' => 'Editing', 'order' => 3, 'estimated_duration' => 7, 'description' => 'Video editing and cuts'],
                    ['name' => 'Audio Post', 'order' => 4, 'estimated_duration' => 3, 'description' => 'Audio editing and mixing'],
                    ['name' => 'Color Grading', 'order' => 5, 'estimated_duration' => 2, 'description' => 'Color correction and grading'],
                    ['name' => 'Final Export', 'order' => 6, 'estimated_duration' => 1, 'description' => 'Rendering and delivery']
                ]
            ],
            [
                'name' => 'Standard Podcast Production',
                'project_type' => 'podcast',
                'description' => 'Podcast production workflow from planning to publishing',
                'is_default' => true,
                'stages' => [
                    ['name' => 'Planning', 'order' => 1, 'estimated_duration' => 1, 'description' => 'Episode planning and research'],
                    ['name' => 'Recording', 'order' => 2, 'estimated_duration' => 1, 'description' => 'Podcast recording session'],
                    ['name' => 'Editing', 'order' => 3, 'estimated_duration' => 2, 'description' => 'Audio editing and cleanup'],
                    ['name' => 'Post-Production', 'order' => 4, 'estimated_duration' => 1, 'description' => 'Adding intro/outro, music'],
                    ['name' => 'Publishing', 'order' => 5, 'estimated_duration' => 1, 'description' => 'Upload and distribution']
                ]
            ]
        ];

        foreach ($defaultWorkflows as $workflowData) {
            $workflowData['slug'] = \Illuminate\Support\Str::slug($workflowData['name']);
            $workflowData['is_active'] = true;
            $workflowData['created_by'] = 1; // Assuming admin user ID is 1

            static::create($workflowData);
        }
    }
}
