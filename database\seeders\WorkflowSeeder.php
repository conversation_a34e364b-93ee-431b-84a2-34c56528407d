<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WorkflowSeeder extends Seeder
{
    public function run(): void
    {
        $workflows = [
            [
                'name' => 'Music Production Workflow',
                'slug' => 'music-production',
                'description' => 'Standard workflow for music production projects',
                'project_type' => 'music',
                'is_default' => true,
                'created_by' => 1,
                'stages' => json_encode([
                    [
                        'id' => 1,
                        'name' => 'Pre-Production',
                        'description' => 'Planning and preparation phase',
                        'department_id' => 2, // Project Management
                        'estimated_hours' => 8,
                        'tasks' => [
                            'Client consultation',
                            'Project scope definition',
                            'Budget approval',
                            'Team assignment'
                        ]
                    ],
                    [
                        'id' => 2,
                        'name' => 'Recording',
                        'description' => 'Audio recording phase',
                        'department_id' => 3, // Audio Engineering
                        'estimated_hours' => 16,
                        'tasks' => [
                            'Studio setup',
                            'Sound check',
                            'Recording sessions',
                            'Take compilation'
                        ]
                    ],
                    [
                        'id' => 3,
                        'name' => 'Production',
                        'description' => 'Music production and arrangement',
                        'department_id' => 4, // Music Production
                        'estimated_hours' => 24,
                        'tasks' => [
                            'Beat creation',
                            'Arrangement',
                            'Additional instrumentation',
                            'Vocal production'
                        ]
                    ],
                    [
                        'id' => 4,
                        'name' => 'Mixing',
                        'description' => 'Audio mixing phase',
                        'department_id' => 3, // Audio Engineering
                        'estimated_hours' => 12,
                        'tasks' => [
                            'Track preparation',
                            'Mixing process',
                            'Client review',
                            'Revisions'
                        ]
                    ],
                    [
                        'id' => 5,
                        'name' => 'Mastering',
                        'description' => 'Final mastering phase',
                        'department_id' => 3, // Audio Engineering
                        'estimated_hours' => 4,
                        'tasks' => [
                            'Mastering process',
                            'Quality check',
                            'Format preparation',
                            'Final delivery'
                        ]
                    ]
                ]),
                'automation_rules' => json_encode([
                    [
                        'trigger' => 'stage_completed',
                        'condition' => 'stage_id == 1',
                        'action' => 'notify_department',
                        'params' => ['department_id' => 3]
                    ],
                    [
                        'trigger' => 'task_overdue',
                        'condition' => 'hours_overdue > 24',
                        'action' => 'escalate_to_manager',
                        'params' => ['notify_client' => false]
                    ]
                ])
            ],
            [
                'name' => 'Podcast Production Workflow',
                'slug' => 'podcast-production',
                'description' => 'Workflow for podcast recording and production',
                'project_type' => 'podcast',
                'is_default' => true,
                'created_by' => 1,
                'stages' => json_encode([
                    [
                        'id' => 1,
                        'name' => 'Pre-Production',
                        'description' => 'Podcast planning and preparation',
                        'department_id' => 2,
                        'estimated_hours' => 4,
                        'tasks' => [
                            'Topic research',
                            'Guest coordination',
                            'Script preparation',
                            'Studio booking'
                        ]
                    ],
                    [
                        'id' => 2,
                        'name' => 'Recording',
                        'description' => 'Podcast recording session',
                        'department_id' => 3,
                        'estimated_hours' => 3,
                        'tasks' => [
                            'Equipment setup',
                            'Sound check',
                            'Recording session',
                            'Backup creation'
                        ]
                    ],
                    [
                        'id' => 3,
                        'name' => 'Post-Production',
                        'description' => 'Editing and post-production',
                        'department_id' => 6, // Post Production
                        'estimated_hours' => 6,
                        'tasks' => [
                            'Audio editing',
                            'Noise reduction',
                            'Music and effects',
                            'Final mix'
                        ]
                    ],
                    [
                        'id' => 4,
                        'name' => 'Publishing',
                        'description' => 'Final delivery and publishing',
                        'department_id' => 8, // Marketing
                        'estimated_hours' => 2,
                        'tasks' => [
                            'Format export',
                            'Metadata addition',
                            'Platform upload',
                            'Promotion'
                        ]
                    ]
                ])
            ],
            [
                'name' => 'Video Production Workflow',
                'slug' => 'video-production',
                'description' => 'Complete video production workflow',
                'project_type' => 'video',
                'is_default' => true,
                'created_by' => 1,
                'stages' => json_encode([
                    [
                        'id' => 1,
                        'name' => 'Pre-Production',
                        'description' => 'Video planning and preparation',
                        'department_id' => 5, // Video Production
                        'estimated_hours' => 12,
                        'tasks' => [
                            'Concept development',
                            'Storyboard creation',
                            'Location scouting',
                            'Crew assignment'
                        ]
                    ],
                    [
                        'id' => 2,
                        'name' => 'Production',
                        'description' => 'Video shooting phase',
                        'department_id' => 5,
                        'estimated_hours' => 8,
                        'tasks' => [
                            'Equipment setup',
                            'Lighting setup',
                            'Video recording',
                            'Audio recording'
                        ]
                    ],
                    [
                        'id' => 3,
                        'name' => 'Post-Production',
                        'description' => 'Video editing and finishing',
                        'department_id' => 6, // Post Production
                        'estimated_hours' => 20,
                        'tasks' => [
                            'Video editing',
                            'Color grading',
                            'Audio post',
                            'Graphics and effects'
                        ]
                    ],
                    [
                        'id' => 4,
                        'name' => 'Delivery',
                        'description' => 'Final delivery and distribution',
                        'department_id' => 6,
                        'estimated_hours' => 4,
                        'tasks' => [
                            'Final render',
                            'Quality check',
                            'Format delivery',
                            'Archive creation'
                        ]
                    ]
                ])
            ]
        ];

        foreach ($workflows as $workflow) {
            DB::table('workflows')->insert(array_merge($workflow, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}
