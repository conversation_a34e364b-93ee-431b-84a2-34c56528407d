<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:6',
            'remember' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $credentials = $request->only('email', 'password');
        $remember = $request->boolean('remember');

        if (!auth()->attempt($credentials, $remember)) {
            // track failed login attempt
            AnalyticsEvent::track(
                'login_failed',
                'auth',
                null,
                ['email' => $request->email]
            );

            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        $user = auth()->user();

        // check if user is active
        if ($user->status !== 'active') {
            auth()->logout();
            return response()->json([
                'success' => false,
                'message' => 'Account is not active'
            ], 403);
        }

        // load relationships
        $user->load(['department', 'role']);

        // track successful login
        AnalyticsEvent::trackUserEvent('login_success', $user);

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'full_name' => $user->full_name,
                    'email' => $user->email,
                    'avatar' => $user->avatar,
                    'department' => $user->department,
                    'role' => $user->role,
                    'permissions' => $user->role?->permissions ?? [],
                    'is_available' => $user->is_available
                ],
                'token' => $user->createToken('auth-token')->plainTextToken
            ]
        ]);
    }

    public function logout(Request $request)
    {
        $user = auth()->user();
        
        if ($user) {
            // track logout
            AnalyticsEvent::trackUserEvent('logout', $user);
            
            // revoke current token
            $request->user()->currentAccessToken()->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    public function me(Request $request)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'User not authenticated'
            ], 401);
        }

        $user->load(['department', 'role']);

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'full_name' => $user->full_name,
                    'email' => $user->email,
                    'phone' => $user->phone,
                    'avatar' => $user->avatar,
                    'department' => $user->department,
                    'role' => $user->role,
                    'permissions' => $user->role?->permissions ?? [],
                    'is_available' => $user->is_available,
                    'preferences' => $user->preferences,
                    'skills' => $user->skills,
                    'last_active_at' => $user->last_active_at
                ]
            ]
        ]);
    }

    public function updateProfile(Request $request)
    {
        $user = $request->user();
        
        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'phone' => 'sometimes|string|max:20',
            'preferences' => 'sometimes|array',
            'skills' => 'sometimes|array',
            'is_available' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Profile updated successfully',
            'data' => ['user' => $user->fresh(['department', 'role'])]
        ]);
    }

    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Current password is incorrect'
            ], 422);
        }

        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        // track password change
        AnalyticsEvent::trackUserEvent('password_changed', $user);

        return response()->json([
            'success' => true,
            'message' => 'Password changed successfully'
        ]);
    }
}
