<?php

namespace App\Services;

use App\Models\AutomationWorkflow;
use App\Models\Task;
use App\Models\User;
use App\Models\Project;
use App\Models\Department;
use App\Models\ChatChannel;
use App\Models\ChatMessage;
use App\Models\AnalyticsEvent;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class WorkflowEngine
{
    public function execute(AutomationWorkflow $workflow, array $triggerData): array
    {
        if (!$workflow->is_active) {
            throw new \Exception('Workflow is not active');
        }

        $results = [];
        $context = $this->buildContext($workflow, $triggerData);

        foreach ($workflow->actions as $action) {
            try {
                $result = $this->executeAction($action, $context);
                $results[] = [
                    'action' => $action['type'],
                    'success' => true,
                    'result' => $result
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'action' => $action['type'],
                    'success' => false,
                    'error' => $e->getMessage()
                ];
                
                Log::error('Workflow action failed', [
                    'workflow_id' => $workflow->id,
                    'action' => $action,
                    'error' => $e->getMessage(),
                    'context' => $context
                ]);
            }
        }

        // track workflow execution
        AnalyticsEvent::track('workflow_executed', 'workflow', $workflow, [
            'trigger_data' => $triggerData,
            'results' => $results
        ]);

        return $results;
    }

    public function trigger(string $triggerType, array $data): void
    {
        $workflows = AutomationWorkflow::where('trigger_type', $triggerType)
            ->where('is_active', true)
            ->get();

        foreach ($workflows as $workflow) {
            if ($this->shouldExecute($workflow, $data)) {
                try {
                    $this->execute($workflow, $data);
                } catch (\Exception $e) {
                    Log::error('Workflow execution failed', [
                        'workflow_id' => $workflow->id,
                        'trigger_type' => $triggerType,
                        'data' => $data,
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }

    private function shouldExecute(AutomationWorkflow $workflow, array $data): bool
    {
        if (empty($workflow->trigger_conditions)) {
            return true;
        }

        foreach ($workflow->trigger_conditions as $condition => $value) {
            if (!$this->evaluateCondition($condition, $value, $data)) {
                return false;
            }
        }

        return true;
    }

    private function evaluateCondition(string $condition, $expectedValue, array $data): bool
    {
        switch ($condition) {
            case 'project_type':
                return isset($data['project']['type']) && $data['project']['type'] === $expectedValue;
            
            case 'task_priority':
                return isset($data['task']['priority']) && $data['task']['priority'] === $expectedValue;
            
            case 'department_id':
                return isset($data['department_id']) && $data['department_id'] == $expectedValue;
            
            case 'days_before':
                if (isset($data['due_date'])) {
                    $dueDate = new \DateTime($data['due_date']);
                    $now = new \DateTime();
                    $daysDiff = $now->diff($dueDate)->days;
                    return $daysDiff <= $expectedValue;
                }
                return false;
            
            default:
                return true;
        }
    }

    private function buildContext(AutomationWorkflow $workflow, array $triggerData): array
    {
        $context = $triggerData;
        
        // add workflow info
        $context['workflow'] = [
            'id' => $workflow->id,
            'name' => $workflow->name
        ];

        // resolve entity relationships
        if (isset($triggerData['project_id'])) {
            $project = Project::with(['client', 'projectManager'])->find($triggerData['project_id']);
            if ($project) {
                $context['project'] = $project->toArray();
            }
        }

        if (isset($triggerData['task_id'])) {
            $task = Task::with(['project', 'assignedUser', 'department'])->find($triggerData['task_id']);
            if ($task) {
                $context['task'] = $task->toArray();
            }
        }

        if (isset($triggerData['user_id'])) {
            $user = User::find($triggerData['user_id']);
            if ($user) {
                $context['user'] = $user->toArray();
            }
        }

        return $context;
    }

    private function executeAction(array $action, array $context): mixed
    {
        $config = $action['config'];
        
        switch ($action['type']) {
            case 'create_task':
                return $this->createTask($config, $context);
            
            case 'send_notification':
                return $this->sendNotification($config, $context);
            
            case 'assign_user':
                return $this->assignUser($config, $context);
            
            case 'update_status':
                return $this->updateStatus($config, $context);
            
            case 'send_email':
                return $this->sendEmail($config, $context);
            
            default:
                throw new \Exception("Unknown action type: {$action['type']}");
        }
    }

    private function createTask(array $config, array $context): Task
    {
        $taskData = [
            'title' => $this->interpolate($config['title'], $context),
            'description' => $this->interpolate($config['description'] ?? '', $context),
            'priority' => $config['priority'] ?? 'medium',
            'status' => 'pending'
        ];

        // set project
        if (isset($config['project_id'])) {
            $taskData['project_id'] = $config['project_id'];
        } elseif (isset($context['project']['id'])) {
            $taskData['project_id'] = $context['project']['id'];
        }

        // set department
        if (isset($config['department_id'])) {
            $taskData['department_id'] = $config['department_id'];
        }

        // set assigned user
        if (isset($config['assigned_user_id'])) {
            $taskData['assigned_user_id'] = $config['assigned_user_id'];
        }

        // set due date
        if (isset($config['due_days'])) {
            $taskData['due_date'] = now()->addDays($config['due_days']);
        } elseif (isset($config['due_date'])) {
            $taskData['due_date'] = $this->interpolate($config['due_date'], $context);
        }

        return Task::create($taskData);
    }

    private function sendNotification(array $config, array $context): bool
    {
        $message = $this->interpolate($config['message'], $context);
        
        if (isset($config['user_id'])) {
            // send to specific user
            $userId = $this->interpolate($config['user_id'], $context);
            // implement user notification logic here
            return true;
        }

        if (isset($config['channels'])) {
            // send to chat channels
            foreach ($config['channels'] as $channelName) {
                $channelName = $this->interpolate($channelName, $context);
                $channel = ChatChannel::where('slug', $channelName)->first();
                
                if ($channel) {
                    ChatMessage::create([
                        'channel_id' => $channel->id,
                        'user_id' => 1, // system user
                        'message' => $message,
                        'type' => 'system'
                    ]);
                }
            }
            return true;
        }

        return false;
    }

    private function assignUser(array $config, array $context): bool
    {
        if (!isset($config['entity_type']) || !isset($config['entity_id']) || !isset($config['user_id'])) {
            throw new \Exception('Missing required config for assign_user action');
        }

        $entityType = $config['entity_type'];
        $entityId = $this->interpolate($config['entity_id'], $context);
        $userId = $this->interpolate($config['user_id'], $context);

        switch ($entityType) {
            case 'task':
                $task = Task::find($entityId);
                if ($task) {
                    $task->update(['assigned_user_id' => $userId]);
                    return true;
                }
                break;
            
            case 'project':
                $project = Project::find($entityId);
                if ($project) {
                    $project->update(['project_manager_id' => $userId]);
                    return true;
                }
                break;
        }

        return false;
    }

    private function updateStatus(array $config, array $context): bool
    {
        if (!isset($config['entity']) || !isset($config['status'])) {
            throw new \Exception('Missing required config for update_status action');
        }

        $entity = $config['entity'];
        $status = $config['status'];
        $entityId = $config['entity_id'] ?? null;

        switch ($entity) {
            case 'task':
                $id = $entityId ?: $context['task']['id'] ?? null;
                if ($id) {
                    $task = Task::find($id);
                    if ($task) {
                        $task->update(['status' => $status]);
                        return true;
                    }
                }
                break;
            
            case 'project':
                $id = $entityId ?: $context['project']['id'] ?? null;
                if ($id) {
                    $project = Project::find($id);
                    if ($project) {
                        $project->update(['status' => $status]);
                        return true;
                    }
                }
                break;
        }

        return false;
    }

    private function sendEmail(array $config, array $context): bool
    {
        $to = $this->interpolate($config['to'], $context);
        $subject = $this->interpolate($config['subject'], $context);
        $template = $config['template'] ?? 'default';

        // implement email sending logic here
        // Mail::to($to)->send(new WorkflowEmail($subject, $template, $context));
        
        return true;
    }

    private function interpolate(string $template, array $context): string
    {
        return preg_replace_callback('/\{\{([^}]+)\}\}/', function ($matches) use ($context) {
            $path = trim($matches[1]);
            $keys = explode('.', $path);
            $value = $context;

            foreach ($keys as $key) {
                if (is_array($value) && isset($value[$key])) {
                    $value = $value[$key];
                } else {
                    return $matches[0]; // return original if not found
                }
            }

            return is_scalar($value) ? (string) $value : $matches[0];
        }, $template);
    }
}
