<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('channel_id')->constrained('chat_channels')->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('message');
            $table->enum('type', ['text', 'file', 'image', 'audio', 'system'])->default('text');
            $table->json('attachments')->nullable();
            $table->foreignId('reply_to')->nullable()->constrained('chat_messages')->onDelete('set null');
            $table->boolean('is_edited')->default(false);
            $table->timestamp('edited_at')->nullable();
            $table->json('reactions')->nullable(); // emoji reactions
            $table->timestamps();
            
            $table->index(['channel_id', 'created_at']);
            $table->index(['user_id', 'type']);
            $table->index('reply_to');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('chat_messages');
    }
};
