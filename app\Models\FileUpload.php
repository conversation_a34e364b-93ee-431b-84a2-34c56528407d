<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Storage;

class FileUpload extends Model
{
    use HasFactory;

    protected $fillable = [
        'filename',
        'original_name',
        'path',
        'disk',
        'mime_type',
        'size',
        'uploadable_type',
        'uploadable_id',
        'uploaded_by',
        'type',
        'metadata',
        'is_processed',
        'versions'
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_processed' => 'boolean',
        'versions' => 'array'
    ];

    // relationships
    public function uploadable(): MorphTo
    {
        return $this->morphTo();
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    // scopes
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeAudio($query)
    {
        return $query->where('type', 'audio');
    }

    public function scopeVideo($query)
    {
        return $query->where('type', 'video');
    }

    public function scopeImages($query)
    {
        return $query->where('type', 'image');
    }

    public function scopeProcessed($query)
    {
        return $query->where('is_processed', true);
    }

    // methods
    public function getUrl(): string
    {
        return Storage::disk($this->disk)->url($this->path);
    }

    public function getDownloadUrl(): string
    {
        return route('files.download', $this->id);
    }

    public function getSizeFormatted(): string
    {
        $bytes = $this->size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function isAudio(): bool
    {
        return $this->type === 'audio' || 
               str_starts_with($this->mime_type, 'audio/');
    }

    public function isVideo(): bool
    {
        return $this->type === 'video' || 
               str_starts_with($this->mime_type, 'video/');
    }

    public function isImage(): bool
    {
        return $this->type === 'image' || 
               str_starts_with($this->mime_type, 'image/');
    }

    public function isDocument(): bool
    {
        return $this->type === 'document' || 
               in_array($this->mime_type, [
                   'application/pdf',
                   'application/msword',
                   'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                   'text/plain'
               ]);
    }

    public function delete(): bool
    {
        // delete file from storage
        if (Storage::disk($this->disk)->exists($this->path)) {
            Storage::disk($this->disk)->delete($this->path);
        }

        // delete versions if they exist
        if ($this->versions) {
            foreach ($this->versions as $version) {
                if (isset($version['path']) && Storage::disk($this->disk)->exists($version['path'])) {
                    Storage::disk($this->disk)->delete($version['path']);
                }
            }
        }

        return parent::delete();
    }

    public function addVersion(string $name, string $path, array $metadata = []): void
    {
        $versions = $this->versions ?? [];
        $versions[$name] = [
            'path' => $path,
            'metadata' => $metadata,
            'created_at' => now()->toISOString()
        ];
        
        $this->update(['versions' => $versions]);
    }

    public function getVersion(string $name): ?array
    {
        return $this->versions[$name] ?? null;
    }

    public function markAsProcessed(array $metadata = []): void
    {
        $this->update([
            'is_processed' => true,
            'metadata' => array_merge($this->metadata ?? [], $metadata)
        ]);
    }
}
