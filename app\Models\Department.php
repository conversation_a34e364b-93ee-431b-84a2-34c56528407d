<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color_code',
        'icon',
        'is_active',
        'settings'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'settings' => 'array'
    ];

    // relationships
    public function users(): Has<PERSON>any
    {
        return $this->hasMany(User::class);
    }

    public function roles(): HasMany
    {
        return $this->hasMany(Role::class);
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    public function chatChannels(): Has<PERSON>any
    {
        return $this->hasMany(ChatChannel::class);
    }

    // scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // methods
    public function getActiveUsersCount(): int
    {
        return $this->users()->where('status', 'active')->count();
    }

    public function getActiveTasksCount(): int
    {
        return $this->tasks()->whereIn('status', ['pending', 'in_progress'])->count();
    }
}
