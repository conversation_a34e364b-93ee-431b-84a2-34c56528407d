<?php

namespace App\Http\Controllers;

use App\Models\Role;
use App\Models\Department;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Inertia\Inertia;

class RoleController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_roles')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('dashboard')->with('error', 'Access denied');
        }

        $query = Role::with(['department', 'users'])
            ->orderBy('level', 'desc')
            ->orderBy('name');

        if ($request->has('department')) {
            $query->where('department_id', $request->department);
        }

        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->has('type')) {
            if ($request->type === 'system') {
                $query->where('is_system_role', true);
            } elseif ($request->type === 'custom') {
                $query->where('is_system_role', false);
            }
        }

        if ($request->expectsJson()) {
            $roles = $query->paginate($request->get('per_page', 15));
            return response()->json([
                'success' => true,
                'data' => [
                    'roles' => $roles->items(),
                    'pagination' => [
                        'current_page' => $roles->currentPage(),
                        'last_page' => $roles->lastPage(),
                        'per_page' => $roles->perPage(),
                        'total' => $roles->total()
                    ]
                ]
            ]);
        }

        $roles = $query->get();
        $departments = Department::active()->get();

        return view('roles.index', [
            'roles' => $roles,
            'departments' => $departments,
            'filters' => $request->only(['department', 'status', 'type'])
        ]);
    }

    public function create(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_roles')) {
            return redirect()->route('roles.index')->with('error', 'Access denied');
        }

        $departments = Department::active()->get();
        $permissions = $this->getAvailablePermissions();

        return view('roles.create', [
            'departments' => $departments,
            'permissions' => $permissions
        ]);
    }

    public function store(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_roles')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:roles,name',
            'description' => 'nullable|string',
            'department_id' => 'required|exists:departments,id',
            'permissions' => 'required|array',
            'permissions.*' => 'string',
            'level' => 'required|integer|min:1|max:10',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $data['slug'] = Str::slug($data['name']);
        $data['is_system_role'] = false;

        $role = Role::create($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Role created successfully',
                'data' => ['role' => $role->load(['department', 'users'])]
            ], 201);
        }

        return redirect()->route('roles.index')->with('success', 'Role created successfully');
    }

    public function show(Request $request, Role $role)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_roles')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('roles.index')->with('error', 'Access denied');
        }

        $role->load(['department', 'users.department']);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => ['role' => $role]
            ]);
        }

        return view('roles.show', [
            'role' => $role
        ]);
    }

    public function edit(Request $request, Role $role)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_roles')) {
            return redirect()->route('roles.index')->with('error', 'Access denied');
        }

        if ($role->is_system_role && !$user->hasPermission('*')) {
            return redirect()->route('roles.index')->with('error', 'Cannot edit system roles');
        }

        $departments = Department::active()->get();
        $permissions = $this->getAvailablePermissions();

        return view('roles.edit', [
            'role' => $role,
            'departments' => $departments,
            'permissions' => $permissions
        ]);
    }

    public function update(Request $request, Role $role)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_roles')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($role->is_system_role && !$user->hasPermission('*')) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot edit system roles'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255|unique:roles,name,' . $role->id,
            'description' => 'nullable|string',
            'department_id' => 'sometimes|exists:departments,id',
            'permissions' => 'sometimes|array',
            'permissions.*' => 'string',
            'level' => 'sometimes|integer|min:1|max:10',
            'is_active' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        if (isset($data['name'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        $role->update($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Role updated successfully',
                'data' => ['role' => $role->fresh(['department', 'users'])]
            ]);
        }

        return redirect()->route('roles.index')->with('success', 'Role updated successfully');
    }

    public function destroy(Request $request, Role $role)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_roles')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($role->is_system_role) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete system roles'
            ], 403);
        }

        if ($role->users()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete role with assigned users'
            ], 422);
        }

        $role->delete();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Role deleted successfully'
            ]);
        }

        return redirect()->route('roles.index')->with('success', 'Role deleted successfully');
    }

    protected function getAvailablePermissions(): array
    {
        return [
            'user_management' => [
                'view_users',
                'create_users',
                'edit_users',
                'delete_users',
                'manage_user_roles'
            ],
            'role_management' => [
                'view_roles',
                'manage_roles'
            ],
            'project_management' => [
                'view_projects',
                'create_projects',
                'edit_projects',
                'delete_projects',
                'manage_project_assignments'
            ],
            'task_management' => [
                'view_tasks',
                'create_tasks',
                'edit_tasks',
                'delete_tasks',
                'assign_tasks'
            ],
            'booking_management' => [
                'view_bookings',
                'create_bookings',
                'edit_bookings',
                'delete_bookings',
                'manage_studio_rooms'
            ],
            'financial_management' => [
                'view_financials',
                'manage_invoices',
                'manage_payments',
                'view_reports'
            ],
            'workflow_automation' => [
                'view_workflows',
                'manage_workflows',
                'execute_workflows'
            ],
            'system_administration' => [
                'manage_settings',
                'view_analytics',
                'manage_departments',
                'system_maintenance'
            ]
        ];
    }
}
