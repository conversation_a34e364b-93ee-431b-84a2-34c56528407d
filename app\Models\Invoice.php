<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Invoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_number',
        'client_id',
        'project_id',
        'created_by',
        'issue_date',
        'due_date',
        'sent_at',
        'paid_at',
        'line_items',
        'subtotal',
        'tax_rate',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'status',
        'notes',
        'payment_terms'
    ];

    protected $casts = [
        'issue_date' => 'date',
        'due_date' => 'date',
        'sent_at' => 'datetime',
        'paid_at' => 'datetime',
        'line_items' => 'array',
        'payment_terms' => 'array',
        'subtotal' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2'
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    // Scopes
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['paid', 'cancelled']);
    }

    public function scopeUnpaid($query)
    {
        return $query->whereIn('status', ['sent', 'partial', 'overdue']);
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    // Accessors & Mutators
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date < now() && !in_array($this->status, ['paid', 'cancelled']);
    }

    public function getDaysOverdueAttribute(): int
    {
        if (!$this->is_overdue) {
            return 0;
        }

        return now()->diffInDays($this->due_date);
    }

    public function getAmountPaidAttribute(): float
    {
        return $this->payments()->sum('amount');
    }

    public function getAmountDueAttribute(): float
    {
        return $this->total_amount - $this->amount_paid;
    }

    public function getPaymentStatusAttribute(): string
    {
        $amountPaid = $this->amount_paid;
        
        if ($amountPaid == 0) {
            return 'unpaid';
        } elseif ($amountPaid >= $this->total_amount) {
            return 'paid';
        } else {
            return 'partial';
        }
    }

    // Methods
    public function markAsSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now()
        ]);
    }

    public function markAsPaid(): void
    {
        $this->update([
            'status' => 'paid',
            'paid_at' => now()
        ]);
    }

    public function updatePaymentStatus(): void
    {
        $amountPaid = $this->amount_paid;
        
        if ($amountPaid >= $this->total_amount) {
            $this->markAsPaid();
        } elseif ($amountPaid > 0) {
            $this->update(['status' => 'partial']);
        } elseif ($this->is_overdue) {
            $this->update(['status' => 'overdue']);
        }
    }

    public function generatePDF(): string
    {
        // Implementation for PDF generation
        // Would use a library like DomPDF or similar
        return '';
    }

    public function sendEmail(): bool
    {
        // Implementation for sending invoice email
        return true;
    }

    public function addLineItem(array $item): void
    {
        $lineItems = $this->line_items ?? [];
        $lineItems[] = $item;
        
        $this->update(['line_items' => $lineItems]);
        $this->recalculateTotal();
    }

    public function removeLineItem(int $index): void
    {
        $lineItems = $this->line_items ?? [];
        
        if (isset($lineItems[$index])) {
            unset($lineItems[$index]);
            $this->update(['line_items' => array_values($lineItems)]);
            $this->recalculateTotal();
        }
    }

    public function recalculateTotal(): void
    {
        $lineItems = $this->line_items ?? [];
        $subtotal = collect($lineItems)->sum('amount');
        $taxAmount = $subtotal * ($this->tax_rate / 100);
        $total = $subtotal + $taxAmount - $this->discount_amount;

        $this->update([
            'subtotal' => $subtotal,
            'tax_amount' => $taxAmount,
            'total_amount' => $total
        ]);
    }

    public function duplicate(): self
    {
        $newInvoice = $this->replicate();
        $newInvoice->invoice_number = $this->generateInvoiceNumber();
        $newInvoice->status = 'draft';
        $newInvoice->sent_at = null;
        $newInvoice->paid_at = null;
        $newInvoice->issue_date = now()->toDateString();
        $newInvoice->due_date = now()->addDays(30)->toDateString();
        $newInvoice->save();

        return $newInvoice;
    }

    private function generateInvoiceNumber(): string
    {
        $prefix = 'INV-' . date('Y') . '-';
        $lastInvoice = static::where('invoice_number', 'like', $prefix . '%')
            ->orderBy('invoice_number', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastNumber = intval(substr($lastInvoice->invoice_number, strlen($prefix)));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
