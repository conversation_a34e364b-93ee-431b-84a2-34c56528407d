<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('automation_workflows', function (Blueprint $table) {
            if (!Schema::hasColumn('automation_workflows', 'deleted_at')) {
                $table->softDeletes();
            }
        });
    }

    public function down(): void
    {
        Schema::table('automation_workflows', function (Blueprint $table) {
            if (Schema::hasColumn('automation_workflows', 'deleted_at')) {
                $table->dropSoftDeletes();
            }
        });
    }
};
