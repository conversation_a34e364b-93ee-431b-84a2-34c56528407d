<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class DemoUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create demo admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'status' => 'active',
                'is_available' => true,
            ]
        );

        // Create demo manager user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Studio',
                'last_name' => 'Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'status' => 'active',
                'is_available' => true,
            ]
        );

        // Create demo engineer user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Audio',
                'last_name' => 'Engineer',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'status' => 'active',
                'is_available' => true,
            ]
        );
    }
}
