@extends('layouts.app')

@section('title', 'Create Automation Workflow')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Create Automation Workflow</h1>
                    <p class="mt-1 text-sm text-gray-600">Set up automated actions for your studio processes</p>
                </div>
                <div>
                    <a href="{{ route('automation.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Back to Workflows
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form action="{{ route('automation.store') }}" method="POST" class="space-y-6">
            @csrf
            
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                </div>
                <div class="p-6 space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">Workflow Name</label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-300 @enderror" 
                               required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="3" 
                                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="trigger_type" class="block text-sm font-medium text-gray-700">Trigger Event</label>
                            <select name="trigger_type" id="trigger_type" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('trigger_type') border-red-300 @enderror" 
                                    required>
                                <option value="">Select Trigger</option>
                                @foreach($triggerTypes as $key => $label)
                                    <option value="{{ $key }}" {{ old('trigger_type') === $key ? 'selected' : '' }}>
                                        {{ $label }}
                                    </option>
                                @endforeach
                            </select>
                            @error('trigger_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <div class="mt-1">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Active</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trigger Conditions -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Trigger Conditions</h3>
                    <p class="mt-1 text-sm text-gray-600">Optional conditions that must be met for the workflow to execute</p>
                </div>
                <div class="p-6">
                    <div id="trigger-conditions" class="space-y-4">
                        <!-- Conditions will be dynamically added based on trigger type -->
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Actions</h3>
                            <p class="mt-1 text-sm text-gray-600">Define what happens when the workflow is triggered</p>
                        </div>
                        <button type="button" onclick="addAction()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm font-medium">
                            Add Action
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="actions-container" class="space-y-4">
                        <!-- Actions will be dynamically added -->
                    </div>
                    @error('actions')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Submit -->
            <div class="flex justify-end space-x-3">
                <a href="{{ route('automation.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium">
                    Create Workflow
                </button>
            </div>
        </form>
    </main>
</div>
@endsection

@push('scripts')
<script>
    let actionCount = 0;
    const triggerTypes = @json($triggerTypes);
    const actionTypes = @json($actionTypes);
    const departments = @json($departments);
    const users = @json($users);

    // Add initial action
    document.addEventListener('DOMContentLoaded', function() {
        addAction();
    });

    function addAction() {
        actionCount++;
        const container = document.getElementById('actions-container');
        const actionDiv = document.createElement('div');
        actionDiv.className = 'border border-gray-200 rounded-lg p-4';
        actionDiv.id = `action-${actionCount}`;
        
        actionDiv.innerHTML = `
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-md font-medium text-gray-900">Action ${actionCount}</h4>
                <button type="button" onclick="removeAction(${actionCount})" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Action Type</label>
                    <select name="actions[${actionCount}][type]" onchange="updateActionConfig(${actionCount})" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                        <option value="">Select Action</option>
                        ${Object.entries(actionTypes).map(([key, label]) => `<option value="${key}">${label}</option>`).join('')}
                    </select>
                </div>
            </div>
            
            <div id="action-config-${actionCount}" class="mt-4">
                <!-- Action-specific configuration will be added here -->
            </div>
        `;
        
        container.appendChild(actionDiv);
    }

    function removeAction(actionId) {
        const actionDiv = document.getElementById(`action-${actionId}`);
        if (actionDiv) {
            actionDiv.remove();
        }
    }

    function updateActionConfig(actionId) {
        const select = document.querySelector(`select[name="actions[${actionId}][type]"]`);
        const configDiv = document.getElementById(`action-config-${actionId}`);
        const actionType = select.value;
        
        let configHtml = '';
        
        switch (actionType) {
            case 'create_task':
                configHtml = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Task Title</label>
                            <input type="text" name="actions[${actionId}][config][title]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Department</label>
                            <select name="actions[${actionId}][config][department_id]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="">Select Department</option>
                                ${departments.map(dept => `<option value="${dept.id}">${dept.name}</option>`).join('')}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Priority</label>
                            <select name="actions[${actionId}][config][priority]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Due Days</label>
                            <input type="number" name="actions[${actionId}][config][due_days]" min="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>
                `;
                break;
                
            case 'send_notification':
                configHtml = `
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Message</label>
                            <textarea name="actions[${actionId}][config][message]" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Channels (comma-separated)</label>
                            <input type="text" name="actions[${actionId}][config][channels]" placeholder="general, project-updates" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>
                `;
                break;
                
            case 'assign_user':
                configHtml = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">User</label>
                            <select name="actions[${actionId}][config][user_id]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                                <option value="">Select User</option>
                                ${users.map(user => `<option value="${user.id}">${user.first_name} ${user.last_name}</option>`).join('')}
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Role</label>
                            <input type="text" name="actions[${actionId}][config][role]" placeholder="e.g., Lead Engineer" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        </div>
                    </div>
                `;
                break;
                
            case 'send_email':
                configHtml = `
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">To Email</label>
                                <input type="email" name="actions[${actionId}][config][to]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Subject</label>
                                <input type="text" name="actions[${actionId}][config][subject]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Template</label>
                            <select name="actions[${actionId}][config][template]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <option value="default">Default</option>
                                <option value="booking_confirmation">Booking Confirmation</option>
                                <option value="deadline_reminder">Deadline Reminder</option>
                                <option value="project_update">Project Update</option>
                            </select>
                        </div>
                    </div>
                `;
                break;
                
            case 'update_status':
                configHtml = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Entity Type</label>
                            <select name="actions[${actionId}][config][entity]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                                <option value="">Select Entity</option>
                                <option value="project">Project</option>
                                <option value="task">Task</option>
                                <option value="booking">Booking</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">New Status</label>
                            <input type="text" name="actions[${actionId}][config][status]" placeholder="e.g., in_progress, completed" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                        </div>
                    </div>
                `;
                break;
        }
        
        configDiv.innerHTML = configHtml;
    }
</script>
@endpush
