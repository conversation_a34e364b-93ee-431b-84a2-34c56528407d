<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\AnalyticsEvent;

class TrackUserActivity
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // track user activity if authenticated
        if (auth()->check()) {
            $user = auth()->user();
            
            // update last active timestamp
            $user->updateLastActive();

            // track page views and API calls
            if ($request->isMethod('GET') || $request->isMethod('POST')) {
                AnalyticsEvent::track(
                    'page_view',
                    'user_activity',
                    null,
                    [
                        'url' => $request->url(),
                        'method' => $request->method(),
                        'route' => $request->route()?->getName(),
                        'user_agent' => $request->userAgent()
                    ],
                    null,
                    $user->id
                );
            }
        }

        return $response;
    }
}
