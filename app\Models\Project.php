<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'type',
        'status',
        'priority',
        'client_id',
        'project_manager_id',
        'budget',
        'total_cost',
        'start_date',
        'deadline',
        'completed_at',
        'progress_percentage',
        'requirements',
        'deliverables',
        'metadata'
    ];

    protected $casts = [
        'budget' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'start_date' => 'date',
        'deadline' => 'date',
        'completed_at' => 'date',
        'requirements' => 'array',
        'deliverables' => 'array',
        'metadata' => 'array'
    ];

    // relationships
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function projectManager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'project_manager_id');
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    public function teamMembers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'project_users')
                    ->withPivot('role', 'joined_at', 'left_at');
    }

    public function chatChannels(): HasMany
    {
        return $this->hasMany(ChatChannel::class);
    }

    public function fileUploads(): MorphMany
    {
        return $this->morphMany(FileUpload::class, 'uploadable');
    }

    public function analyticsEvents(): MorphMany
    {
        return $this->morphMany(AnalyticsEvent::class, 'trackable');
    }

    // scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeOverdue($query)
    {
        return $query->where('deadline', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeUpcoming($query, $days = 7)
    {
        return $query->where('deadline', '<=', now()->addDays($days))
                    ->where('deadline', '>=', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    // methods
    public function updateProgress(): void
    {
        $totalTasks = $this->tasks()->count();
        
        if ($totalTasks === 0) {
            $this->update(['progress_percentage' => 0]);
            return;
        }

        $completedTasks = $this->tasks()->where('status', 'completed')->count();
        $progress = round(($completedTasks / $totalTasks) * 100);
        
        $this->update(['progress_percentage' => $progress]);

        // auto-complete project if all tasks are done
        if ($progress === 100 && $this->status !== 'completed') {
            $this->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);
        }
    }

    public function isOverdue(): bool
    {
        return $this->deadline && 
               $this->deadline->isPast() && 
               !in_array($this->status, ['completed', 'cancelled']);
    }

    public function getDaysUntilDeadline(): ?int
    {
        if (!$this->deadline) {
            return null;
        }

        return now()->diffInDays($this->deadline, false);
    }

    public function addTeamMember(User $user, string $role = 'member'): void
    {
        $this->teamMembers()->attach($user->id, [
            'role' => $role,
            'joined_at' => now()
        ]);
    }

    public function removeTeamMember(User $user): void
    {
        $this->teamMembers()->updateExistingPivot($user->id, [
            'left_at' => now()
        ]);
    }

    public function calculateTotalCost(): void
    {
        $tasksCost = $this->tasks()
            ->whereNotNull('actual_hours')
            ->with('assignedUser')
            ->get()
            ->sum(function ($task) {
                return ($task->actual_hours ?? 0) * ($task->assignedUser->hourly_rate ?? 0);
            });

        $bookingsCost = $this->bookings()->sum('total_cost');
        
        $this->update(['total_cost' => $tasksCost + $bookingsCost]);
    }
}
