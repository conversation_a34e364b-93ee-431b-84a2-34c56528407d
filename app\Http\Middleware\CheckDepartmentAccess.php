<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckDepartmentAccess
{
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        $user = auth()->user();
        $departmentId = $request->route('department') ?? $request->input('department_id');

        // super admin can access all departments
        if ($user->hasPermission('*')) {
            return $next($request);
        }

        // check if user can access this department
        if ($departmentId && !$user->role->canAccessDepartment($departmentId)) {
            return response()->json([
                'success' => false,
                'message' => 'Department access denied'
            ], 403);
        }

        return $next($request);
    }
}
