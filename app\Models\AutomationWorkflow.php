<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AutomationWorkflow extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'trigger_type',
        'trigger_conditions',
        'actions',
        'is_active',
        'created_by',
        'metadata'
    ];

    protected $casts = [
        'trigger_conditions' => 'array',
        'actions' => 'array',
        'is_active' => 'boolean',
        'metadata' => 'array'
    ];

    /**
     * Get the user who created this workflow
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope for active workflows
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for workflows by trigger type
     */
    public function scopeByTriggerType($query, $type)
    {
        return $query->where('trigger_type', $type);
    }

    /**
     * Execute the workflow actions
     */
    public function execute($triggerData = [])
    {
        if (!$this->is_active) {
            return false;
        }

        // Check if trigger conditions are met
        if (!$this->checkTriggerConditions($triggerData)) {
            return false;
        }

        // Execute each action
        foreach ($this->actions as $action) {
            $this->executeAction($action, $triggerData);
        }

        return true;
    }

    /**
     * Check if trigger conditions are met
     */
    protected function checkTriggerConditions($triggerData)
    {
        if (empty($this->trigger_conditions)) {
            return true;
        }

        foreach ($this->trigger_conditions as $condition) {
            if (!$this->evaluateCondition($condition, $triggerData)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition
     */
    protected function evaluateCondition($condition, $triggerData)
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? '';

        $actualValue = data_get($triggerData, $field);

        switch ($operator) {
            case '=':
                return $actualValue == $value;
            case '!=':
                return $actualValue != $value;
            case '>':
                return $actualValue > $value;
            case '<':
                return $actualValue < $value;
            case '>=':
                return $actualValue >= $value;
            case '<=':
                return $actualValue <= $value;
            case 'contains':
                return str_contains(strtolower($actualValue), strtolower($value));
            case 'starts_with':
                return str_starts_with(strtolower($actualValue), strtolower($value));
            case 'ends_with':
                return str_ends_with(strtolower($actualValue), strtolower($value));
            default:
                return false;
        }
    }

    /**
     * Execute a single action
     */
    protected function executeAction($action, $triggerData)
    {
        $type = $action['type'] ?? '';

        switch ($type) {
            case 'send_email':
                $this->sendEmailAction($action, $triggerData);
                break;
            case 'create_task':
                $this->createTaskAction($action, $triggerData);
                break;
            case 'update_status':
                $this->updateStatusAction($action, $triggerData);
                break;
            case 'send_notification':
                $this->sendNotificationAction($action, $triggerData);
                break;
            case 'webhook':
                $this->webhookAction($action, $triggerData);
                break;
        }
    }

    /**
     * Send email action
     */
    protected function sendEmailAction($action, $triggerData)
    {
        // Implementation for sending email
        // This would integrate with your email service
    }

    /**
     * Create task action
     */
    protected function createTaskAction($action, $triggerData)
    {
        $taskData = $action['task_data'] ?? [];
        
        // Replace placeholders with actual data
        foreach ($taskData as $key => $value) {
            if (is_string($value)) {
                $taskData[$key] = $this->replacePlaceholders($value, $triggerData);
            }
        }

        Task::create($taskData);
    }

    /**
     * Update status action
     */
    protected function updateStatusAction($action, $triggerData)
    {
        $modelType = $action['model_type'] ?? '';
        $modelId = $action['model_id'] ?? data_get($triggerData, 'id');
        $status = $action['status'] ?? '';

        if ($modelType && $modelId && $status) {
            $model = app("App\\Models\\{$modelType}")->find($modelId);
            if ($model) {
                $model->update(['status' => $status]);
            }
        }
    }

    /**
     * Send notification action
     */
    protected function sendNotificationAction($action, $triggerData)
    {
        // Implementation for sending notifications
        // This would integrate with your notification system
    }

    /**
     * Webhook action
     */
    protected function webhookAction($action, $triggerData)
    {
        $url = $action['url'] ?? '';
        $method = $action['method'] ?? 'POST';
        $data = $action['data'] ?? [];

        if ($url) {
            // Replace placeholders in data
            foreach ($data as $key => $value) {
                if (is_string($value)) {
                    $data[$key] = $this->replacePlaceholders($value, $triggerData);
                }
            }

            // Make HTTP request
            // This would use Guzzle or Laravel's HTTP client
        }
    }

    /**
     * Replace placeholders in text with actual data
     */
    protected function replacePlaceholders($text, $data)
    {
        return preg_replace_callback('/\{\{([^}]+)\}\}/', function ($matches) use ($data) {
            return data_get($data, trim($matches[1]), $matches[0]);
        }, $text);
    }

    /**
     * Get available trigger types
     */
    public static function getTriggerTypes()
    {
        return [
            'project_created' => 'Project Created',
            'project_completed' => 'Project Completed',
            'task_created' => 'Task Created',
            'task_completed' => 'Task Completed',
            'booking_created' => 'Booking Created',
            'payment_received' => 'Payment Received',
            'client_created' => 'Client Created',
            'user_created' => 'User Created',
            'deadline_approaching' => 'Deadline Approaching',
            'overdue_task' => 'Overdue Task'
        ];
    }

    /**
     * Get available action types
     */
    public static function getActionTypes()
    {
        return [
            'send_email' => 'Send Email',
            'create_task' => 'Create Task',
            'update_status' => 'Update Status',
            'send_notification' => 'Send Notification',
            'webhook' => 'Call Webhook'
        ];
    }
}
