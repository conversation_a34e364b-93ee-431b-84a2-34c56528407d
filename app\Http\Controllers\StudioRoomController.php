<?php

namespace App\Http\Controllers;

use App\Models\StudioRoom;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class StudioRoomController extends Controller
{
    public function index(Request $request)
    {
        $query = StudioRoom::orderBy('name');

        // apply filters
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('available')) {
            $query->available();
        }

        if ($request->has('capacity')) {
            $query->withCapacity($request->capacity);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $rooms = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => [
                'rooms' => $rooms->items(),
                'pagination' => [
                    'current_page' => $rooms->currentPage(),
                    'last_page' => $rooms->lastPage(),
                    'per_page' => $rooms->perPage(),
                    'total' => $rooms->total()
                ]
            ]
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:recording,mixing,mastering,podcast,video,rehearsal',
            'capacity' => 'required|integer|min:1',
            'hourly_rate' => 'required|numeric|min:0',
            'equipment' => 'nullable|array',
            'features' => 'nullable|array',
            'requires_engineer' => 'boolean',
            'availability_schedule' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['slug'] = Str::slug($data['name']);
        $data['is_available'] = true;

        $room = StudioRoom::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Studio room created successfully',
            'data' => ['room' => $room]
        ], 201);
    }

    public function show(Request $request, StudioRoom $room)
    {
        $room->load(['bookings' => function ($query) {
            $query->upcoming()->with(['client', 'engineer']);
        }]);

        return response()->json([
            'success' => true,
            'data' => ['room' => $room]
        ]);
    }

    public function update(Request $request, StudioRoom $room)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'type' => 'sometimes|in:recording,mixing,mastering,podcast,video,rehearsal',
            'capacity' => 'sometimes|integer|min:1',
            'hourly_rate' => 'sometimes|numeric|min:0',
            'equipment' => 'nullable|array',
            'features' => 'nullable|array',
            'is_available' => 'sometimes|boolean',
            'requires_engineer' => 'sometimes|boolean',
            'availability_schedule' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        if (isset($data['name'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        $room->update($data);

        return response()->json([
            'success' => true,
            'message' => 'Studio room updated successfully',
            'data' => ['room' => $room->fresh()]
        ]);
    }

    public function destroy(Request $request, StudioRoom $room)
    {
        $user = $request->user();

        if (!$user->hasPermission('*')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // check if room has future bookings
        if ($room->bookings()->where('start_time', '>', now())->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete room with future bookings'
            ], 422);
        }

        $room->delete();

        return response()->json([
            'success' => true,
            'message' => 'Studio room deleted successfully'
        ]);
    }

    public function availability(Request $request, StudioRoom $room)
    {
        $validator = Validator::make($request->all(), [
            'start_time' => 'required|date|after:now',
            'end_time' => 'required|date|after:start_time'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $startTime = new \DateTime($request->start_time);
        $endTime = new \DateTime($request->end_time);

        $isAvailable = $room->isAvailableAt($startTime, $endTime);
        $cost = $room->getBookingRate($startTime, $endTime);

        return response()->json([
            'success' => true,
            'data' => [
                'available' => $isAvailable,
                'cost' => $cost,
                'duration_hours' => $startTime->diff($endTime)->h
            ]
        ]);
    }

    public function stats(Request $request, StudioRoom $room)
    {
        $days = $request->get('days', 30);
        
        $stats = [
            'utilization_rate' => $room->getUtilizationRate($days),
            'revenue' => $room->getRevenueForPeriod(
                now()->subDays($days),
                now()
            ),
            'total_bookings' => $room->bookings()
                ->where('start_time', '>=', now()->subDays($days))
                ->count(),
            'upcoming_bookings' => $room->bookings()
                ->upcoming()
                ->count(),
            'average_session_duration' => $this->getAverageSessionDuration($room, $days),
            'most_popular_times' => $this->getMostPopularTimes($room, $days)
        ];

        return response()->json([
            'success' => true,
            'data' => ['stats' => $stats]
        ]);
    }

    public function schedule(Request $request, StudioRoom $room)
    {
        $date = $request->get('date', now()->format('Y-m-d'));
        
        $bookings = $room->bookings()
            ->whereDate('start_time', $date)
            ->with(['client', 'engineer'])
            ->orderBy('start_time')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'date' => $date,
                'bookings' => $bookings,
                'room' => $room
            ]
        ]);
    }

    private function getAverageSessionDuration(StudioRoom $room, int $days): float
    {
        $bookings = $room->bookings()
            ->where('start_time', '>=', now()->subDays($days))
            ->whereIn('status', ['completed'])
            ->get();

        if ($bookings->isEmpty()) {
            return 0;
        }

        $totalHours = $bookings->sum(function ($booking) {
            return $booking->start_time->diffInHours($booking->end_time);
        });

        return $totalHours / $bookings->count();
    }

    private function getMostPopularTimes(StudioRoom $room, int $days): array
    {
        $bookings = $room->bookings()
            ->where('start_time', '>=', now()->subDays($days))
            ->whereIn('status', ['confirmed', 'completed'])
            ->get();

        $timeSlots = [];

        foreach ($bookings as $booking) {
            $hour = $booking->start_time->format('H');
            $timeSlots[$hour] = ($timeSlots[$hour] ?? 0) + 1;
        }

        arsort($timeSlots);

        return array_slice($timeSlots, 0, 5, true);
    }
}
