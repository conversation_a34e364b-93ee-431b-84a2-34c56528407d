@extends('layouts.app')

@section('title', 'Create Invoice')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Create Invoice</h1>
                <a href="{{ route('accounting.invoices.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Invoices
                </a>
            </div>

            <div class="card shadow">
                <div class="card-body">
                    <form action="{{ route('accounting.invoices.store') }}" method="POST" id="invoiceForm">
                        @csrf
                        
                        <div class="row">
                            <!-- Invoice Details -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Invoice Details</h5>
                                
                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                                    <select class="form-select @error('client_id') is-invalid @enderror" id="client_id" name="client_id" required>
                                        <option value="">Select Client</option>
                                        @foreach($clients as $client)
                                        <option value="{{ $client->id }}" {{ old('client_id') == $client->id ? 'selected' : '' }}>
                                            {{ $client->name }}
                                        </option>
                                        @endforeach
                                    </select>
                                    @error('client_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="invoice_date" class="form-label">Invoice Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('invoice_date') is-invalid @enderror" 
                                                   id="invoice_date" name="invoice_date" value="{{ old('invoice_date', date('Y-m-d')) }}" required>
                                            @error('invoice_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="due_date" class="form-label">Due Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control @error('due_date') is-invalid @enderror" 
                                                   id="due_date" name="due_date" value="{{ old('due_date', date('Y-m-d', strtotime('+30 days'))) }}" required>
                                            @error('due_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="project_id" class="form-label">Related Project</label>
                                    <select class="form-select @error('project_id') is-invalid @enderror" id="project_id" name="project_id">
                                        <option value="">Select Project (Optional)</option>
                                        @foreach($projects as $project)
                                        <option value="{{ $project->id }}" {{ old('project_id') == $project->id ? 'selected' : '' }}>
                                            {{ $project->name }}
                                        </option>
                                        @endforeach
                                    </select>
                                    @error('project_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes" name="notes" rows="4" 
                                              placeholder="Additional notes or terms">{{ old('notes') }}</textarea>
                                    @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Invoice Items -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Invoice Items</h5>
                                
                                <div id="invoice-items">
                                    <div class="invoice-item border rounded p-3 mb-3">
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="mb-3">
                                                    <label class="form-label">Description <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" name="items[0][description]" 
                                                           placeholder="Service or product description" required>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">Quantity</label>
                                                    <input type="number" class="form-control quantity-input" name="items[0][quantity]" 
                                                           value="1" min="1" step="0.01" onchange="calculateItemTotal(this)">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">Rate ($)</label>
                                                    <input type="number" class="form-control rate-input" name="items[0][rate]" 
                                                           step="0.01" min="0" onchange="calculateItemTotal(this)">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="mb-3">
                                                    <label class="form-label">Amount ($)</label>
                                                    <input type="number" class="form-control amount-input" name="items[0][amount]" 
                                                           step="0.01" readonly>
                                                </div>
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)">
                                            <i class="fas fa-trash"></i> Remove Item
                                        </button>
                                    </div>
                                </div>

                                <button type="button" class="btn btn-outline-primary mb-3" onclick="addItem()">
                                    <i class="fas fa-plus"></i> Add Item
                                </button>

                                <!-- Totals -->
                                <div class="border-top pt-3">
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>Subtotal:</strong>
                                        </div>
                                        <div class="col-6 text-end">
                                            <span id="subtotal">$0.00</span>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <label for="tax_rate" class="form-label">Tax Rate (%):</label>
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control form-control-sm" id="tax_rate" 
                                                   name="tax_rate" value="{{ old('tax_rate', 0) }}" min="0" max="100" 
                                                   step="0.01" onchange="calculateTotal()">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <strong>Tax Amount:</strong>
                                        </div>
                                        <div class="col-6 text-end">
                                            <span id="tax-amount">$0.00</span>
                                        </div>
                                    </div>
                                    <div class="row border-top pt-2">
                                        <div class="col-6">
                                            <h5><strong>Total:</strong></h5>
                                        </div>
                                        <div class="col-6 text-end">
                                            <h5><strong id="total">$0.00</strong></h5>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" id="subtotal_amount" name="subtotal_amount" value="0">
                                <input type="hidden" id="tax_amount" name="tax_amount" value="0">
                                <input type="hidden" id="total_amount" name="total_amount" value="0">
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('accounting.invoices.index') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" name="action" value="draft" class="btn btn-outline-primary">Save as Draft</button>
                                    <button type="submit" name="action" value="send" class="btn btn-primary">Create & Send</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let itemIndex = 1;

function addItem() {
    const container = document.getElementById('invoice-items');
    const itemHtml = `
        <div class="invoice-item border rounded p-3 mb-3">
            <div class="row">
                <div class="col-12">
                    <div class="mb-3">
                        <label class="form-label">Description <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="items[${itemIndex}][description]" 
                               placeholder="Service or product description" required>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">Quantity</label>
                        <input type="number" class="form-control quantity-input" name="items[${itemIndex}][quantity]" 
                               value="1" min="1" step="0.01" onchange="calculateItemTotal(this)">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">Rate ($)</label>
                        <input type="number" class="form-control rate-input" name="items[${itemIndex}][rate]" 
                               step="0.01" min="0" onchange="calculateItemTotal(this)">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="mb-3">
                        <label class="form-label">Amount ($)</label>
                        <input type="number" class="form-control amount-input" name="items[${itemIndex}][amount]" 
                               step="0.01" readonly>
                    </div>
                </div>
            </div>
            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(this)">
                <i class="fas fa-trash"></i> Remove Item
            </button>
        </div>
    `;
    container.insertAdjacentHTML('beforeend', itemHtml);
    itemIndex++;
}

function removeItem(button) {
    const container = document.getElementById('invoice-items');
    if (container.children.length > 1) {
        button.closest('.invoice-item').remove();
        calculateTotal();
    }
}

function calculateItemTotal(input) {
    const item = input.closest('.invoice-item');
    const quantity = parseFloat(item.querySelector('.quantity-input').value) || 0;
    const rate = parseFloat(item.querySelector('.rate-input').value) || 0;
    const amount = quantity * rate;
    
    item.querySelector('.amount-input').value = amount.toFixed(2);
    calculateTotal();
}

function calculateTotal() {
    let subtotal = 0;
    
    document.querySelectorAll('.amount-input').forEach(input => {
        subtotal += parseFloat(input.value) || 0;
    });
    
    const taxRate = parseFloat(document.getElementById('tax_rate').value) || 0;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;
    
    document.getElementById('subtotal').textContent = '$' + subtotal.toFixed(2);
    document.getElementById('tax-amount').textContent = '$' + taxAmount.toFixed(2);
    document.getElementById('total').textContent = '$' + total.toFixed(2);
    
    document.getElementById('subtotal_amount').value = subtotal.toFixed(2);
    document.getElementById('tax_amount').value = taxAmount.toFixed(2);
    document.getElementById('total_amount').value = total.toFixed(2);
}

// Form validation
document.getElementById('invoiceForm').addEventListener('submit', function(e) {
    const clientId = document.getElementById('client_id').value;
    const invoiceDate = document.getElementById('invoice_date').value;
    const dueDate = document.getElementById('due_date').value;
    
    if (!clientId || !invoiceDate || !dueDate) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    // Check if at least one item exists
    const items = document.querySelectorAll('.invoice-item');
    if (items.length === 0) {
        e.preventDefault();
        alert('Please add at least one invoice item.');
        return false;
    }
    
    // Validate each item
    let hasValidItem = false;
    items.forEach(item => {
        const description = item.querySelector('input[name*="[description]"]').value.trim();
        const rate = parseFloat(item.querySelector('.rate-input').value) || 0;
        
        if (description && rate > 0) {
            hasValidItem = true;
        }
    });
    
    if (!hasValidItem) {
        e.preventDefault();
        showAlert('Please ensure at least one item has a description and rate.', 'Validation Error', 'warning');
        return false;
    }
});

// Auto-calculate due date when invoice date changes
document.getElementById('invoice_date').addEventListener('change', function() {
    const invoiceDate = new Date(this.value);
    const dueDate = new Date(invoiceDate);
    dueDate.setDate(dueDate.getDate() + 30);
    
    document.getElementById('due_date').value = dueDate.toISOString().split('T')[0];
});
</script>
@endpush
