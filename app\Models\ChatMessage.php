<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ChatMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'channel_id',
        'user_id',
        'message',
        'type',
        'attachments',
        'reply_to',
        'is_edited',
        'edited_at',
        'reactions'
    ];

    protected $casts = [
        'attachments' => 'array',
        'is_edited' => 'boolean',
        'edited_at' => 'datetime',
        'reactions' => 'array'
    ];

    // relationships
    public function channel(): BelongsTo
    {
        return $this->belongsTo(ChatChannel::class, 'channel_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function replyTo(): BelongsTo
    {
        return $this->belongsTo(ChatMessage::class, 'reply_to');
    }

    // scopes
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    public function scopeWithAttachments($query)
    {
        return $query->whereNotNull('attachments');
    }

    // methods
    public function edit(string $newMessage): void
    {
        $this->update([
            'message' => $newMessage,
            'is_edited' => true,
            'edited_at' => now()
        ]);
    }

    public function addReaction(User $user, string $emoji): void
    {
        $reactions = $this->reactions ?? [];
        
        if (!isset($reactions[$emoji])) {
            $reactions[$emoji] = [];
        }

        if (!in_array($user->id, $reactions[$emoji])) {
            $reactions[$emoji][] = $user->id;
        }

        $this->update(['reactions' => $reactions]);
    }

    public function removeReaction(User $user, string $emoji): void
    {
        $reactions = $this->reactions ?? [];
        
        if (isset($reactions[$emoji])) {
            $reactions[$emoji] = array_filter($reactions[$emoji], function ($userId) use ($user) {
                return $userId !== $user->id;
            });

            if (empty($reactions[$emoji])) {
                unset($reactions[$emoji]);
            }
        }

        $this->update(['reactions' => $reactions]);
    }

    public function hasReaction(User $user, string $emoji): bool
    {
        $reactions = $this->reactions ?? [];
        return isset($reactions[$emoji]) && in_array($user->id, $reactions[$emoji]);
    }

    public function getReactionCount(string $emoji): int
    {
        $reactions = $this->reactions ?? [];
        return count($reactions[$emoji] ?? []);
    }

    public function canBeEditedBy(User $user): bool
    {
        // users can edit their own messages within 15 minutes
        return $this->user_id === $user->id && 
               $this->created_at->diffInMinutes(now()) <= 15;
    }

    public function canBeDeletedBy(User $user): bool
    {
        // users can delete their own messages or channel admins can delete any
        return $this->user_id === $user->id || 
               $this->channel->isAdmin($user);
    }

    protected static function boot()
    {
        parent::boot();

        static::created(function ($message) {
            // update channel's last message timestamp
            $message->channel->updateLastMessage();
        });
    }
}
