@extends('layouts.app')

@push('styles')
<style>
    /* Dashboard Layout Styles */
    .dashboard-container {
        height: 100vh;
        overflow: hidden;
    }

    .sidebar-container {
        flex-shrink: 0;
    }

    .main-content-container {
        flex: 1;
        min-width: 0; /* Prevents flex item from overflowing */
    }

    @media (max-width: 1023px) {
        .sidebar-container {
            position: fixed;
            z-index: 50;
            height: 100vh;
            top: 0;
            left: 0;
        }

        .main-content-container {
            width: 100%;
        }
    }
</style>
@endpush

@section('content')
<div class="flex dashboard-container bg-gray-50">
    <!-- Sidebar -->
    <div class="sidebar-container">
        @include('partials.sidebar')
    </div>

    <!-- Main Content -->
    <div class="main-content-container flex flex-col overflow-hidden">
        <!-- Top Navigation -->
        @include('partials.topnav')

        <!-- Page Content -->
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                @yield('dashboard-content')
            </div>
        </main>
    </div>
</div>
@endsection
