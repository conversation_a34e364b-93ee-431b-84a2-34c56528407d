<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('expense_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->string('color_code', 7)->default('#6B7280');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->string('description');
            $table->decimal('amount', 12, 2);
            $table->date('expense_date');
            $table->foreignId('category_id')->constrained('expense_categories')->onDelete('cascade');
            $table->foreignId('recorded_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('project_id')->nullable()->constrained('projects')->onDelete('set null');
            
            $table->string('vendor')->nullable();
            $table->string('receipt_number')->nullable();
            $table->decimal('tax_amount', 12, 2)->default(0);
            $table->boolean('is_billable')->default(false);
            $table->text('notes')->nullable();
            $table->json('attachments')->nullable(); // File paths for receipts
            
            $table->timestamps();
            
            $table->index(['category_id', 'expense_date']);
            $table->index(['project_id', 'is_billable']);
            $table->index('expense_date');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('expenses');
        Schema::dropIfExists('expense_categories');
    }
};
