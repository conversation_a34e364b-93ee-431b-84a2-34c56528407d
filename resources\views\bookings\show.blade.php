@extends('layouts.app')

@section('title', 'Booking Details')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Booking #{{ $booking->booking_number ?? $booking->id }}</h1>
                    <span class="badge bg-{{ 
                        $booking->status == 'confirmed' ? 'success' : 
                        ($booking->status == 'in_progress' ? 'warning' : 
                        ($booking->status == 'completed' ? 'info' : 
                        ($booking->status == 'cancelled' ? 'danger' : 'secondary'))) 
                    }} fs-6 mt-1">
                        {{ ucfirst(str_replace('_', ' ', $booking->status)) }}
                    </span>
                </div>
                <div class="btn-group">
                    <a href="{{ route('bookings.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Bookings
                    </a>
                    <a href="{{ route('bookings.edit', $booking) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Booking
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i> Actions
                        </button>
                        <ul class="dropdown-menu">
                            @if($booking->status == 'pending')
                            <li><a class="dropdown-item" href="#" onclick="confirmBooking({{ $booking->id }})">
                                <i class="fas fa-check text-success"></i> Confirm Booking
                            </a></li>
                            @endif
                            @if($booking->status == 'confirmed' && $booking->start_time->isToday())
                            <li><a class="dropdown-item" href="#" onclick="checkIn({{ $booking->id }})">
                                <i class="fas fa-sign-in-alt text-info"></i> Check In
                            </a></li>
                            @endif
                            @if($booking->status == 'in_progress')
                            <li><a class="dropdown-item" href="#" onclick="checkOut({{ $booking->id }})">
                                <i class="fas fa-sign-out-alt text-warning"></i> Check Out
                            </a></li>
                            @endif
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="cancelBooking({{ $booking->id }})">
                                <i class="fas fa-times text-danger"></i> Cancel Booking
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Booking Details -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Booking Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Studio Room:</strong></td>
                                            <td>
                                                @if($booking->studioRoom)
                                                <a href="{{ route('studio-rooms.show', $booking->studioRoom) }}">
                                                    {{ $booking->studioRoom->name }}
                                                </a>
                                                <br><small class="text-muted">{{ ucfirst($booking->studioRoom->type) }}</small>
                                                @else
                                                <span class="text-muted">Room not assigned</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Date:</strong></td>
                                            <td>{{ $booking->start_time->format('l, F j, Y') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Time:</strong></td>
                                            <td>
                                                {{ $booking->start_time->format('g:i A') }} - {{ $booking->end_time->format('g:i A') }}
                                                <br><small class="text-muted">
                                                    Duration: {{ $booking->start_time->diffInHours($booking->end_time) }}h 
                                                    {{ $booking->start_time->diffInMinutes($booking->end_time) % 60 }}m
                                                </small>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Total Cost:</strong></td>
                                            <td class="text-success"><strong>${{ number_format($booking->total_cost ?? 0, 2) }}</strong></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Client:</strong></td>
                                            <td>
                                                @if($booking->client)
                                                <div>
                                                    <a href="{{ route('clients.show', $booking->client) }}">{{ $booking->client->name }}</a>
                                                </div>
                                                <small class="text-muted">{{ $booking->client->email }}</small>
                                                @if($booking->client->phone)
                                                <br><small class="text-muted">{{ $booking->client->phone }}</small>
                                                @endif
                                                @else
                                                <span class="text-muted">Walk-in client</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Engineer:</strong></td>
                                            <td>{{ $booking->engineer->name ?? 'No engineer assigned' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Project:</strong></td>
                                            <td>
                                                @if($booking->project)
                                                <a href="{{ route('projects.show', $booking->project) }}">{{ $booking->project->name }}</a>
                                                @else
                                                <span class="text-muted">No project linked</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Created:</strong></td>
                                            <td>{{ $booking->created_at->format('M d, Y g:i A') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            @if($booking->purpose)
                            <div class="mt-3">
                                <h6>Session Purpose</h6>
                                <p class="text-muted">{{ $booking->purpose }}</p>
                            </div>
                            @endif

                            @if($booking->special_requirements)
                            <div class="mt-3">
                                <h6>Special Requirements</h6>
                                <p class="text-muted">{{ $booking->special_requirements }}</p>
                            </div>
                            @endif

                            @if($booking->equipment_needed && count($booking->equipment_needed) > 0)
                            <div class="mt-3">
                                <h6>Additional Equipment</h6>
                                <div class="row">
                                    @foreach($booking->equipment_needed as $equipment)
                                    <div class="col-md-6 mb-1">
                                        <span class="badge bg-light text-dark">
                                            <i class="fas fa-microphone me-1"></i>{{ $equipment }}
                                        </span>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Session Timeline -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Session Timeline</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Booking Created</h6>
                                        <p class="timeline-text">{{ $booking->created_at->format('M d, Y g:i A') }}</p>
                                    </div>
                                </div>
                                
                                @if($booking->confirmed_at)
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Booking Confirmed</h6>
                                        <p class="timeline-text">{{ $booking->confirmed_at->format('M d, Y g:i A') }}</p>
                                    </div>
                                </div>
                                @endif

                                @if($booking->checked_in_at)
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-info"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Client Checked In</h6>
                                        <p class="timeline-text">{{ $booking->checked_in_at->format('M d, Y g:i A') }}</p>
                                        @if($booking->check_in_notes)
                                        <small class="text-muted">{{ $booking->check_in_notes }}</small>
                                        @endif
                                    </div>
                                </div>
                                @endif

                                @if($booking->checked_out_at)
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-warning"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Session Completed</h6>
                                        <p class="timeline-text">{{ $booking->checked_out_at->format('M d, Y g:i A') }}</p>
                                        @if($booking->check_out_notes)
                                        <small class="text-muted">{{ $booking->check_out_notes }}</small>
                                        @endif
                                    </div>
                                </div>
                                @endif

                                @if($booking->cancelled_at)
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-danger"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Booking Cancelled</h6>
                                        <p class="timeline-text">{{ $booking->cancelled_at->format('M d, Y g:i A') }}</p>
                                        @if($booking->cancellation_reason)
                                        <small class="text-muted">Reason: {{ $booking->cancellation_reason }}</small>
                                        @endif
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                @if($booking->status == 'pending')
                                <button class="btn btn-success" onclick="confirmBooking({{ $booking->id }})">
                                    <i class="fas fa-check"></i> Confirm Booking
                                </button>
                                @endif
                                
                                @if($booking->status == 'confirmed' && $booking->start_time->isToday())
                                <button class="btn btn-info" onclick="checkIn({{ $booking->id }})">
                                    <i class="fas fa-sign-in-alt"></i> Check In Client
                                </button>
                                @endif
                                
                                @if($booking->status == 'in_progress')
                                <button class="btn btn-warning" onclick="checkOut({{ $booking->id }})">
                                    <i class="fas fa-sign-out-alt"></i> Check Out Client
                                </button>
                                @endif
                                
                                <a href="{{ route('bookings.edit', $booking) }}" class="btn btn-outline-primary">
                                    <i class="fas fa-edit"></i> Edit Booking
                                </a>
                                
                                @if($booking->client)
                                <a href="{{ route('clients.show', $booking->client) }}" class="btn btn-outline-info">
                                    <i class="fas fa-user"></i> View Client
                                </a>
                                @endif
                                
                                @if($booking->studioRoom)
                                <a href="{{ route('studio-rooms.show', $booking->studioRoom) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-door-open"></i> View Room
                                </a>
                                @endif
                                
                                <button class="btn btn-outline-danger" onclick="cancelBooking({{ $booking->id }})">
                                    <i class="fas fa-times"></i> Cancel Booking
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Booking Summary -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Booking Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary">{{ $booking->start_time->diffInHours($booking->end_time) }}</h4>
                                        <small class="text-muted">Hours</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success">${{ number_format($booking->total_cost ?? 0, 2) }}</h4>
                                    <small class="text-muted">Total Cost</small>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                @if($booking->start_time->isFuture())
                                <h5 class="text-info">{{ $booking->start_time->diffForHumans() }}</h5>
                                <small class="text-muted">Session starts</small>
                                @elseif($booking->start_time->isToday())
                                <h5 class="text-warning">Today</h5>
                                <small class="text-muted">{{ $booking->start_time->format('g:i A') }}</small>
                                @else
                                <h5 class="text-muted">{{ $booking->start_time->diffForHumans() }}</h5>
                                <small class="text-muted">Session was</small>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Related Files -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Session Files</h5>
                        </div>
                        <div class="card-body">
                            @if($booking->files && $booking->files->count() > 0)
                            @foreach($booking->files as $file)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <i class="fas fa-file"></i> {{ $file->name }}
                                    <br><small class="text-muted">{{ $file->size_formatted }}</small>
                                </div>
                                <a href="{{ route('files.download', $file) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                            @endforeach
                            @else
                            <div class="text-center py-3">
                                <i class="fas fa-file fa-2x text-muted mb-2"></i>
                                <p class="text-muted">No files uploaded yet</p>
                                <a href="{{ route('files.upload', ['booking' => $booking->id]) }}" class="btn btn-sm btn-primary">
                                    Upload Files
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include modals from index view -->
@include('bookings.partials.modals')
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}

.timeline-title {
    margin-bottom: 5px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 0;
    color: #6c757d;
}
</style>
@endpush

@push('scripts')
<script>
async function confirmBooking(bookingId) {
    const confirmed = await showConfirm('Are you sure you want to confirm this booking?', 'Confirm Booking', 'info');
    if (confirmed) {
        window.location.href = `/bookings/${bookingId}/confirm`;
    }
}

function checkIn(bookingId) {
    showConfirm(
        'Are you ready to check in this booking?',
        'Check In Booking',
        'info'
    ).then(confirmed => {
        if (confirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/bookings/${bookingId}/check-in`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';

            form.appendChild(csrfToken);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function checkOut(bookingId) {
    showConfirm(
        'Are you ready to check out this booking? This will mark the session as completed.',
        'Check Out Booking',
        'warning'
    ).then(confirmed => {
        if (confirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/bookings/${bookingId}/check-out`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';

            form.appendChild(csrfToken);
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function cancelBooking(bookingId) {
    showConfirm(
        'Are you sure you want to cancel this booking? This action cannot be undone.',
        'Cancel Booking',
        'danger'
    ).then(confirmed => {
        if (confirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/bookings/${bookingId}/cancel`;

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';

            form.appendChild(csrfToken);
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
@endpush
