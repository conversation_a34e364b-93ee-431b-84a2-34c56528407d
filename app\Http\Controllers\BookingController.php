<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\StudioRoom;
use App\Models\Client;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        
        $query = Booking::with(['client', 'studioRoom', 'engineer', 'project'])
            ->orderBy('start_time', 'desc');

        // filter based on user permissions
        if (!$user->hasPermission('*')) {
            $query->where('engineer_id', $user->id);
        }

        // apply filters
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('studio_room_id') && $request->studio_room_id) {
            $query->where('studio_room_id', $request->studio_room_id);
        }

        // Handle frontend parameter format
        if ($request->has('room') && $request->room) {
            $query->where('studio_room_id', $request->room);
        }

        if ($request->has('client_id') && $request->client_id) {
            $query->where('client_id', $request->client_id);
        }

        // Handle frontend parameter format
        if ($request->has('client') && $request->client) {
            $query->where('client_id', $request->client);
        }

        if ($request->has('date') && $request->date) {
            $query->whereDate('start_time', $request->date);
        }

        // Handle date range filters
        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('start_time', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('start_time', '<=', $request->date_to);
        }

        if ($request->has('upcoming') && $request->upcoming) {
            $query->where('start_time', '>=', now());
        }

        $bookings = $query->paginate($request->get('per_page', 15));

        // Calculate summary data
        $baseQuery = Booking::query();
        if (!$user->hasPermission('*')) {
            $baseQuery->where('engineer_id', $user->id);
        }

        $summary = [
            'total_bookings' => $baseQuery->count(),
            'today_sessions' => $baseQuery->whereDate('start_time', today())->count(),
            'this_week' => $baseQuery->whereBetween('start_time', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'revenue' => $baseQuery->where('status', 'completed')->sum('total_cost')
        ];

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => [
                    'bookings' => $bookings->items(),
                    'pagination' => [
                        'current_page' => $bookings->currentPage(),
                        'last_page' => $bookings->lastPage(),
                        'per_page' => $bookings->perPage(),
                        'total' => $bookings->total()
                    ],
                    'summary' => $summary
                ]
            ]);
        }

        // Get rooms for filter dropdown
        $rooms = StudioRoom::orderBy('name')->get();
        $clients = Client::orderBy('name')->get();
        $engineers = \App\Models\User::whereHas('role', function($q) {
            $q->where('name', 'engineer');
        })->orderBy('first_name')->get();

        // Return view for web requests
        return view('bookings.index', compact('bookings', 'rooms', 'clients', 'engineers'));
    }

    public function create()
    {
        $rooms = StudioRoom::orderBy('name')->get();
        $clients = Client::orderBy('name')->get();
        $projects = \App\Models\Project::orderBy('title')->get();
        $engineers = \App\Models\User::whereHas('role', function($q) {
            $q->where('name', 'engineer');
        })->orderBy('first_name')->get();

        return view('bookings.create', compact('rooms', 'clients', 'projects', 'engineers'));
    }

    public function edit(Booking $booking)
    {
        $rooms = StudioRoom::orderBy('name')->get();
        $clients = Client::orderBy('name')->get();
        $projects = \App\Models\Project::orderBy('title')->get();
        $engineers = \App\Models\User::whereHas('role', function($q) {
            $q->where('name', 'engineer');
        })->orderBy('first_name')->get();

        return view('bookings.edit', compact('booking', 'rooms', 'clients', 'projects', 'engineers'));
    }

    public function store(Request $request)
    {
        // Handle both frontend parameter formats
        $data = $request->all();

        // Map frontend parameters to expected format
        if (isset($data['start_date']) && !isset($data['booking_date'])) {
            $data['booking_date'] = $data['start_date'];
        }
        if (isset($data['duration']) && !isset($data['duration_hours'])) {
            $data['duration_hours'] = $data['duration'];
        }

        $validator = Validator::make($data, [
            'client_id' => 'required|exists:clients,id',
            'studio_room_id' => 'required|exists:studio_rooms,id',
            'project_id' => 'nullable|exists:projects,id',
            'engineer_id' => 'nullable|exists:users,id',
            'booking_date' => 'required|date|after_or_equal:today',
            'start_time' => 'required',
            'duration_hours' => 'required|numeric|min:0.5|max:24',
            'session_type' => 'required|in:recording,mixing,mastering,rehearsal,other',
            'notes' => 'nullable|string',
            'equipment_requests' => 'nullable|array',
            'special_requirements' => 'nullable|array',
            'requires_setup' => 'boolean',
            'setup_time_minutes' => 'nullable|integer|min:0',
            'total_cost' => 'required|numeric|min:0'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        // Convert date and time to datetime objects
        $bookingDate = $data['booking_date'];
        $startTime = $data['start_time'];
        $durationHours = $data['duration_hours'];

        $startDateTime = new \DateTime($bookingDate . ' ' . $startTime);
        $endDateTime = clone $startDateTime;
        $endDateTime->add(new \DateInterval('PT' . ($durationHours * 60) . 'M'));

        // Update data with proper datetime fields
        $data['start_time'] = $startDateTime;
        $data['end_time'] = $endDateTime;

        // Remove the form-specific fields
        unset($data['booking_date'], $data['duration_hours']);

        // check room availability
        $room = StudioRoom::find($data['studio_room_id']);

        if (!$room->isAvailableAt($startDateTime, $endDateTime)) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Studio room is not available at the requested time'
                ], 422);
            }
            return redirect()->back()->with('error', 'Studio room is not available at the requested time')->withInput();
        }

        // Set additional booking data
        $data['hourly_rate'] = $room->hourly_rate;
        $data['status'] = $data['status'] ?? 'pending';

        $booking = Booking::create($data);

        // track booking creation
        AnalyticsEvent::trackBookingEvent('booking_created', $booking, [], $booking->total_cost);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Booking created successfully',
                'data' => ['booking' => $booking->load(['client', 'studioRoom', 'engineer'])]
            ], 201);
        }

        return redirect()->route('bookings.show', $booking)->with('success', 'Booking created successfully!');
    }

    public function show(Request $request, Booking $booking)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $booking->engineer_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $booking->load(['client', 'studioRoom', 'engineer', 'project', 'equipmentBookings']);

        return response()->json([
            'success' => true,
            'data' => ['booking' => $booking]
        ]);
    }

    public function update(Request $request, Booking $booking)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $booking->engineer_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'start_time' => 'sometimes|date',
            'end_time' => 'sometimes|date|after:start_time',
            'status' => 'sometimes|in:pending,confirmed,in_progress,completed,cancelled,no_show',
            'notes' => 'nullable|string',
            'equipment_requests' => 'nullable|array',
            'special_requirements' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // recalculate cost if time changed
        if (isset($data['start_time']) || isset($data['end_time'])) {
            $startTime = new \DateTime($data['start_time'] ?? $booking->start_time);
            $endTime = new \DateTime($data['end_time'] ?? $booking->end_time);
            $data['total_cost'] = $booking->studioRoom->getBookingRate($startTime, $endTime);
        }

        $booking->update($data);

        // track booking update
        AnalyticsEvent::trackBookingEvent('booking_updated', $booking, $data);

        return response()->json([
            'success' => true,
            'message' => 'Booking updated successfully',
            'data' => ['booking' => $booking->fresh(['client', 'studioRoom', 'engineer'])]
        ]);
    }

    public function confirm(Request $request, Booking $booking)
    {
        if ($booking->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Booking cannot be confirmed'
            ], 422);
        }

        $booking->confirm();

        return response()->json([
            'success' => true,
            'message' => 'Booking confirmed successfully',
            'data' => ['booking' => $booking->fresh()]
        ]);
    }

    public function checkIn(Request $request, Booking $booking)
    {
        if ($booking->status !== 'confirmed') {
            return response()->json([
                'success' => false,
                'message' => 'Booking must be confirmed before check-in'
            ], 422);
        }

        $booking->checkIn();

        return response()->json([
            'success' => true,
            'message' => 'Checked in successfully',
            'data' => ['booking' => $booking->fresh()]
        ]);
    }

    public function checkOut(Request $request, Booking $booking)
    {
        if ($booking->status !== 'in_progress') {
            return response()->json([
                'success' => false,
                'message' => 'Booking must be in progress to check out'
            ], 422);
        }

        $booking->checkOut();

        return response()->json([
            'success' => true,
            'message' => 'Checked out successfully',
            'data' => ['booking' => $booking->fresh()]
        ]);
    }

    public function cancel(Request $request, Booking $booking)
    {
        if (!$booking->canBeModified()) {
            return response()->json([
                'success' => false,
                'message' => 'Booking cannot be cancelled'
            ], 422);
        }

        $booking->cancel();

        return response()->json([
            'success' => true,
            'message' => 'Booking cancelled successfully',
            'data' => ['booking' => $booking->fresh()]
        ]);
    }

    public function availability(Request $request)
    {
        // Handle both frontend parameter formats
        $data = $request->all();

        // Map frontend parameters to expected format
        if (isset($data['start_date']) && !isset($data['date'])) {
            $data['date'] = $data['start_date'];
        }
        if (isset($data['duration']) && !isset($data['duration_hours'])) {
            $data['duration_hours'] = $data['duration'];
        }

        $validator = Validator::make($data, [
            'studio_room_id' => 'required|exists:studio_rooms,id',
            'date' => 'required|date',
            'duration_hours' => 'required|numeric|min:0.5|max:24'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $room = StudioRoom::find($data['studio_room_id']);
        $date = $data['date'];
        $duration = $data['duration_hours'];

        // get available time slots
        $availableSlots = $this->getAvailableTimeSlots($room, $date, $duration);

        return response()->json([
            'success' => true,
            'data' => [
                'available_slots' => $availableSlots,
                'room' => $room,
                'total_slots' => count($availableSlots)
            ]
        ]);
    }

    private function getAvailableTimeSlots(StudioRoom $room, string $date, float $duration): array
    {
        $slots = [];
        $startHour = 9; // 9 AM
        $endHour = 22; // 10 PM
        $slotInterval = 0.5; // 30-minute intervals

        for ($hour = $startHour; $hour <= $endHour - $duration; $hour += $slotInterval) {
            $startTime = new \DateTime($date);
            $startTime->setTime(floor($hour), ($hour - floor($hour)) * 60);

            $endTime = clone $startTime;
            $endTime->add(new \DateInterval('PT' . ($duration * 60) . 'M'));

            if ($room->isAvailableAt($startTime, $endTime)) {
                $slots[] = [
                    'start_time' => $startTime->format('Y-m-d H:i:s'),
                    'end_time' => $endTime->format('Y-m-d H:i:s'),
                    'display_time' => $startTime->format('g:i A') . ' - ' . $endTime->format('g:i A'),
                    'cost' => $room->getBookingRate($startTime, $endTime)
                ];
            }
        }

        return $slots;
    }
}
