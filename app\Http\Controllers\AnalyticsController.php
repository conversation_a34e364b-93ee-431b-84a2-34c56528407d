<?php

namespace App\Http\Controllers;

use App\Models\AnalyticsEvent;
use App\Models\Project;
use App\Models\Task;
use App\Models\Booking;
use App\Models\User;
use App\Models\Client;
use App\Models\Department;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_analytics')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return $this->overview($request);
        }

        // Return analytics dashboard view
        return view('analytics.index');
    }

    public function overview(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_analytics')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);
        $endDate = now();

        $projectAnalytics = $this->getProjectAnalytics($startDate, $endDate);
        $taskAnalytics = $this->getTaskAnalytics($startDate, $endDate);
        $bookingAnalytics = $this->getBookingAnalytics($startDate, $endDate);
        $revenueAnalytics = $this->getRevenueAnalytics($startDate, $endDate);

        $overview = [
            // Flatten the data structure for easier frontend access
            'total_revenue' => $revenueAnalytics['total_revenue'],
            'total_bookings' => $bookingAnalytics['total_bookings'],
            'completed_sessions' => $bookingAnalytics['completed_sessions'],
            'active_projects' => $projectAnalytics['active_projects'] ?? 0,
            'completed_projects' => $projectAnalytics['completed_projects'] ?? 0,
            'studio_utilization' => $bookingAnalytics['utilization_rate'],
            'revenue_growth' => $revenueAnalytics['revenue_growth'] ?? 0,
            'bookings_growth' => $this->getBookingsGrowth($startDate, $endDate),
            'total_hours_booked' => $this->getTotalHoursBooked($startDate, $endDate),

            // Keep nested structure for detailed analytics
            'projects' => $projectAnalytics,
            'tasks' => $taskAnalytics,
            'bookings' => $bookingAnalytics,
            'revenue' => $revenueAnalytics,
            'performance' => $this->getPerformanceAnalytics($startDate, $endDate),
            'trends' => $this->getTrendAnalytics($startDate, $endDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $overview
        ]);
    }



    private function getBookingsGrowth($startDate, $endDate): float
    {
        $currentPeriodBookings = Booking::whereBetween('start_time', [$startDate, $endDate])->count();
        $previousPeriodStart = $startDate->copy()->subDays($endDate->diffInDays($startDate));
        $previousPeriodBookings = Booking::whereBetween('start_time', [$previousPeriodStart, $startDate])->count();

        if ($previousPeriodBookings == 0) {
            return $currentPeriodBookings > 0 ? 100 : 0;
        }

        return round((($currentPeriodBookings - $previousPeriodBookings) / $previousPeriodBookings) * 100, 2);
    }

    private function getTotalHoursBooked($startDate, $endDate): float
    {
        $bookings = Booking::whereBetween('start_time', [$startDate, $endDate])
            ->whereIn('status', ['completed', 'confirmed', 'in_progress'])
            ->get();

        $totalMinutes = $bookings->sum(function ($booking) {
            return $booking->start_time->diffInMinutes($booking->end_time);
        });

        return round($totalMinutes / 60, 2);
    }

    public function departmentPerformance(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        $departments = Department::with(['users', 'tasks'])
            ->active()
            ->get()
            ->map(function ($dept) use ($startDate) {
                $tasks = $dept->tasks()->where('created_at', '>=', $startDate);
                
                return [
                    'id' => $dept->id,
                    'name' => $dept->name,
                    'color_code' => $dept->color_code,
                    'active_users' => $dept->users()->active()->count(),
                    'total_tasks' => $tasks->count(),
                    'completed_tasks' => $tasks->where('status', 'completed')->count(),
                    'overdue_tasks' => $tasks->where('due_date', '<', now())
                        ->whereNotIn('status', ['completed', 'cancelled'])->count(),
                    'avg_completion_time' => $this->getAvgCompletionTime($dept, $startDate),
                    'efficiency_score' => $this->calculateEfficiencyScore($dept, $startDate)
                ];
            });

        return response()->json([
            'success' => true,
            'data' => ['departments' => $departments]
        ]);
    }

    public function userPerformance(Request $request)
    {
        $period = $request->get('period', '30');
        $departmentId = $request->get('department_id');
        $startDate = now()->subDays($period);

        $query = User::with(['department', 'role'])
            ->active()
            ->has('assignedTasks');

        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        $users = $query->get()->map(function ($user) use ($startDate) {
            $tasks = $user->assignedTasks()->where('created_at', '>=', $startDate);
            
            return [
                'id' => $user->id,
                'name' => $user->full_name,
                'department' => $user->department->name,
                'role' => $user->role->name,
                'total_tasks' => $tasks->count(),
                'completed_tasks' => $tasks->where('status', 'completed')->count(),
                'in_progress_tasks' => $tasks->where('status', 'in_progress')->count(),
                'overdue_tasks' => $tasks->where('due_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled'])->count(),
                'avg_completion_time' => $this->getUserAvgCompletionTime($user, $startDate),
                'productivity_score' => $this->calculateProductivityScore($user, $startDate)
            ];
        });

        return response()->json([
            'success' => true,
            'data' => ['users' => $users]
        ]);
    }

    public function projectAnalytics(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        $analytics = [
            'project_types' => $this->getProjectTypeDistribution($startDate),
            'completion_rates' => $this->getProjectCompletionRates($startDate),
            'budget_analysis' => $this->getBudgetAnalysis($startDate),
            'timeline_analysis' => $this->getTimelineAnalysis($startDate),
            'client_satisfaction' => $this->getClientSatisfactionMetrics($startDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics
        ]);
    }

    public function revenueAnalytics(Request $request)
    {
        $period = $request->get('period', '30');
        $startDate = now()->subDays($period);

        $analytics = [
            'total_revenue' => $this->getTotalRevenue($startDate),
            'revenue_by_service' => $this->getRevenueByService($startDate),
            'revenue_by_client_type' => $this->getRevenueByClientType($startDate),
            'monthly_trends' => $this->getMonthlyRevenueTrends(),
            'studio_utilization' => $this->getStudioUtilizationRevenue($startDate),
            'forecasting' => $this->getRevenueForecast()
        ];

        return response()->json([
            'success' => true,
            'data' => $analytics
        ]);
    }

    private function getProjectAnalytics($startDate, $endDate): array
    {
        return [
            'total_created' => Project::whereBetween('created_at', [$startDate, $endDate])->count(),
            'completed' => Project::whereBetween('completed_at', [$startDate, $endDate])->count(),
            'active_projects' => Project::where('status', 'active')->count(),
            'completed_projects' => Project::where('status', 'completed')->count(),
            'in_progress' => Project::where('status', 'active')->count(),
            'overdue' => Project::overdue()->count(),
            'by_type' => Project::whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('type')
                ->selectRaw('type, COUNT(*) as count')
                ->pluck('count', 'type')
                ->toArray()
        ];
    }

    private function getTaskAnalytics($startDate, $endDate): array
    {
        return [
            'total_created' => Task::whereBetween('created_at', [$startDate, $endDate])->count(),
            'completed' => Task::whereBetween('completed_at', [$startDate, $endDate])->count(),
            'in_progress' => Task::where('status', 'in_progress')->count(),
            'overdue' => Task::overdue()->count(),
            'avg_completion_time' => $this->getOverallAvgCompletionTime($startDate)
        ];
    }

    private function getBookingAnalytics($startDate, $endDate): array
    {
        // Get all bookings without user permission filters for analytics
        $totalBookings = Booking::whereBetween('start_time', [$startDate, $endDate])->count();
        $completedSessions = Booking::whereBetween('start_time', [$startDate, $endDate])
            ->where('status', 'completed')->count();

        // If no bookings in date range, try getting all-time data
        if ($totalBookings === 0) {
            $totalBookings = Booking::count();
            $completedSessions = Booking::where('status', 'completed')->count();
        }

        return [
            'total_bookings' => $totalBookings,
            'completed_sessions' => $completedSessions,
            'cancellation_rate' => $this->getCancellationRate($startDate, $endDate),
            'avg_session_duration' => $this->getAvgSessionDuration($startDate, $endDate),
            'utilization_rate' => $this->getOverallUtilizationRate($startDate, $endDate)
        ];
    }

    private function getRevenueAnalytics($startDate, $endDate): array
    {
        // Get revenue from bookings without user permission filters
        $totalRevenue = Booking::whereBetween('start_time', [$startDate, $endDate])
            ->whereIn('status', ['completed', 'confirmed'])
            ->sum('total_cost');

        // If no revenue in date range, try getting recent revenue
        if ($totalRevenue == 0) {
            $totalRevenue = Booking::whereIn('status', ['completed', 'confirmed'])
                ->where('start_time', '>=', now()->subDays(90)) // Last 90 days
                ->sum('total_cost');
        }

        return [
            'total_revenue' => $totalRevenue,
            'avg_booking_value' => $this->getAvgBookingValue($startDate, $endDate),
            'revenue_growth' => $this->getRevenueGrowth($startDate, $endDate),
            'top_clients' => $this->getTopClientsByRevenue($startDate, $endDate)
        ];
    }

    private function getPerformanceAnalytics($startDate, $endDate): array
    {
        return [
            'project_completion_rate' => $this->getProjectCompletionRate($startDate, $endDate),
            'task_completion_rate' => $this->getTaskCompletionRate($startDate, $endDate),
            'on_time_delivery_rate' => $this->getOnTimeDeliveryRate($startDate, $endDate),
            'client_satisfaction_score' => $this->getClientSatisfactionScore($startDate, $endDate)
        ];
    }

    private function getTrendAnalytics($startDate, $endDate): array
    {
        return [
            'daily_projects' => $this->getDailyProjectTrends($startDate, $endDate),
            'daily_tasks' => $this->getDailyTaskTrends($startDate, $endDate),
            'daily_revenue' => $this->getDailyRevenueTrends($startDate, $endDate),
            'weekly_performance' => $this->getWeeklyPerformanceTrends($startDate, $endDate)
        ];
    }

    // Helper methods for calculations
    private function getAvgCompletionTime($department, $startDate): float
    {
        $completedTasks = $department->tasks()
            ->where('completed_at', '>=', $startDate)
            ->whereNotNull('started_at')
            ->whereNotNull('completed_at')
            ->get();

        if ($completedTasks->isEmpty()) {
            return 0;
        }

        $totalHours = $completedTasks->sum(function ($task) {
            return $task->started_at->diffInHours($task->completed_at);
        });

        return $totalHours / $completedTasks->count();
    }

    private function calculateEfficiencyScore($department, $startDate): float
    {
        $tasks = $department->tasks()->where('created_at', '>=', $startDate);
        $total = $tasks->count();
        
        if ($total === 0) {
            return 0;
        }

        $completed = $tasks->where('status', 'completed')->count();
        $onTime = $tasks->where('status', 'completed')
            ->where('completed_at', '<=', DB::raw('due_date'))->count();

        $completionRate = ($completed / $total) * 100;
        $onTimeRate = $total > 0 ? ($onTime / $total) * 100 : 0;

        return ($completionRate + $onTimeRate) / 2;
    }

    private function getUserAvgCompletionTime($user, $startDate): float
    {
        $completedTasks = $user->assignedTasks()
            ->where('completed_at', '>=', $startDate)
            ->whereNotNull('started_at')
            ->whereNotNull('completed_at')
            ->get();

        if ($completedTasks->isEmpty()) {
            return 0;
        }

        $totalHours = $completedTasks->sum(function ($task) {
            return $task->started_at->diffInHours($task->completed_at);
        });

        return $totalHours / $completedTasks->count();
    }

    private function calculateProductivityScore($user, $startDate): float
    {
        $tasks = $user->assignedTasks()->where('created_at', '>=', $startDate);
        $total = $tasks->count();
        
        if ($total === 0) {
            return 0;
        }

        $completed = $tasks->where('status', 'completed')->count();
        $onTime = $tasks->where('status', 'completed')
            ->where('completed_at', '<=', DB::raw('due_date'))->count();

        $completionRate = ($completed / $total) * 100;
        $onTimeRate = $total > 0 ? ($onTime / $total) * 100 : 0;

        return ($completionRate + $onTimeRate) / 2;
    }

    // Additional helper methods would be implemented here...
    // Due to length constraints, I'm showing the structure
}
