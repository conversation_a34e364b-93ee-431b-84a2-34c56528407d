<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'invoice_id',
        'recorded_by',
        'amount',
        'payment_method',
        'payment_date',
        'reference_number',
        'notes'
    ];

    protected $casts = [
        'payment_date' => 'date',
        'amount' => 'decimal:2'
    ];

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    public function recordedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recorded_by');
    }

    // Boot method to update invoice status when payment is created
    protected static function boot()
    {
        parent::boot();

        static::created(function ($payment) {
            $payment->invoice->updatePaymentStatus();
        });

        static::updated(function ($payment) {
            $payment->invoice->updatePaymentStatus();
        });

        static::deleted(function ($payment) {
            $payment->invoice->updatePaymentStatus();
        });
    }

    // Scopes
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('payment_date', now()->month)
                    ->whereYear('payment_date', now()->year);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('payment_date', now()->year);
    }

    public function scopeByMethod($query, string $method)
    {
        return $query->where('payment_method', $method);
    }

    // Accessors
    public function getFormattedAmountAttribute(): string
    {
        return '₦' . number_format($this->amount, 2);
    }

    public function getPaymentMethodLabelAttribute(): string
    {
        $labels = [
            'cash' => 'Cash',
            'bank_transfer' => 'Bank Transfer',
            'card' => 'Card Payment',
            'check' => 'Check',
            'other' => 'Other'
        ];

        return $labels[$this->payment_method] ?? $this->payment_method;
    }

    // Methods
    public function void(): bool
    {
        // Mark payment as voided and update invoice
        $this->update(['notes' => ($this->notes ?? '') . ' [VOIDED]']);
        $this->delete();
        
        return true;
    }

    public function refund(float $amount = null): Payment
    {
        $refundAmount = $amount ?? $this->amount;
        
        return static::create([
            'invoice_id' => $this->invoice_id,
            'recorded_by' => auth()->id(),
            'amount' => -$refundAmount,
            'payment_method' => $this->payment_method,
            'payment_date' => now()->toDateString(),
            'reference_number' => 'REFUND-' . $this->reference_number,
            'notes' => "Refund for payment #{$this->id}"
        ]);
    }
}
