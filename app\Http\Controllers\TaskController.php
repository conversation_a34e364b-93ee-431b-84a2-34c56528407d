<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\Project;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TaskController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        
        $query = Task::with(['project', 'assignedUser', 'department'])
            ->orderBy('created_at', 'desc');

        // filter based on user permissions
        if (!$user->hasPermission('*')) {
            $query->where(function ($q) use ($user) {
                $q->where('assigned_to', $user->id)
                  ->orWhere('department_id', $user->department_id)
                  ->orWhereHas('project', function ($projectQuery) use ($user) {
                      $projectQuery->where('project_manager_id', $user->id);
                  });
            });
        }

        // apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->filled('assigned_to')) {
            $query->where('assigned_to', $request->assigned_to);
        }

        if ($request->filled('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        if ($request->filled('project_id')) {
            $query->where('project_id', $request->project_id);
        }

        if ($request->filled('overdue')) {
            $query->overdue();
        }

        $tasks = $query->paginate($request->get('per_page', 15));

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            // Calculate summary data
            $baseQuery = Task::query();

            // Apply same permission filters for summary
            if (!$user->hasPermission('*')) {
                $baseQuery->where(function ($q) use ($user) {
                    $q->where('assigned_to', $user->id)
                      ->orWhere('department_id', $user->department_id)
                      ->orWhereHas('project', function ($projectQuery) use ($user) {
                          $projectQuery->where('project_manager_id', $user->id);
                      });
                });
            }

            $summary = [
                'total' => $baseQuery->count(),
                'pending' => $baseQuery->where('status', 'pending')->count(),
                'in_progress' => $baseQuery->where('status', 'in_progress')->count(),
                'completed' => $baseQuery->where('status', 'completed')->count(),
                'overdue' => $baseQuery->where('due_date', '<', now())->whereNotIn('status', ['completed', 'cancelled'])->count()
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'tasks' => $tasks->items(),
                    'summary' => $summary,
                    'pagination' => [
                        'current_page' => $tasks->currentPage(),
                        'last_page' => $tasks->lastPage(),
                        'per_page' => $tasks->perPage(),
                        'total' => $tasks->total()
                    ]
                ]
            ]);
        }

        // Get projects for filter dropdown
        $projects = Project::orderBy('title')->get();
        $users = \App\Models\User::orderBy('first_name')->get();
        $departments = \App\Models\Department::orderBy('name')->get();

        // Calculate summary data for server-side rendering
        $baseQuery = Task::query();

        // Apply same permission filters for summary
        if (!$user->hasPermission('*')) {
            $baseQuery->where(function ($q) use ($user) {
                $q->where('assigned_to', $user->id)
                  ->orWhere('department_id', $user->department_id)
                  ->orWhereHas('project', function ($projectQuery) use ($user) {
                      $projectQuery->where('project_manager_id', $user->id);
                  });
            });
        }

        $summary = [
            'total' => $baseQuery->count(),
            'pending' => $baseQuery->where('status', 'pending')->count(),
            'in_progress' => $baseQuery->where('status', 'in_progress')->count(),
            'completed' => $baseQuery->where('status', 'completed')->count(),
            'overdue' => $baseQuery->where('due_date', '<', now())->whereNotIn('status', ['completed', 'cancelled'])->count()
        ];

        // Return view for web requests
        return view('tasks.index', compact('tasks', 'projects', 'users', 'departments', 'summary'));
    }

    public function create()
    {
        $projects = Project::orderBy('title')->get();
        $users = \App\Models\User::orderBy('first_name')->get();
        $departments = \App\Models\Department::orderBy('name')->get();

        return view('tasks.create', compact('projects', 'users', 'departments'));
    }

    public function edit(Task $task)
    {
        $projects = Project::orderBy('title')->get();
        $users = \App\Models\User::orderBy('first_name')->get();
        $departments = \App\Models\Department::orderBy('name')->get();

        return view('tasks.edit', compact('task', 'projects', 'users', 'departments'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'project_id' => 'required|exists:projects,id',
            'assigned_to' => 'nullable|exists:users,id',
            'department_id' => 'nullable|exists:departments,id',
            'priority' => 'in:low,medium,high,urgent',
            'due_date' => 'nullable|date|after:now',
            'estimated_hours' => 'nullable|integer|min:1',
            'requirements' => 'nullable|array',
            'deliverables' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['assigned_by'] = $request->user()->id;
        $data['status'] = 'pending';

        $task = Task::create($data);

        // track task creation
        AnalyticsEvent::trackTaskEvent('task_created', $task);

        return response()->json([
            'success' => true,
            'message' => 'Task created successfully',
            'data' => ['task' => $task->load(['project', 'assignedUser', 'department'])]
        ], 201);
    }

    public function show(Request $request, Task $task)
    {
        $user = $request->user();

        if (!$this->canAccessTask($user, $task)) {
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            abort(403, 'Access denied');
        }

        $task->load([
            'project',
            'assignedUser',
            'assignedBy',
            'department',
            'dependencies',
            'dependentTasks',
            'fileUploads'
        ]);

        // Return JSON for API requests, view for web requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => ['task' => $task]
            ]);
        }

        return view('tasks.show', compact('task'));
    }

    public function update(Request $request, Task $task)
    {
        $user = $request->user();

        if (!$this->canAccessTask($user, $task)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
            'status' => 'sometimes|in:pending,in_progress,review,completed,cancelled',
            'priority' => 'sometimes|in:low,medium,high,urgent',
            'due_date' => 'nullable|date',
            'estimated_hours' => 'nullable|integer|min:1',
            'actual_hours' => 'nullable|integer|min:0',
            'progress_percentage' => 'nullable|integer|min:0|max:100',
            'requirements' => 'nullable|array',
            'deliverables' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();

        // handle status changes
        if (isset($data['status'])) {
            if ($data['status'] === 'in_progress' && $task->status === 'pending') {
                $data['started_at'] = now();
            } elseif ($data['status'] === 'completed' && $task->status !== 'completed') {
                $data['completed_at'] = now();
                $data['progress_percentage'] = 100;
            }
        }

        $task->update($data);

        // track task update
        AnalyticsEvent::trackTaskEvent('task_updated', $task, $data);

        // update project progress if task completed
        if (isset($data['status']) && $data['status'] === 'completed') {
            $task->project->updateProgress();
        }

        return response()->json([
            'success' => true,
            'message' => 'Task updated successfully',
            'data' => ['task' => $task->fresh(['project', 'assignedUser', 'department'])]
        ]);
    }

    public function start(Request $request, Task $task)
    {
        $user = $request->user();

        if ($task->assigned_to !== $user->id && !$user->hasPermission('*')) {
            return response()->json([
                'success' => false,
                'message' => 'You can only start tasks assigned to you'
            ], 403);
        }

        if ($task->status !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Task cannot be started'
            ], 422);
        }

        if (!$task->canStart()) {
            return response()->json([
                'success' => false,
                'message' => 'Task dependencies are not completed'
            ], 422);
        }

        $task->start();

        return response()->json([
            'success' => true,
            'message' => 'Task started successfully',
            'data' => ['task' => $task->fresh()]
        ]);
    }

    public function complete(Request $request, Task $task)
    {
        $user = $request->user();

        if ($task->assigned_to !== $user->id && !$user->hasPermission('*')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if ($task->status === 'completed') {
            return response()->json([
                'success' => false,
                'message' => 'Task is already completed'
            ], 422);
        }

        $task->complete();

        return response()->json([
            'success' => true,
            'message' => 'Task completed successfully',
            'data' => ['task' => $task->fresh()]
        ]);
    }

    public function updateProgress(Request $request, Task $task)
    {
        $validator = Validator::make($request->all(), [
            'progress_percentage' => 'required|integer|min:0|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $task->updateProgress($request->progress_percentage);

        return response()->json([
            'success' => true,
            'message' => 'Progress updated successfully',
            'data' => [
                'task' => $task->fresh(),
                'progress' => $task->fresh()->progress_percentage
            ]
        ]);
    }

    private function canAccessTask($user, $task): bool
    {
        // admin can access all tasks
        if ($user->hasPermission('*')) {
            return true;
        }

        // assigned user can access
        if ($task->assigned_to === $user->id) {
            return true;
        }

        // department members can access department tasks
        if ($task->department_id === $user->department_id) {
            return true;
        }

        // project manager can access project tasks
        if ($task->project->project_manager_id === $user->id) {
            return true;
        }

        return false;
    }
}
