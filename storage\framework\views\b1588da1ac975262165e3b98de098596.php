<?php $__env->startSection('title', 'Analytics Dashboard'); ?>

<?php $__env->startSection('dashboard-content'); ?>
<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
    <div class="flex items-center space-x-4">
        <select id="periodSelect" class="bg-white border border-gray-300 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500">
            <option value="7">Last 7 Days</option>
            <option value="30" selected>Last 30 Days</option>
            <option value="90">Last 90 Days</option>
            <option value="365">Last Year</option>
        </select>
        <button onclick="refreshDashboard()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-sync-alt"></i>
            <span>Refresh</span>
        </button>
        <button onclick="exportReport()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-download"></i>
            <span>Export</span>
        </button>
    </div>
</div>
<!-- Loading Overlay -->
<div id="analyticsLoading" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <span class="text-gray-700">Loading analytics data...</span>
    </div>
</div>

<!-- Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Revenue Card -->
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg text-white transform hover:scale-105 transition-transform duration-200">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium uppercase tracking-wide">Total Revenue</p>
                    <p id="totalRevenue" class="text-3xl font-bold mt-2">₦0.00</p>
                    <div class="flex items-center mt-2">
                        <span id="revenueGrowth" class="text-sm font-medium">+0%</span>
                        <span class="text-blue-100 text-sm ml-1">vs last period</span>
                    </div>
                </div>
                <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-dollar-sign text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Bookings Card -->
    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg text-white transform hover:scale-105 transition-transform duration-200">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium uppercase tracking-wide">Total Bookings</p>
                    <p id="totalBookings" class="text-3xl font-bold mt-2">0</p>
                    <div class="flex items-center mt-2">
                        <span id="bookingsGrowth" class="text-sm font-medium">+0%</span>
                        <span class="text-green-100 text-sm ml-1">vs last period</span>
                    </div>
                </div>
                <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-calendar text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects Card -->
    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg text-white transform hover:scale-105 transition-transform duration-200">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium uppercase tracking-wide">Active Projects</p>
                    <p id="activeProjects" class="text-3xl font-bold mt-2">0</p>
                    <div class="flex items-center mt-2">
                        <span id="completedProjects" class="text-sm font-medium">0</span>
                        <span class="text-purple-100 text-sm ml-1">completed this period</span>
                    </div>
                </div>
                <div class="bg-purple-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-project-diagram text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Studio Utilization Card -->
    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-lg text-white transform hover:scale-105 transition-transform duration-200">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-yellow-100 text-sm font-medium uppercase tracking-wide">Studio Utilization</p>
                    <p id="studioUtilization" class="text-3xl font-bold mt-2">0%</p>
                    <div class="flex items-center mt-2">
                        <span id="totalHours" class="text-sm font-medium">0</span>
                        <span class="text-yellow-100 text-sm ml-1">hours booked</span>
                    </div>
                </div>
                <div class="bg-yellow-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-chart-pie text-2xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Analytics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg text-white transform hover:scale-105 transition-transform duration-200">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-blue-100 text-sm font-medium uppercase tracking-wide">Average Session Duration</p>
                    <p class="text-3xl font-bold mt-2"><?php echo e($quickStats['avg_session_duration'] ?? '0h 0m'); ?></p>
                    <div class="flex items-center mt-2">
                        <span class="text-blue-100 text-sm">Per booking session</span>
                    </div>
                </div>
                <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-clock text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg text-white transform hover:scale-105 transition-transform duration-200">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-green-100 text-sm font-medium uppercase tracking-wide">Client Retention Rate</p>
                    <p class="text-3xl font-bold mt-2"><?php echo e($quickStats['client_retention_rate'] ?? 0); ?>%</p>
                    <div class="flex items-center mt-2">
                        <span class="text-green-100 text-sm">Returning clients</span>
                    </div>
                </div>
                <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-users text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg text-white transform hover:scale-105 transition-transform duration-200">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-purple-100 text-sm font-medium uppercase tracking-wide">Average Project Value</p>
                    <p class="text-3xl font-bold mt-2">₦<?php echo e(number_format($quickStats['avg_project_value'] ?? 0, 2)); ?></p>
                    <div class="flex items-center mt-2">
                        <span class="text-purple-100 text-sm">Per project</span>
                    </div>
                </div>
                <div class="bg-purple-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-chart-line text-2xl"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl shadow-lg text-white transform hover:scale-105 transition-transform duration-200">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-orange-100 text-sm font-medium uppercase tracking-wide">Peak Booking Hours</p>
                    <p class="text-3xl font-bold mt-2"><?php echo e($quickStats['peak_hours'] ?? 'N/A'); ?></p>
                    <div class="flex items-center mt-2">
                        <span class="text-orange-100 text-sm">Most active time</span>
                    </div>
                </div>
                <div class="bg-orange-400 bg-opacity-30 rounded-full p-3">
                    <i class="fas fa-chart-bar text-2xl"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Revenue Chart -->
    <div class="lg:col-span-2">
        <div class="bg-white shadow-lg rounded-xl">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-medium text-gray-900">Revenue Overview</h3>
                <div class="flex space-x-2">
                    <button type="button" class="bg-indigo-600 text-white px-3 py-1 rounded text-sm" onclick="toggleChart('revenue')">Revenue</button>
                    <button type="button" class="bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm" onclick="toggleChart('bookings')">Bookings</button>
                </div>
            </div>
            <div class="p-6">
                <div class="chart-area">
                    <canvas id="revenueChart" width="100%" height="40"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Studio Room Performance -->
    <div class="lg:col-span-1">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Studio Room Performance</h3>
            </div>
            <div class="p-6">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="roomPerformanceChart"></canvas>
                </div>
                <div class="mt-4 text-center text-sm">
                    <?php $__currentLoopData = $roomPerformance ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $room): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="mr-2">
                        <i class="fas fa-circle" style="color: <?php echo e($room['color']); ?>"></i> <?php echo e($room['name']); ?>

                    </span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
    <!-- Top Clients -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Top Clients</h3>
        </div>
        <div class="p-6">
            <?php $__empty_1 = true; $__currentLoopData = $topClients ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center">
                    <div class="h-10 w-10 rounded-full bg-indigo-500 flex items-center justify-center text-white font-semibold mr-3">
                        <?php echo e(substr($client['name'], 0, 1)); ?>

                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900"><?php echo e($client['name']); ?></h4>
                        <p class="text-sm text-gray-500"><?php echo e($client['projects_count']); ?> projects</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm font-semibold text-green-600">₦<?php echo e(number_format($client['total_spent'], 2)); ?></div>
                    <p class="text-sm text-gray-500"><?php echo e($client['bookings_count']); ?> bookings</p>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="text-center py-8">
                <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No client data available</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <?php $__empty_1 = true; $__currentLoopData = $recentActivity ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="flex items-center mb-4">
                <div class="h-10 w-10 rounded-full <?php echo e($activity['type'] == 'booking' ? 'bg-green-500' : ($activity['type'] == 'project' ? 'bg-blue-500' : 'bg-purple-500')); ?> flex items-center justify-center text-white mr-3">
                    <i class="fas fa-<?php echo e($activity['type'] == 'booking' ? 'calendar' : ($activity['type'] == 'project' ? 'project-diagram' : 'user')); ?>"></i>
                </div>
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900"><?php echo e($activity['title']); ?></h4>
                    <p class="text-sm text-gray-500"><?php echo e($activity['description']); ?></p>
                </div>
                <span class="text-sm text-gray-500"><?php echo e($activity['time']); ?></span>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="text-center py-8">
                <i class="fas fa-clock text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No recent activity</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($chartData['labels'] ?? [], 15, 512) ?>,
        datasets: [{
            label: 'Revenue',
            data: <?php echo json_encode($chartData['revenue'] ?? [], 15, 512) ?>,
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            borderWidth: 2,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Room Performance Chart
const roomCtx = document.getElementById('roomPerformanceChart').getContext('2d');
const roomChart = new Chart(roomCtx, {
    type: 'doughnut',
    data: {
        labels: <?php echo json_encode(array_column($roomPerformance ?? [], 'name'), 512) ?>,
        datasets: [{
            data: <?php echo json_encode(array_column($roomPerformance ?? [], 'bookings'), 512) ?>,
            backgroundColor: <?php echo json_encode(array_column($roomPerformance ?? [], 'color'), 512) ?>,
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Global variables
let revenueChart, utilizationChart, projectChart;
let currentPeriod = 30;

$(document).ready(function() {
    initializeAnalytics();
    loadAnalyticsData();

    // Period selector change handler
    $('#periodSelect').on('change', function() {
        currentPeriod = $(this).val();
        loadAnalyticsData();
    });
});

function initializeAnalytics() {
    // Initialize charts
    initializeRevenueChart();
    initializeUtilizationChart();
    initializeProjectChart();
}

function loadAnalyticsData() {
    showLoading();

    $.ajax({
        url: '/api/analytics/overview',
        method: 'GET',
        data: { period: currentPeriod },
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            updateOverviewCards(response.data);
            updateCharts(response.data);
        } else {
            showNotification('Error loading analytics data: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Analytics API error:', xhr);
        showNotification('Failed to load analytics data', 'error');
    })
    .always(function() {
        hideLoading();
    });
}

function updateOverviewCards(data) {
    // Update revenue card
    $('#totalRevenue').text('₦' + formatNumber(data.total_revenue || 0));
    $('#revenueGrowth').text((data.revenue_growth >= 0 ? '+' : '') + (data.revenue_growth || 0) + '%');

    // Update bookings card
    $('#totalBookings').text(formatNumber(data.total_bookings || 0));
    $('#bookingsGrowth').text((data.bookings_growth >= 0 ? '+' : '') + (data.bookings_growth || 0) + '%');

    // Update projects card
    $('#activeProjects').text(formatNumber(data.active_projects || 0));
    $('#completedProjects').text(formatNumber(data.completed_projects || 0));

    // Update utilization card
    $('#studioUtilization').text((data.studio_utilization || 0) + '%');
    $('#totalHours').text(formatNumber(data.total_hours_booked || 0));
}

function updateCharts(data) {
    // Update revenue chart
    if (revenueChart && data.revenue_chart) {
        revenueChart.data.labels = data.revenue_chart.labels || [];
        revenueChart.data.datasets[0].data = data.revenue_chart.data || [];
        revenueChart.update();
    }

    // Update utilization chart
    if (utilizationChart && data.utilization_chart) {
        utilizationChart.data.datasets[0].data = data.utilization_chart.data || [];
        utilizationChart.update();
    }

    // Update project chart
    if (projectChart && data.project_chart) {
        projectChart.data.labels = data.project_chart.labels || [];
        projectChart.data.datasets[0].data = data.project_chart.data || [];
        projectChart.update();
    }
}

function initializeRevenueChart() {
    const ctx = document.getElementById('revenueChart');
    if (!ctx) return;

    revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Revenue',
                data: [],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '₦' + formatNumber(value);
                        }
                    }
                }
            }
        }
    });
}

function initializeUtilizationChart() {
    const ctx = document.getElementById('utilizationChart');
    if (!ctx) return;

    utilizationChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Utilized', 'Available'],
            datasets: [{
                data: [0, 100],
                backgroundColor: [
                    'rgb(34, 197, 94)',
                    'rgb(229, 231, 235)'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function initializeProjectChart() {
    const ctx = document.getElementById('projectChart');
    if (!ctx) return;

    projectChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [{
                label: 'Projects',
                data: [],
                backgroundColor: 'rgba(147, 51, 234, 0.8)',
                borderColor: 'rgb(147, 51, 234)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function refreshDashboard() {
    loadAnalyticsData();
    showNotification('Dashboard refreshed successfully', 'success');
}

function exportReport() {
    showLoading();

    $.ajax({
        url: '/api/analytics/export',
        method: 'GET',
        data: { period: currentPeriod },
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success && response.download_url) {
            // Create download link
            const link = document.createElement('a');
            link.href = response.download_url;
            link.download = `analytics_report_${currentPeriod}days.pdf`;
            link.click();
            showNotification('Report exported successfully', 'success');
        } else {
            showNotification('Error exporting report: ' + (response.message || 'Unknown error'), 'error');
        }
    })
    .fail(function() {
        showNotification('Failed to export report', 'error');
    })
    .always(function() {
        hideLoading();
    });
}

function showLoading() {
    $('#analyticsLoading').removeClass('hidden');
}

function hideLoading() {
    $('#analyticsLoading').addClass('hidden');
}

function formatNumber(num) {
    return new Intl.NumberFormat('en-NG').format(num || 0);
}

function toggleChart(type) {
    // Update active button
    document.querySelectorAll('.btn-group .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Update chart data based on type
    loadAnalyticsData();
}
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.avatar-sm {
    width: 2rem;
    height: 2rem;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.chart-area {
    position: relative;
    height: 320px;
}

.chart-pie {
    position: relative;
    height: 245px;
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\workspace\.php\glitchafrica\resources\views/analytics/index.blade.php ENDPATH**/ ?>