<!-- Top Navigation Bar -->
<header class="bg-white shadow-sm border-b border-gray-200">
    <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
        <!-- Mobile menu button -->
        <div class="flex items-center lg:hidden">
            <button onclick="toggleMobileMenu()" class="text-gray-500 hover:text-gray-700 focus:outline-none focus:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>

        <!-- Page Title -->
        <div class="flex-1 lg:flex-none">
            <h1 class="text-xl font-semibold text-gray-900 lg:hidden">
                @yield('title', 'Dashboard')
            </h1>
        </div>

        <!-- Search Bar (Desktop) -->
        <div class="hidden lg:flex flex-1 max-w-lg mx-8">
            <div class="relative w-full">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
                <input type="text" 
                       id="global-search" 
                       class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                       placeholder="Search projects, tasks, clients..."
                       autocomplete="off">
                
                <!-- Search Results Dropdown -->
                <div id="search-results" class="hidden absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                    <!-- Search results will be populated here -->
                </div>
            </div>
        </div>

        <!-- Right side items -->
        <div class="flex items-center space-x-4">
            <!-- Search Button (Mobile) -->
            <button onclick="toggleMobileSearch()" class="lg:hidden text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </button>

            <!-- Notifications -->
            <div class="relative">
                <button onclick="toggleNotifications()" class="text-gray-500 hover:text-gray-700 focus:outline-none focus:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                    @if(isset($unreadNotifications) && $unreadNotifications > 0)
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                            {{ $unreadNotifications > 9 ? '9+' : $unreadNotifications }}
                        </span>
                    @endif
                </button>

                <!-- Notifications Dropdown -->
                <div id="notifications-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
                    <div class="p-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900">Notifications</h3>
                            @if(isset($unreadNotifications) && $unreadNotifications > 0)
                                <button onclick="markAllAsRead()" class="text-sm text-blue-600 hover:text-blue-800">
                                    Mark all as read
                                </button>
                            @endif
                        </div>
                    </div>
                    <div class="max-h-64 overflow-y-auto">
                        @if(isset($notifications) && count($notifications) > 0)
                            @foreach($notifications as $notification)
                                <div class="p-4 border-b border-gray-100 hover:bg-gray-50 {{ $notification['read_at'] ? '' : 'bg-blue-50' }}">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            @if($notification['type'] === 'task_assigned')
                                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                </div>
                                            @elseif($notification['type'] === 'project_update')
                                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                                    </svg>
                                                </div>
                                            @else
                                                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                </div>
                                            @endif
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm font-medium text-gray-900">{{ $notification['title'] }}</p>
                                            <p class="text-sm text-gray-500">{{ $notification['message'] }}</p>
                                            <p class="text-xs text-gray-400 mt-1">{{ \Carbon\Carbon::parse($notification['created_at'])->diffForHumans() }}</p>
                                        </div>
                                        @if(!$notification['read_at'])
                                            <div class="flex-shrink-0">
                                                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="p-8 text-center">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                </svg>
                                <p class="text-gray-500">No notifications</p>
                            </div>
                        @endif
                    </div>
                    @if(isset($notifications) && count($notifications) > 0)
                        <div class="p-4 border-t border-gray-200">
                            <a href="{{ route('notifications.index') }}" class="block text-center text-sm text-blue-600 hover:text-blue-800">
                                View all notifications
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="relative">
                <button onclick="toggleQuickActions()" class="text-gray-500 hover:text-gray-700 focus:outline-none focus:text-gray-700">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                </button>

                <!-- Quick Actions Dropdown -->
                <div id="quick-actions-dropdown" class="hidden absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
                    <div class="p-2">
                        <a href="{{ route('projects.create') }}" class="flex items-center px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100">
                            <svg class="w-4 h-4 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            New Project
                        </a>
                        <a href="{{ route('tasks.create') }}" class="flex items-center px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100">
                            <svg class="w-4 h-4 mr-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            New Task
                        </a>
                        <a href="{{ route('bookings.create') }}" class="flex items-center px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100">
                            <svg class="w-4 h-4 mr-3 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            New Booking
                        </a>
                        <a href="{{ route('clients.create') }}" class="flex items-center px-3 py-2 text-sm text-gray-700 rounded-lg hover:bg-gray-100">
                            <svg class="w-4 h-4 mr-3 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            New Client
                        </a>
                    </div>
                </div>
            </div>

            <!-- User Avatar -->
            <div class="flex items-center">
                @if(auth()->user()->avatar)
                    <img class="w-8 h-8 rounded-full" src="{{ auth()->user()->avatar }}" alt="{{ auth()->user()->full_name }}">
                @else
                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-700">{{ substr(auth()->user()->first_name, 0, 1) }}{{ substr(auth()->user()->last_name, 0, 1) }}</span>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Mobile Search Bar -->
    <div id="mobile-search" class="hidden lg:hidden border-t border-gray-200 p-4">
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
            <input type="text" 
                   id="mobile-global-search" 
                   class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
                   placeholder="Search projects, tasks, clients..."
                   autocomplete="off">
        </div>
    </div>
</header>

<script>
    function toggleNotifications() {
        const dropdown = document.getElementById('notifications-dropdown');
        dropdown.classList.toggle('hidden');
        
        // Close other dropdowns
        document.getElementById('quick-actions-dropdown').classList.add('hidden');
    }
    
    function toggleQuickActions() {
        const dropdown = document.getElementById('quick-actions-dropdown');
        dropdown.classList.toggle('hidden');
        
        // Close other dropdowns
        document.getElementById('notifications-dropdown').classList.add('hidden');
    }
    
    function toggleMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-menu-overlay');

        sidebar.classList.toggle('-translate-x-full');
        overlay.classList.toggle('hidden');
    }

    function closeMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-menu-overlay');

        sidebar.classList.add('-translate-x-full');
        overlay.classList.add('hidden');
    }

    function toggleMobileSearch() {
        const mobileSearch = document.getElementById('mobile-search');
        mobileSearch.classList.toggle('hidden');

        if (!mobileSearch.classList.contains('hidden')) {
            document.getElementById('mobile-global-search').focus();
        }
    }
    
    function markAllAsRead() {
        fetch('/api/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': window.Laravel.csrfToken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error marking notifications as read:', error);
        });
    }
    
    // Global search functionality
    let searchTimeout;
    
    function setupGlobalSearch(inputId, resultsId) {
        const searchInput = document.getElementById(inputId);
        const searchResults = document.getElementById(resultsId);
        
        if (!searchInput) return;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            if (query.length < 2) {
                if (searchResults) searchResults.classList.add('hidden');
                return;
            }
            
            searchTimeout = setTimeout(() => {
                performGlobalSearch(query, searchResults);
            }, 300);
        });
        
        // Close search results when clicking outside
        document.addEventListener('click', function(event) {
            if (searchResults && !searchInput.contains(event.target) && !searchResults.contains(event.target)) {
                searchResults.classList.add('hidden');
            }
        });
    }
    
    function performGlobalSearch(query, resultsContainer) {
        if (!resultsContainer) return;
        
        fetch(`/api/search?q=${encodeURIComponent(query)}`, {
            headers: {
                'X-CSRF-TOKEN': window.Laravel.csrfToken,
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.results, resultsContainer);
        })
        .catch(error => {
            console.error('Search error:', error);
            resultsContainer.classList.add('hidden');
        });
    }
    
    function displaySearchResults(results, container) {
        if (!results || results.length === 0) {
            container.innerHTML = '<div class="px-4 py-2 text-sm text-gray-500">No results found</div>';
            container.classList.remove('hidden');
            return;
        }
        
        const html = results.map(result => `
            <a href="${result.url}" class="block px-4 py-2 hover:bg-gray-100">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-${result.type === 'project' ? 'blue' : result.type === 'task' ? 'green' : result.type === 'client' ? 'orange' : 'gray'}-100 text-${result.type === 'project' ? 'blue' : result.type === 'task' ? 'green' : result.type === 'client' ? 'orange' : 'gray'}-800">
                            ${result.type}
                        </span>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm font-medium text-gray-900">${result.title}</p>
                        ${result.description ? `<p class="text-sm text-gray-500">${result.description}</p>` : ''}
                    </div>
                </div>
            </a>
        `).join('');
        
        container.innerHTML = html;
        container.classList.remove('hidden');
    }
    
    // Initialize search functionality
    document.addEventListener('DOMContentLoaded', function() {
        setupGlobalSearch('global-search', 'search-results');
        setupGlobalSearch('mobile-global-search', null);
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const notificationsDropdown = document.getElementById('notifications-dropdown');
        const quickActionsDropdown = document.getElementById('quick-actions-dropdown');
        
        if (!event.target.closest('[onclick*="toggleNotifications"]')) {
            notificationsDropdown.classList.add('hidden');
        }
        
        if (!event.target.closest('[onclick*="toggleQuickActions"]')) {
            quickActionsDropdown.classList.add('hidden');
        }
    });
</script>
