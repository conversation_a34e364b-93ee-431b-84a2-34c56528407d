<?php $__env->startSection('title', 'Tasks'); ?>
<?php $__env->startSection('dashboard-content'); ?>

<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Tasks</h1>
    <a href="<?php echo e(route('tasks.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
        <i class="fas fa-plus"></i>
        <span>Create Task</span>
    </a>
</div>

            <!-- Filters -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                        <div>
                            <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Statuses</option>
                                <option value="pending">Pending</option>
                                <option value="in_progress">In Progress</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div>
                            <label for="priorityFilter" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                            <select id="priorityFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Priorities</option>
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>
                        <div>
                            <label for="projectFilter" class="block text-sm font-medium text-gray-700 mb-2">Project</label>
                            <select id="projectFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Projects</option>
                                <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($project->id); ?>"><?php echo e($project->title); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div>
                            <label for="assigneeFilter" class="block text-sm font-medium text-gray-700 mb-2">Assignee</label>
                            <select id="assigneeFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">All Assignees</option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($user->id); ?>"><?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div>
                            <label for="searchFilter" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                            <input type="text" id="searchFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="Task title">
                        </div>
                        <div class="flex items-end">
                            <div class="flex space-x-2">
                                <button type="button" onclick="applyTaskFilters()" class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    Filter
                                </button>
                                <button type="button" onclick="clearTaskFilters()" class="px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                                    Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6" id="summaryCards">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Total Tasks</p>
                            <p class="text-3xl font-bold text-gray-900" id="totalTasks"><?php echo e($summary['total'] ?? 0); ?></p>
                        </div>
                        <div class="p-3 bg-blue-100 rounded-full">
                            <i class="fas fa-tasks text-2xl text-blue-600"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">In Progress</p>
                            <p class="text-3xl font-bold text-gray-900" id="inProgressTasks"><?php echo e($summary['in_progress'] ?? 0); ?></p>
                        </div>
                        <div class="p-3 bg-yellow-100 rounded-full">
                            <i class="fas fa-spinner text-2xl text-yellow-600"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Completed</p>
                            <p class="text-3xl font-bold text-gray-900" id="completedTasks"><?php echo e($summary['completed'] ?? 0); ?></p>
                        </div>
                        <div class="p-3 bg-green-100 rounded-full">
                            <i class="fas fa-check-circle text-2xl text-green-600"></i>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 uppercase tracking-wide">Overdue</p>
                            <p class="text-3xl font-bold text-gray-900" id="overdueTasks"><?php echo e($summary['overdue'] ?? 0); ?></p>
                        </div>
                        <div class="p-3 bg-red-100 rounded-full">
                            <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading Spinner -->
            <div id="loadingSpinner" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                            <svg class="animate-spin h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Loading...</h3>
                        <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500">Please wait while we load the tasks.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tasks Table -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Tasks List</h3>
                </div>
                <div class="p-6" id="tasksContainer">
                    <!-- Tasks will be loaded here via AJAX -->
                </div>
                <div id="paginationContainer" class="mt-6"></div>
            </div>
        </div>
    </div>
</div>

<!-- Update Progress Modal -->
<div id="progressModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white modal-content" style="opacity: 0; transform: scale(0.95) translateY(-20px); transition: all 0.3s ease;">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Update Task Progress</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeProgressModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="progressForm" method="POST" onsubmit="submitProgressUpdate(event)">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="mb-4">
                    <label for="progress" class="block text-sm font-medium text-gray-700 mb-2">Progress (%)</label>
                    <input type="range" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer" id="progress" name="progress_percentage" min="0" max="100" value="0" oninput="updateProgressValue(this.value)">
                    <div class="flex justify-between text-sm text-gray-500 mt-1">
                        <span>0%</span>
                        <span id="progress-value">0%</span>
                        <span>100%</span>
                    </div>
                </div>
                <div class="mb-4">
                    <label for="progress_notes" class="block text-sm font-medium text-gray-700 mb-2">Progress Notes</label>
                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" id="progress_notes" name="notes" rows="3" placeholder="Optional notes about the progress update"></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors" onclick="closeProgressModal()">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">Update Progress</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Task</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this task? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="btn btn-danger">Delete Task</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let currentTaskPage = 1;

// Load tasks on page load
$(document).ready(function() {
    console.log('Document ready, loading tasks...');
    loadTasks();

    // Auto-apply filters on change
    $('#statusFilter, #priorityFilter, #projectFilter, #assigneeFilter').change(function() {
        currentTaskPage = 1;
        loadTasks();
    });

    // Search with debounce
    let searchTimeout;
    $('#searchFilter').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            currentTaskPage = 1;
            loadTasks();
        }, 500);
    });


});

// Load tasks via AJAX
function loadTasks() {
    console.log('Loading tasks...');
    $('#loadingSpinner').removeClass('hidden');
    $('#tasksContainer').addClass('opacity-50');

    const filters = {
        status: $('#statusFilter').val(),
        priority: $('#priorityFilter').val(),
        project_id: $('#projectFilter').val(),
        assigned_to: $('#assigneeFilter').val(),
        search: $('#searchFilter').val(),
        page: currentTaskPage
    };

    console.log('Filters:', filters);

    $.ajax({
        url: '<?php echo e(route("tasks.index")); ?>',
        method: 'GET',
        data: filters,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .done(function(response) {
        console.log('Tasks response:', response);
        console.log('Response data structure:', response.data);
        if (response.success) {
            console.log('Rendering tasks...');
            console.log('Tasks:', response.data.tasks);
            console.log('Summary:', response.data.summary);
            renderTasks(response.data.tasks);
            renderTaskPagination(response.data.pagination);
            updateTaskSummaryCards(response.data.summary);
            console.log('Tasks rendered successfully');
        } else {
            console.error('Response error:', response);
            showTaskNotification('Error loading tasks: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('AJAX Error:', xhr);
        console.error('Response Text:', xhr.responseText);
        let errorMessage = 'Error loading tasks';
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage += ': ' + xhr.responseJSON.message;
        } else if (xhr.status) {
            errorMessage += ' (Status: ' + xhr.status + ')';
        }
        showTaskNotification(errorMessage, 'error');
    })
    .always(function() {
        console.log('Hiding loading spinner...');
        $('#loadingSpinner').addClass('hidden').hide();
        $('#tasksContainer').removeClass('opacity-50');
        console.log('Loading spinner hidden');
    });
}

// Render tasks table
function renderTasks(tasks) {
    console.log('renderTasks called with:', tasks);
    const container = $('#tasksContainer');

    if (tasks.length === 0) {
        console.log('No tasks found, showing empty state');
        container.html(`
            <div class="text-center py-12">
                <i class="fas fa-tasks text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
                <p class="text-gray-500 mb-6">Create your first task to get started.</p>
                <a href="<?php echo e(route('tasks.create')); ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">Create Task</a>
            </div>
        `);
        return;
    }

    let html = `
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assignee</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
    `;

    tasks.forEach(task => {
        const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed';
        const rowClass = isOverdue ? 'bg-red-50' : 'hover:bg-gray-50';

        html += `
            <tr class="${rowClass}">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="font-medium text-gray-900">
                        <a href="/tasks/${task.id}" class="text-indigo-600 hover:text-indigo-900">
                            ${task.title}
                        </a>
                    </div>
                    ${task.description ? `<div class="text-sm text-gray-500">${task.description.substring(0, 50)}${task.description.length > 50 ? '...' : ''}</div>` : ''}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${task.project ? `<a href="/projects/${task.project.id}" class="text-indigo-600 hover:text-indigo-900">${task.project.title}</a>` : '<span class="text-gray-500">No project</span>'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${task.assigned_user ? `
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-8 w-8">
                                <div class="h-8 w-8 rounded-full bg-indigo-600 flex items-center justify-center text-white text-sm font-medium">
                                    ${task.assigned_user.first_name.charAt(0)}${task.assigned_user.last_name.charAt(0)}
                                </div>
                            </div>
                            <div class="ml-3">
                                <div class="text-sm font-medium text-gray-900">${task.assigned_user.first_name} ${task.assigned_user.last_name}</div>
                            </div>
                        </div>
                    ` : '<span class="text-gray-500">Unassigned</span>'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(task.priority)}">
                        ${task.priority ? task.priority.charAt(0).toUpperCase() + task.priority.slice(1) : 'Medium'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(task.status)}">
                        ${task.status ? task.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Pending'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    ${task.due_date ? `
                        <div class="text-sm ${isOverdue ? 'text-red-600 font-bold' : 'text-gray-900'}">
                            ${new Date(task.due_date).toLocaleDateString()}
                            ${isOverdue ? '<i class="fas fa-exclamation-triangle ml-1"></i>' : ''}
                        </div>
                    ` : '<span class="text-gray-500 text-sm">No due date</span>'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div class="bg-indigo-600 h-2 rounded-full" style="width: ${task.progress_percentage || 0}%"></div>
                        </div>
                        <span class="text-sm text-gray-600">${task.progress_percentage || 0}%</span>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button type="button" class="text-indigo-600 hover:text-indigo-900" onclick="viewTask(${task.id})" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="text-gray-600 hover:text-gray-900" onclick="editTask(${task.id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${task.status !== 'in_progress' ? `
                            <button type="button" class="text-green-600 hover:text-green-900" onclick="startTask(${task.id})" title="Start">
                                <i class="fas fa-play"></i>
                            </button>
                        ` : ''}
                        ${task.status !== 'completed' ? `
                            <button type="button" class="text-blue-600 hover:text-blue-900" onclick="completeTask(${task.id})" title="Complete">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        <button type="button" class="text-yellow-600 hover:text-yellow-900" onclick="updateProgress(${task.id})" title="Update Progress">
                            <i class="fas fa-percentage"></i>
                        </button>
                        <button type="button" class="text-red-600 hover:text-red-900" onclick="deleteTask(${task.id})" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.html(html);
}

// Update summary cards
function updateTaskSummaryCards(summary) {
    console.log('updateTaskSummaryCards called with:', summary);
    if (summary) {
        $('#totalTasks').text(summary.total || 0);
        $('#inProgressTasks').text(summary.in_progress || 0);
        $('#completedTasks').text(summary.completed || 0);
        $('#overdueTasks').text(summary.overdue || 0);
    } else {
        console.log('Summary is undefined or null');
    }
}

// Render pagination
function renderTaskPagination(pagination) {
    // Implementation for pagination rendering
    $('#paginationContainer').html(''); // Simplified for now
}

// Utility functions
function applyTaskFilters() {
    currentTaskPage = 1;
    loadTasks();
}

function clearTaskFilters() {
    $('#statusFilter').val('');
    $('#priorityFilter').val('');
    $('#projectFilter').val('');
    $('#assigneeFilter').val('');
    $('#searchFilter').val('');
    currentTaskPage = 1;
    loadTasks();
}

function showTaskNotification(message, type) {
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const notification = $(`
        <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50">
            ${message}
        </div>
    `);

    $('body').append(notification);
    setTimeout(() => notification.remove(), 3000);
}

async function startTask(taskId) {
    const confirmed = await showConfirm('Are you sure you want to start this task?', 'Start Task', 'info');
    if (confirmed) {
        $.ajax({
            url: `/tasks/${taskId}/start`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .done(function(response) {
            if (response.success) {
                showTaskNotification(response.message, 'success');
                loadTasks(); // Reload tasks to update the list
            } else {
                showTaskNotification(response.message || 'Error starting task', 'error');
            }
        })
        .fail(function(xhr) {
            const response = xhr.responseJSON;
            showTaskNotification(response?.message || 'Error starting task', 'error');
        });
    }
}

async function completeTask(taskId) {
    const confirmed = await showConfirm('Mark this task as completed?', 'Complete Task', 'success');
    if (confirmed) {
        $.ajax({
            url: `/tasks/${taskId}/complete`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .done(function(response) {
            if (response.success) {
                showTaskNotification(response.message, 'success');
                loadTasks(); // Reload tasks to update the list
            } else {
                showTaskNotification(response.message || 'Error completing task', 'error');
            }
        })
        .fail(function(xhr) {
            const response = xhr.responseJSON;
            showTaskNotification(response?.message || 'Error completing task', 'error');
        });
    }
}

function updateProgress(taskId) {
    currentTaskId = taskId;
    const form = document.getElementById('progressForm');
    form.action = `/tasks/${taskId}/progress`;

    // Show the modal
    const modal = document.getElementById('progressModal');
    modal.classList.remove('hidden');

    // Trigger animation
    setTimeout(() => {
        const content = modal.querySelector('.modal-content');
        content.style.opacity = '1';
        content.style.transform = 'scale(1) translateY(0)';
    }, 10);
}

function updateProgressValue(value) {
    document.getElementById('progress-value').textContent = value + '%';
}

function submitProgressUpdate(event) {
    event.preventDefault();

    if (!currentTaskId) return;

    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    // Convert FormData to regular object for PUT request
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.textContent = 'Updating...';

    $.ajax({
        url: `/tasks/${currentTaskId}/progress`,
        method: 'PUT',
        data: data,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .done(function(response) {
        if (response.success) {
            showTaskNotification('Progress updated successfully', 'success');
            closeProgressModal();
            loadTasks(); // Reload tasks
        } else {
            showTaskNotification('Error: ' + (response.message || 'Failed to update progress'), 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Progress update error:', xhr);
        const response = xhr.responseJSON;
        if (response && response.errors) {
            let errorMsg = 'Validation errors: ';
            Object.keys(response.errors).forEach(key => {
                errorMsg += response.errors[key].join(', ') + ' ';
            });
            showTaskNotification(errorMsg, 'error');
        } else {
            showTaskNotification('Error updating progress', 'error');
        }
    })
    .always(function() {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

function closeProgressModal() {
    const modal = document.getElementById('progressModal');
    const content = modal.querySelector('.modal-content');

    // Animate out
    content.style.opacity = '0';
    content.style.transform = 'scale(0.95) translateY(-20px)';

    setTimeout(() => {
        modal.classList.add('hidden');
        // Reset form
        document.getElementById('progressForm').reset();
        currentTaskId = null;
    }, 300);
}

async function deleteTask(taskId) {
    const confirmed = await showConfirm(
        'Are you sure you want to delete this task? This action cannot be undone.',
        'Delete Task',
        'danger'
    );

    if (confirmed) {
        // Show loading state
        showTaskNotification('Deleting task...', 'info');

        try {
            const response = await fetch(`/tasks/${taskId}`, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                showTaskNotification('Task deleted successfully', 'success');
                loadTasks(); // Reload the tasks list
            } else {
                showTaskNotification('Error: ' + (data.message || 'Failed to delete task'), 'error');
            }
        } catch (error) {
            console.error('Delete task error:', error);
            showTaskNotification('Error deleting task', 'error');
        }
    }
}

function viewTask(taskId) {
    // Load task data for viewing
    fetch(`/tasks/${taskId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
        .then(response => response.json())
        .then(data => {
            console.log('Task data received:', data);
            if (data.success) {
                renderTaskDetails(data.data.task);
                showModal('viewTaskModal');
            } else {
                showNotification('Error loading task: ' + (data.message || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error loading task:', error);
            showNotification('Error loading task details', 'error');
        });
}

function editTask(taskId) {
    // Close view modal first if it's open
    hideModal('viewTaskModal');

    // Load task data for editing
    fetch(`/tasks/${taskId}`, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateEditTaskForm(data.data.task);
                showModal('editTaskModal');
            } else {
                showTaskNotification('Error loading task: ' + (data.message || 'Unknown error'), 'error');
            }
        })
        .catch(error => {
            console.error('Error loading task:', error);
            showTaskNotification('Error loading task for editing', 'error');
        });
}

function renderTaskDetails(task) {
    const statusColor = getStatusColor(task.status);
    const priorityColor = getPriorityColor(task.priority);

    document.getElementById('taskDetailsContent').innerHTML = `
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex justify-between items-start">
                <div>
                    <h4 class="text-2xl font-bold text-gray-900">${task.title || 'Untitled Task'}</h4>
                    <p class="text-gray-600 mt-1">${task.description || 'No description available'}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusColor}">
                            ${(task.status || 'pending').replace('_', ' ').toUpperCase()}
                        </span>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${priorityColor}">
                            ${(task.priority || 'medium').toUpperCase()}
                        </span>
                        <span class="text-sm text-gray-500">ID: ${task.id}</span>
                    </div>
                </div>
            </div>

            <!-- Basic Info -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Project</label>
                        <p class="mt-1 text-sm text-gray-900">${task.project ? task.project.title : 'No project assigned'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Assigned To</label>
                        <p class="mt-1 text-sm text-gray-900">${task.assigned_to ? (task.assigned_to.first_name + ' ' + task.assigned_to.last_name) : 'Not assigned'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Created By</label>
                        <p class="mt-1 text-sm text-gray-900">${task.created_by ? (task.created_by.first_name + ' ' + task.created_by.last_name) : 'Unknown'}</p>
                    </div>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Due Date</label>
                        <p class="mt-1 text-sm text-gray-900 ${task.due_date && new Date(task.due_date) < new Date() ? 'text-red-600 font-medium' : ''}">${task.due_date ? formatDate(task.due_date) : 'Not set'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Estimated Hours</label>
                        <p class="mt-1 text-sm text-gray-900">${task.estimated_hours || 'Not specified'}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Actual Hours</label>
                        <p class="mt-1 text-sm text-gray-900">${task.actual_hours || '0'}</p>
                    </div>
                </div>
            </div>

            <!-- Progress -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Progress</label>
                <div class="flex items-center">
                    <div class="flex-1 bg-gray-200 rounded-full h-3 mr-3">
                        <div class="h-3 rounded-full bg-blue-600" style="width: ${task.progress_percentage || 0}%"></div>
                    </div>
                    <span class="text-sm font-medium text-gray-900">${task.progress_percentage || 0}%</span>
                </div>
            </div>

            <!-- Timestamps -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-500">
                <div>
                    <p>Created: ${formatDate(task.created_at)}</p>
                    <p>Updated: ${formatDate(task.updated_at)}</p>
                </div>
                <div>
                    ${task.started_at ? `<p>Started: ${formatDate(task.started_at)}</p>` : ''}
                    ${task.completed_at ? `<p>Completed: ${formatDate(task.completed_at)}</p>` : ''}
                </div>
            </div>
        </div>
    `;
}

function populateEditTaskForm(task) {
    document.getElementById('editTaskId').value = task.id;
    document.getElementById('editTaskTitle').value = task.title || '';
    document.getElementById('editTaskDescription').value = task.description || '';
    document.getElementById('editTaskProject').value = task.project_id || '';
    document.getElementById('editTaskAssignedTo').value = task.assigned_to_id || '';
    document.getElementById('editTaskStatus').value = task.status || 'pending';
    document.getElementById('editTaskPriority').value = task.priority || 'medium';
    document.getElementById('editTaskDueDate').value = task.due_date ? task.due_date.split('T')[0] : '';
    document.getElementById('editTaskEstimatedHours').value = task.estimated_hours || '';
}

function getStatusColor(status) {
    switch(status) {
        case 'completed': return 'bg-green-100 text-green-800';
        case 'in_progress': return 'bg-blue-100 text-blue-800';
        case 'on_hold': return 'bg-yellow-100 text-yellow-800';
        case 'cancelled': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getPriorityColor(priority) {
    switch(priority) {
        case 'high': return 'bg-red-100 text-red-800';
        case 'medium': return 'bg-yellow-100 text-yellow-800';
        case 'low': return 'bg-green-100 text-green-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function formatDate(dateString) {
    if (!dateString) return 'Not set';
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function closeViewTaskModal() {
    const modal = document.getElementById('viewTaskModal');
    const content = modal.querySelector('.modal-content');
    content.classList.add('scale-95', 'opacity-0');
    setTimeout(() => {
        modal.classList.add('hidden');
        content.classList.remove('scale-95', 'opacity-0');
    }, 200);
}

function closeEditTaskModal() {
    const modal = document.getElementById('editTaskModal');
    const content = modal.querySelector('.modal-content');
    content.classList.add('scale-95', 'opacity-0');
    setTimeout(() => {
        modal.classList.add('hidden');
        content.classList.remove('scale-95', 'opacity-0');
    }, 200);
}

function editTaskFromView() {
    const taskId = document.getElementById('taskDetailsContent').dataset.taskId;
    if (taskId) {
        closeViewTaskModal();
        setTimeout(() => {
            editTask(taskId);
        }, 300);
    }
}

// Add modal animation on show
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    const content = modal.querySelector('.modal-content');
    modal.classList.remove('hidden');
    setTimeout(() => {
        content.classList.remove('scale-95', 'opacity-0');
    }, 10);
}

function showNotification(message, type) {
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const notification = $(`
        <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50">
            ${message}
        </div>
    `);

    $('body').append(notification);
    setTimeout(() => notification.remove(), 3000);
}
</script>

<!-- View Task Modal -->
<div id="viewTaskModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-4">
    <div class="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl transform transition-all duration-300 scale-95 opacity-0 modal-content">
        <!-- Header -->
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-6 rounded-t-2xl">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="bg-white bg-opacity-20 p-2 rounded-lg">
                        <i class="fas fa-tasks text-white text-xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white">Task Details</h3>
                </div>
                <button onclick="closeViewTaskModal()" class="text-white hover:text-gray-200 transition-colors duration-200 p-2 hover:bg-white hover:bg-opacity-20 rounded-lg">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Content -->
        <div class="px-8 py-6 max-h-96 overflow-y-auto">
            <div id="taskDetailsContent" class="space-y-6">
                <!-- Task details will be loaded here -->
                <div class="flex items-center justify-center py-12">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-8 py-4 rounded-b-2xl border-t border-gray-200">
            <div class="flex justify-end space-x-3">
                <button onclick="closeViewTaskModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2">
                    <i class="fas fa-times"></i>
                    <span>Close</span>
                </button>
                <button onclick="editTaskFromView()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2">
                    <i class="fas fa-edit"></i>
                    <span>Edit Task</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Task Modal -->
<div id="editTaskModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-4">
    <div class="relative w-full max-w-4xl bg-white rounded-2xl shadow-2xl transform transition-all duration-300 scale-95 opacity-0 modal-content">
        <!-- Header -->
        <div class="bg-gradient-to-r from-emerald-600 to-teal-600 px-8 py-6 rounded-t-2xl">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="bg-white bg-opacity-20 p-2 rounded-lg">
                        <i class="fas fa-edit text-white text-xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white">Edit Task</h3>
                </div>
                <button onclick="closeEditTaskModal()" class="text-white hover:text-gray-200 transition-colors duration-200 p-2 hover:bg-white hover:bg-opacity-20 rounded-lg">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Content -->
        <div class="px-8 py-6 max-h-96 overflow-y-auto">
            <form id="editTaskForm" method="POST" class="space-y-6">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <input type="hidden" id="editTaskId" name="id">
                <!-- Form Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Title -->
                    <div class="md:col-span-1">
                        <label for="editTaskTitle" class="block text-sm font-semibold text-gray-700 mb-2">
                            Task Title <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text"
                                   id="editTaskTitle"
                                   name="title"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                                   placeholder="Enter task title">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-tasks text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Project -->
                    <div class="md:col-span-1">
                        <label for="editTaskProject" class="block text-sm font-semibold text-gray-700 mb-2">
                            Project
                        </label>
                        <div class="relative">
                            <select id="editTaskProject"
                                    name="project_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-gray-50 focus:bg-white appearance-none">
                                <option value="">Select Project</option>
                                <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($project->id); ?>"><?php echo e($project->title); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div>
                    <label for="editTaskDescription" class="block text-sm font-semibold text-gray-700 mb-2">
                        Description
                    </label>
                    <textarea id="editTaskDescription"
                              name="description"
                              rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-gray-50 focus:bg-white resize-none"
                              placeholder="Enter task description..."></textarea>
                </div>
                <!-- Assigned To & Due Date -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Assigned To -->
                    <div>
                        <label for="editTaskAssignedTo" class="block text-sm font-semibold text-gray-700 mb-2">
                            Assigned To
                        </label>
                        <div class="relative">
                            <select id="editTaskAssignedTo"
                                    name="assigned_to_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-gray-50 focus:bg-white appearance-none">
                                <option value="">Select User</option>
                                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($user->id); ?>"><?php echo e($user->first_name); ?> <?php echo e($user->last_name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Due Date -->
                    <div>
                        <label for="editTaskDueDate" class="block text-sm font-semibold text-gray-700 mb-2">
                            Due Date
                        </label>
                        <div class="relative">
                            <input type="date"
                                   id="editTaskDueDate"
                                   name="due_date"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-gray-50 focus:bg-white">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-calendar text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Status, Priority & Estimated Hours -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Status -->
                    <div>
                        <label for="editTaskStatus" class="block text-sm font-semibold text-gray-700 mb-2">
                            Status
                        </label>
                        <div class="relative">
                            <select id="editTaskStatus"
                                    name="status"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-gray-50 focus:bg-white appearance-none">
                                <option value="pending">📋 Pending</option>
                                <option value="in_progress">🚀 In Progress</option>
                                <option value="completed">✅ Completed</option>
                                <option value="on_hold">⏸️ On Hold</option>
                                <option value="cancelled">❌ Cancelled</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Priority -->
                    <div>
                        <label for="editTaskPriority" class="block text-sm font-semibold text-gray-700 mb-2">
                            Priority
                        </label>
                        <div class="relative">
                            <select id="editTaskPriority"
                                    name="priority"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-gray-50 focus:bg-white appearance-none">
                                <option value="low">🟢 Low</option>
                                <option value="medium">🟡 Medium</option>
                                <option value="high">🔴 High</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-chevron-down text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Estimated Hours -->
                    <div>
                        <label for="editTaskEstimatedHours" class="block text-sm font-semibold text-gray-700 mb-2">
                            Estimated Hours
                        </label>
                        <div class="relative">
                            <input type="number"
                                   id="editTaskEstimatedHours"
                                   name="estimated_hours"
                                   step="0.5"
                                   min="0"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                                   placeholder="1.5">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-clock text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-8 py-4 rounded-b-2xl border-t border-gray-200">
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeEditTaskModal()" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2">
                    <i class="fas fa-times"></i>
                    <span>Cancel</span>
                </button>
                <button type="submit" form="editTaskForm" class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2">
                    <i class="fas fa-save"></i>
                    <span>Update Task</span>
                </button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\workspace\.php\glitchafrica\resources\views/tasks/index.blade.php ENDPATH**/ ?>