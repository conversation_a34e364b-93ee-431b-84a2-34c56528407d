@extends('layouts.app')

@section('title', 'Expenses')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Expenses</h1>
                <div class="btn-group">
                    <a href="{{ route('accounting.dashboard') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Accounting
                    </a>
                    <a href="{{ route('accounting.expenses.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Expense
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('accounting.expenses.index') }}" class="row g-3">
                        <div class="col-md-3">
                            <label for="category" class="form-label">Category</label>
                            <select name="category" id="category" class="form-select">
                                <option value="">All Categories</option>
                                <option value="equipment" {{ request('category') == 'equipment' ? 'selected' : '' }}>Equipment</option>
                                <option value="utilities" {{ request('category') == 'utilities' ? 'selected' : '' }}>Utilities</option>
                                <option value="rent" {{ request('category') == 'rent' ? 'selected' : '' }}>Rent</option>
                                <option value="marketing" {{ request('category') == 'marketing' ? 'selected' : '' }}>Marketing</option>
                                <option value="travel" {{ request('category') == 'travel' ? 'selected' : '' }}>Travel</option>
                                <option value="office_supplies" {{ request('category') == 'office_supplies' ? 'selected' : '' }}>Office Supplies</option>
                                <option value="software" {{ request('category') == 'software' ? 'selected' : '' }}>Software</option>
                                <option value="maintenance" {{ request('category') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                                <option value="other" {{ request('category') == 'other' ? 'selected' : '' }}>Other</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                        <div class="col-md-2">
                            <label for="min_amount" class="form-label">Min Amount</label>
                            <input type="number" name="min_amount" id="min_amount" class="form-control" value="{{ request('min_amount') }}" step="0.01">
                        </div>
                        <div class="col-md-2">
                            <label for="max_amount" class="form-label">Max Amount</label>
                            <input type="number" name="max_amount" id="max_amount" class="form-control" value="{{ request('max_amount') }}" step="0.01">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-1">
                                <button type="submit" class="btn btn-outline-primary btn-sm">Filter</button>
                                <a href="{{ route('accounting.expenses.index') }}" class="btn btn-outline-secondary btn-sm">Clear</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-danger shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Total Expenses</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($summary['total_amount'] ?? 0, 2) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-receipt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">This Month</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($summary['this_month'] ?? 0, 2) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-month fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Average/Month</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">${{ number_format($summary['average_monthly'] ?? 0, 2) }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Count</div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $summary['total_count'] ?? 0 }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-hashtag fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expenses Table -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Expenses List</h6>
                </div>
                <div class="card-body">
                    @if($expenses->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th>Category</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                    <th>Receipt</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($expenses as $expense)
                                <tr>
                                    <td>{{ $expense->expense_date->format('M d, Y') }}</td>
                                    <td>
                                        <div class="fw-bold">{{ $expense->description }}</div>
                                        @if($expense->vendor)
                                        <small class="text-muted">Vendor: {{ $expense->vendor }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ ucfirst(str_replace('_', ' ', $expense->category)) }}</span>
                                    </td>
                                    <td class="text-end">
                                        <strong>${{ number_format($expense->amount, 2) }}</strong>
                                    </td>
                                    <td>{{ ucfirst(str_replace('_', ' ', $expense->payment_method)) }}</td>
                                    <td>
                                        @if($expense->receipt_path)
                                        <a href="{{ Storage::url($expense->receipt_path) }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-file"></i> View
                                        </a>
                                        @else
                                        <span class="text-muted">No receipt</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('accounting.expenses.show', $expense) }}" class="btn btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('accounting.expenses.edit', $expense) }}" class="btn btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" onclick="deleteExpense({{ $expense->id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($expenses->hasPages())
                    <div class="d-flex justify-content-center mt-3">
                        {{ $expenses->appends(request()->query())->links() }}
                    </div>
                    @endif
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No expenses found</h4>
                        <p class="text-muted">Add your first expense to get started.</p>
                        <a href="{{ route('accounting.expenses.create') }}" class="btn btn-primary">Add Expense</a>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Category Breakdown Chart -->
            @if($expenses->count() > 0)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Expenses by Category</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($categoryBreakdown ?? [] as $category => $data)
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="fw-bold">{{ ucfirst(str_replace('_', ' ', $category)) }}</span>
                                <span class="text-muted">${{ number_format($data['amount'], 2) }} ({{ $data['count'] }} items)</span>
                            </div>
                            <div class="progress" style="height: 10px;">
                                <div class="progress-bar" role="progressbar" 
                                     style="width: {{ ($data['amount'] / ($summary['total_amount'] ?: 1)) * 100 }}%">
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Expense</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this expense? This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Expense</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteExpense(expenseId) {
    const form = document.getElementById('deleteForm');
    form.action = `/accounting/expenses/${expenseId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const categorySelect = document.getElementById('category');
    
    categorySelect.addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
@endpush
