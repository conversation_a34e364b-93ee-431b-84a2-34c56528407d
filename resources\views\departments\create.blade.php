@extends('layouts.app')

@section('title', 'Create Department')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Create Department</h1>
                    <p class="mt-1 text-sm text-gray-600">Set up a new department for your studio</p>
                </div>
                <div>
                    <a href="{{ route('departments.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Back to Departments
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form action="{{ route('departments.store') }}" method="POST" class="space-y-6">
            @csrf
            
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                </div>
                <div class="p-6 space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700">Department Name</label>
                        <input type="text" name="name" id="name" value="{{ old('name', $prefill['name'] ?? '') }}" 
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-300 @enderror" 
                               required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="3" 
                                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="color_code" class="block text-sm font-medium text-gray-700">Color Theme</label>
                            <select name="color_code" id="color_code" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('color_code') border-red-300 @enderror">
                                <option value="">Select Color</option>
                                <option value="blue" {{ old('color_code', $prefill['color'] ?? '') === 'blue' ? 'selected' : '' }}>Blue</option>
                                <option value="green" {{ old('color_code', $prefill['color'] ?? '') === 'green' ? 'selected' : '' }}>Green</option>
                                <option value="purple" {{ old('color_code', $prefill['color'] ?? '') === 'purple' ? 'selected' : '' }}>Purple</option>
                                <option value="red" {{ old('color_code', $prefill['color'] ?? '') === 'red' ? 'selected' : '' }}>Red</option>
                                <option value="yellow" {{ old('color_code', $prefill['color'] ?? '') === 'yellow' ? 'selected' : '' }}>Yellow</option>
                                <option value="indigo" {{ old('color_code', $prefill['color'] ?? '') === 'indigo' ? 'selected' : '' }}>Indigo</option>
                                <option value="pink" {{ old('color_code', $prefill['color'] ?? '') === 'pink' ? 'selected' : '' }}>Pink</option>
                                <option value="gray" {{ old('color_code', $prefill['color'] ?? '') === 'gray' ? 'selected' : '' }}>Gray</option>
                            </select>
                            @error('color_code')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="icon" class="block text-sm font-medium text-gray-700">Icon</label>
                            <select name="icon" id="icon" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('icon') border-red-300 @enderror">
                                <option value="">Select Icon</option>
                                <option value="fas fa-headphones" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-headphones' ? 'selected' : '' }}>🎧 Headphones</option>
                                <option value="fas fa-music" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-music' ? 'selected' : '' }}>🎵 Music</option>
                                <option value="fas fa-video" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-video' ? 'selected' : '' }}>📹 Video</option>
                                <option value="fas fa-microphone" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-microphone' ? 'selected' : '' }}>🎤 Microphone</option>
                                <option value="fas fa-users" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-users' ? 'selected' : '' }}>👥 Users</option>
                                <option value="fas fa-building" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-building' ? 'selected' : '' }}>🏢 Building</option>
                                <option value="fas fa-cogs" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-cogs' ? 'selected' : '' }}>⚙️ Cogs</option>
                                <option value="fas fa-chart-line" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-chart-line' ? 'selected' : '' }}>📈 Chart</option>
                                <option value="fas fa-dollar-sign" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-dollar-sign' ? 'selected' : '' }}>💰 Dollar</option>
                                <option value="fas fa-tools" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-tools' ? 'selected' : '' }}>🔧 Tools</option>
                                <option value="fas fa-paint-brush" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-paint-brush' ? 'selected' : '' }}>🎨 Paint Brush</option>
                                <option value="fas fa-camera" {{ old('icon', $prefill['icon'] ?? '') === 'fas fa-camera' ? 'selected' : '' }}>📷 Camera</option>
                            </select>
                            @error('icon')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Status</label>
                        <div class="mt-1">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Active</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Preview</h3>
                </div>
                <div class="p-6">
                    <div id="department-preview" class="max-w-sm">
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="flex items-center">
                                <div id="preview-icon" class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-building text-blue-600 text-xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 id="preview-name" class="text-lg font-medium text-gray-900">Department Name</h3>
                                    <p id="preview-description" class="text-sm text-gray-500">Department description</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Department Settings</h3>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Allow Self-Assignment</label>
                            <div class="mt-1">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="settings[allow_self_assignment]" value="1" {{ old('settings.allow_self_assignment') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Users can assign themselves to this department</span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Require Approval</label>
                            <div class="mt-1">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="settings[require_approval]" value="1" {{ old('settings.require_approval') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">New members require approval</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="settings_max_members" class="block text-sm font-medium text-gray-700">Maximum Members</label>
                        <input type="number" name="settings[max_members]" id="settings_max_members" value="{{ old('settings.max_members') }}" 
                               min="1" placeholder="Leave blank for unlimited"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-3">
                <a href="{{ route('departments.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium">
                    Create Department
                </button>
            </div>
        </form>
    </main>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const nameInput = document.getElementById('name');
        const colorSelect = document.getElementById('color_code');
        const iconSelect = document.getElementById('icon');
        const descriptionInput = document.getElementById('description');

        const previewName = document.getElementById('preview-name');
        const previewDescription = document.getElementById('preview-description');
        const previewIcon = document.getElementById('preview-icon');

        function updatePreview() {
            const name = nameInput.value || 'Department Name';
            const description = descriptionInput.value || 'Department description';
            const color = colorSelect.value || 'blue';
            const icon = iconSelect.value || 'fas fa-building';

            previewName.textContent = name;
            previewDescription.textContent = description;
            
            // Update icon container
            previewIcon.className = `w-12 h-12 bg-${color}-100 rounded-lg flex items-center justify-center`;
            previewIcon.innerHTML = `<i class="${icon} text-${color}-600 text-xl"></i>`;
        }

        nameInput.addEventListener('input', updatePreview);
        colorSelect.addEventListener('change', updatePreview);
        iconSelect.addEventListener('change', updatePreview);
        descriptionInput.addEventListener('input', updatePreview);

        // Initial preview update
        updatePreview();
    });
</script>
@endpush
