@extends('layouts.app')

@section('title', 'Add Client')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Add New Client</h1>
                <a href="{{ route('clients.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Clients
                </a>
            </div>

            <div class="card shadow">
                <div class="card-body">
                    <form action="{{ route('clients.store') }}" method="POST" enctype="multipart/form-data" id="clientForm">
                        @csrf
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Basic Information</h5>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="phone" class="form-label">Phone Number</label>
                                            <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                                   id="phone" name="phone" value="{{ old('phone') }}">
                                            @error('phone')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="type" class="form-label">Client Type</label>
                                            <select class="form-select @error('type') is-invalid @enderror" id="type" name="type">
                                                <option value="individual" {{ old('type', 'individual') == 'individual' ? 'selected' : '' }}>Individual</option>
                                                <option value="band" {{ old('type') == 'band' ? 'selected' : '' }}>Band</option>
                                                <option value="label" {{ old('type') == 'label' ? 'selected' : '' }}>Record Label</option>
                                                <option value="company" {{ old('type') == 'company' ? 'selected' : '' }}>Company</option>
                                            </select>
                                            @error('type')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="company" class="form-label">Company/Band Name</label>
                                    <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                           id="company" name="company" value="{{ old('company') }}" 
                                           placeholder="Optional - if representing a company or band">
                                    @error('company')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="website" class="form-label">Website</label>
                                    <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                           id="website" name="website" value="{{ old('website') }}" 
                                           placeholder="https://example.com">
                                    @error('website')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="profile_picture" class="form-label">Profile Picture</label>
                                    <input type="file" class="form-control @error('profile_picture') is-invalid @enderror" 
                                           id="profile_picture" name="profile_picture" accept="image/*">
                                    <div class="form-text">Upload a profile picture (JPG, PNG - Max 2MB)</div>
                                    @error('profile_picture')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Profile Picture Preview -->
                                <div id="picture-preview" style="display: none;">
                                    <div class="mb-3">
                                        <label class="form-label">Preview</label>
                                        <div class="border rounded p-2">
                                            <img id="preview-image" src="" alt="Profile Preview" class="img-fluid rounded" style="max-height: 150px;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact & Additional Info -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Contact & Additional Information</h5>
                                
                                <div class="mb-3">
                                    <label for="address" class="form-label">Address</label>
                                    <textarea class="form-control @error('address') is-invalid @enderror" 
                                              id="address" name="address" rows="3" 
                                              placeholder="Full address including city, state, zip">{{ old('address') }}</textarea>
                                    @error('address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="city" class="form-label">City</label>
                                            <input type="text" class="form-control @error('city') is-invalid @enderror" 
                                                   id="city" name="city" value="{{ old('city') }}">
                                            @error('city')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="country" class="form-label">Country</label>
                                            <input type="text" class="form-control @error('country') is-invalid @enderror" 
                                                   id="country" name="country" value="{{ old('country') }}">
                                            @error('country')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="social_media" class="form-label">Social Media Links</label>
                                    <div id="social-media-container">
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">
                                                <i class="fab fa-instagram"></i>
                                            </span>
                                            <input type="url" class="form-control" name="social_media[instagram]" 
                                                   placeholder="Instagram URL" value="{{ old('social_media.instagram') }}">
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">
                                                <i class="fab fa-twitter"></i>
                                            </span>
                                            <input type="url" class="form-control" name="social_media[twitter]" 
                                                   placeholder="Twitter URL" value="{{ old('social_media.twitter') }}">
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">
                                                <i class="fab fa-facebook"></i>
                                            </span>
                                            <input type="url" class="form-control" name="social_media[facebook]" 
                                                   placeholder="Facebook URL" value="{{ old('social_media.facebook') }}">
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">
                                                <i class="fab fa-youtube"></i>
                                            </span>
                                            <input type="url" class="form-control" name="social_media[youtube]" 
                                                   placeholder="YouTube URL" value="{{ old('social_media.youtube') }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="genres" class="form-label">Music Genres</label>
                                    <input type="text" class="form-control @error('genres') is-invalid @enderror" 
                                           id="genres" name="genres" value="{{ old('genres') }}" 
                                           placeholder="e.g., Hip Hop, R&B, Pop (comma separated)">
                                    <div class="form-text">Enter genres separated by commas</div>
                                    @error('genres')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="preferred_contact_method" class="form-label">Preferred Contact Method</label>
                                    <select class="form-select @error('preferred_contact_method') is-invalid @enderror" 
                                            id="preferred_contact_method" name="preferred_contact_method">
                                        <option value="email" {{ old('preferred_contact_method', 'email') == 'email' ? 'selected' : '' }}>Email</option>
                                        <option value="phone" {{ old('preferred_contact_method') == 'phone' ? 'selected' : '' }}>Phone</option>
                                        <option value="text" {{ old('preferred_contact_method') == 'text' ? 'selected' : '' }}>Text Message</option>
                                        <option value="whatsapp" {{ old('preferred_contact_method') == 'whatsapp' ? 'selected' : '' }}>WhatsApp</option>
                                    </select>
                                    @error('preferred_contact_method')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes" name="notes" rows="4" 
                                              placeholder="Any additional notes about this client, preferences, special requirements, etc.">{{ old('notes') }}</textarea>
                                    @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Client Settings</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_vip" 
                                               name="is_vip" value="1" {{ old('is_vip') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_vip">
                                            VIP Client
                                        </label>
                                        <div class="form-text">VIP clients get priority booking and special rates</div>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_marketing_emails" 
                                               name="send_marketing_emails" value="1" {{ old('send_marketing_emails', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="send_marketing_emails">
                                            Send Marketing Emails
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_welcome_email" 
                                               name="send_welcome_email" value="1" {{ old('send_welcome_email', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="send_welcome_email">
                                            Send Welcome Email
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('clients.index') }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" name="action" value="save" class="btn btn-outline-primary">Save Client</button>
                                    <button type="submit" name="action" value="save_and_create_project" class="btn btn-primary">Save & Create Project</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Profile picture preview
document.getElementById('profile_picture').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('picture-preview');
    const image = document.getElementById('preview-image');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            image.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});

// Auto-format phone number
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length >= 6) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    } else if (value.length >= 3) {
        value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
    }
    e.target.value = value;
});

// Form validation
document.getElementById('clientForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    
    if (!name || !email) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('Please enter a valid email address.');
        return false;
    }
    
    // Check file size if profile picture is uploaded
    const profilePicture = document.getElementById('profile_picture').files[0];
    if (profilePicture && profilePicture.size > 2 * 1024 * 1024) { // 2MB
        e.preventDefault();
        alert('Profile picture file size must be less than 2MB.');
        return false;
    }
});

// Auto-populate company field based on type
document.getElementById('type').addEventListener('change', function() {
    const companyField = document.getElementById('company');
    const companyLabel = companyField.previousElementSibling;
    
    switch(this.value) {
        case 'band':
            companyLabel.textContent = 'Band Name';
            companyField.placeholder = 'Enter band name';
            break;
        case 'label':
            companyLabel.textContent = 'Record Label Name';
            companyField.placeholder = 'Enter record label name';
            break;
        case 'company':
            companyLabel.textContent = 'Company Name';
            companyField.placeholder = 'Enter company name';
            break;
        default:
            companyLabel.textContent = 'Company/Band Name';
            companyField.placeholder = 'Optional - if representing a company or band';
    }
});

// Genre tags functionality
document.getElementById('genres').addEventListener('input', function(e) {
    // You can add tag validation or formatting here
    this.value = this.value.toLowerCase();
});

// Website URL validation
document.getElementById('website').addEventListener('blur', function(e) {
    if (this.value && !this.value.startsWith('http')) {
        this.value = 'https://' + this.value;
    }
});
</script>
@endpush
