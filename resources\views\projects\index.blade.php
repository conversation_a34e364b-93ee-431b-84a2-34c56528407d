@extends('layouts.dashboard')

@section('title', 'Projects')

@push('styles')
<style>
.project-card {
    transition: all 0.3s ease;
}
.project-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Modal animations */
.modal-content {
    animation: modalSlideIn 0.3s ease-out forwards;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Custom slider styling */
.slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3B82F6, #6366F1);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.6);
}

.slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3B82F6, #6366F1);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.4);
}
</style>
@endpush

@section('dashboard-content')
<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Projects</h1>
    <button onclick="openCreateModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
        <i class="fas fa-plus"></i>
        <span>New Project</span>
    </button>
</div>

<!-- Filters -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="statusFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Statuses</option>
                    <option value="draft">Draft</option>
                    <option value="active">Active</option>
                    <option value="on_hold">On Hold</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                <select id="typeFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Types</option>
                    <option value="music">Music</option>
                    <option value="podcast">Podcast</option>
                    <option value="video">Video</option>
                    <option value="live_session">Live Session</option>
                    <option value="mixing">Mixing</option>
                    <option value="mastering">Mastering</option>
                    <option value="other">Other</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Client</label>
                <select id="clientFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Clients</option>
                    @foreach($clients as $client)
                        <option value="{{ $client->id }}">{{ $client->name }}</option>
                    @endforeach
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" id="searchFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="Search projects...">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                <button onclick="applyFilters()" class="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">Filter</button>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg border-l-4 border-blue-500">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-project-diagram text-blue-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate uppercase">Total Projects</dt>
                        <dd class="text-lg font-semibold text-gray-900" id="totalProjects">{{ $summary['total'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg border-l-4 border-yellow-500">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-spinner text-yellow-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate uppercase">In Progress</dt>
                        <dd class="text-lg font-semibold text-gray-900" id="inProgressProjects">{{ $summary['in_progress'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg border-l-4 border-green-500">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate uppercase">Completed</dt>
                        <dd class="text-lg font-semibold text-gray-900" id="completedProjects">{{ $summary['completed'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg border-l-4 border-purple-500">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-dollar-sign text-purple-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate uppercase">Total Value</dt>
                        <dd class="text-lg font-semibold text-gray-900" id="totalValue">₦{{ number_format($summary['total_value'] ?? 0, 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Projects Grid -->
<div id="projectsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Projects will be loaded here via AJAX -->
</div>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="flex justify-center items-center py-12 hidden">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
</div>

<!-- Pagination -->
<div id="paginationContainer" class="flex justify-center mt-8">
    <!-- Pagination will be loaded here -->
</div>

<!-- Create/Edit Project Modal -->
<div id="projectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Create Project</h3>
            <button onclick="closeProjectModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="projectForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Project Title *</label>
                    <input type="text" name="title" id="projectTitle" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Client *</label>
                    <select name="client_id" id="projectClient" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="">Select Client</option>
                        @foreach($clients as $client)
                            <option value="{{ $client->id }}">{{ $client->name }}</option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Project Type</label>
                    <select name="type" id="projectType" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="music">Music</option>
                        <option value="podcast">Podcast</option>
                        <option value="video">Video</option>
                        <option value="live_session">Live Session</option>
                        <option value="mixing">Mixing</option>
                        <option value="mastering">Mastering</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="projectStatus" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="draft">Draft</option>
                        <option value="active">Active</option>
                        <option value="on_hold">On Hold</option>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Budget (₦)</label>
                    <input type="number" name="budget" id="projectBudget" step="0.01" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Deadline</label>
                    <input type="date" name="deadline" id="projectDeadline" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea name="description" id="projectDescription" rows="4" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
            </div>

            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeProjectModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <span id="submitButtonText">Create Project</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Project Modal -->
<div id="viewProjectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Project Details</h3>
            <button onclick="closeViewModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div id="projectDetails">
            <!-- Project details will be loaded here -->
        </div>
    </div>
</div>

<!-- Update Progress Modal -->
<div id="progressModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full hidden z-50 flex items-center justify-center p-4">
    <div class="relative w-full max-w-lg bg-white rounded-2xl shadow-2xl transform transition-all duration-300 scale-95 opacity-0 modal-content">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6 rounded-t-2xl">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="bg-white bg-opacity-20 p-2 rounded-lg">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-white">Update Progress</h3>
                </div>
                <button onclick="closeProgressModal()" class="text-white hover:text-gray-200 transition-colors duration-200 p-2 hover:bg-white hover:bg-opacity-20 rounded-lg">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Content -->
        <div class="p-8">
            <form id="progressForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-3">Progress (%)</label>
                    <div class="bg-gray-50 p-4 rounded-xl">
                        <input type="range" id="progressSlider" name="progress" min="0" max="100" value="0"
                               class="w-full h-3 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-lg appearance-none cursor-pointer slider"
                               oninput="updateProgressValue(this.value)">
                        <div class="flex justify-between text-sm text-gray-600 mt-2 font-medium">
                            <span>0%</span>
                            <span id="progressValue" class="text-indigo-600 font-bold">0%</span>
                            <span>100%</span>
                        </div>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-semibold text-gray-700 mb-3">Progress Notes</label>
                    <textarea id="progressNotes" name="notes" rows="4"
                              class="w-full border-2 border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 resize-none"
                              placeholder="Optional notes about the progress update..."></textarea>
                </div>

                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-100">
                    <button type="button" onclick="closeProgressModal()" class="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-xl transition-all duration-200 hover:shadow-md">
                        Cancel
                    </button>
                    <button type="submit" id="progressSubmitBtn" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-xl transition-all duration-200 hover:shadow-lg flex items-center space-x-2">
                        <span id="progressSubmitText">Update Progress</span>
                        <div id="progressSpinner" class="hidden">
                            <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Project</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Are you sure you want to delete this project?</p>
                <p class="text-sm text-red-600 mt-2"><strong>Warning:</strong> All associated tasks, files, and data will be permanently deleted.</p>
            </div>
            <div class="flex justify-center space-x-4 mt-4">
                <button type="button" onclick="closeDeleteModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded">Cancel</button>
                <button type="button" onclick="confirmDelete()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">Delete Project</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let currentPage = 1;
let currentProjectId = null;

// Setup CSRF token for AJAX requests
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

// Load projects on page load
$(document).ready(function() {
    console.log('Document ready, loading projects...');
    loadProjects();

    // Auto-apply filters on change
    $('#statusFilter, #typeFilter, #clientFilter').change(function() {
        currentPage = 1;
        loadProjects();
    });

    // Search with debounce
    let searchTimeout;
    $('#searchFilter').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            currentPage = 1;
            loadProjects();
        }, 500);
    });
});

// Load projects via AJAX
function loadProjects() {
    console.log('Loading projects...');
    $('#loadingSpinner').removeClass('hidden');
    $('#projectsContainer').addClass('opacity-50');

    const filters = {
        status: $('#statusFilter').val(),
        type: $('#typeFilter').val(),
        client_id: $('#clientFilter').val(),
        search: $('#searchFilter').val(),
        page: currentPage
    };

    console.log('Filters:', filters);

    $.ajax({
        url: '{{ route("projects.index") }}',
        method: 'GET',
        data: filters,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .done(function(response) {
        console.log('Projects response:', response);
        console.log('Response data structure:', response.data);
        if (response.success) {
            console.log('Rendering projects...');
            console.log('Projects:', response.data.projects);
            console.log('Summary:', response.data.summary);
            renderProjects(response.data.projects);
            renderPagination(response.data.pagination);
            updateSummaryCards(response.data.summary);
            console.log('Projects rendered successfully');
        } else {
            console.error('Response error:', response);
            showNotification('Error loading projects: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('AJAX Error:', xhr);
        console.error('Response Text:', xhr.responseText);
        let errorMessage = 'Error loading projects';
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage += ': ' + xhr.responseJSON.message;
        } else if (xhr.status) {
            errorMessage += ' (Status: ' + xhr.status + ')';
        }
        showNotification(errorMessage, 'error');
    })
    .always(function() {
        console.log('Hiding loading spinner...');
        $('#loadingSpinner').addClass('hidden').hide();
        $('#projectsContainer').removeClass('opacity-50');
        console.log('Loading spinner hidden');
    });
}

// Render projects grid
function renderProjects(projects) {
    console.log('renderProjects called with:', projects);
    const container = $('#projectsContainer');

    if (projects.length === 0) {
        console.log('No projects found, showing empty state');
        container.html(`
            <div class="col-span-full text-center py-12">
                <i class="fas fa-project-diagram text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
                <p class="text-gray-500 mb-4">Create your first project to get started.</p>
                <button onclick="openCreateModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">Create Project</button>
            </div>
        `);
        return;
    }

    let html = '';
    projects.forEach(project => {
        const statusColor = getStatusColor(project.status);
        const progressColor = getProgressColor(project.progress_percentage || 0);

        html += `
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-start mb-4">
                        <h3 class="text-lg font-medium text-gray-900">${project.title}</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}">
                            ${project.status.replace('_', ' ').toUpperCase()}
                        </span>
                    </div>

                    <div class="mb-4">
                        <p class="text-sm text-gray-500 mb-2">Client: <span class="font-medium text-gray-900">${project.client?.name || 'No client assigned'}</span></p>
                        <p class="text-sm text-gray-600">${project.description ? project.description.substring(0, 100) + '...' : 'No description'}</p>
                    </div>

                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>${project.progress_percentage || 0}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full ${progressColor}" style="width: ${project.progress_percentage || 0}%"></div>
                        </div>
                    </div>

                    <div class="grid grid-cols-3 gap-4 text-center border-t border-gray-200 pt-4 mb-4">
                        <div>
                            <div class="text-lg font-semibold text-blue-600">${project.tasks_count || 0}</div>
                            <div class="text-xs text-gray-500">Tasks</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-purple-600">${project.team_members_count || 0}</div>
                            <div class="text-xs text-gray-500">Members</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-green-600">₦${formatNumber(project.budget || 0)}</div>
                            <div class="text-xs text-gray-500">Budget</div>
                        </div>
                    </div>

                    ${project.deadline ? `
                        <div class="mb-4 text-sm">
                            <span class="text-gray-500">Deadline: </span>
                            <span class="font-medium ${new Date(project.deadline) < new Date() ? 'text-red-600' : 'text-gray-900'}">
                                ${formatDate(project.deadline)}
                                ${new Date(project.deadline) < new Date() ? '<i class="fas fa-exclamation-triangle text-red-500 ml-1"></i>' : ''}
                            </span>
                        </div>
                    ` : ''}

                    <div class="flex space-x-2">
                        <button onclick="viewProject(${project.id})" class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white text-center py-2 px-3 rounded text-sm">View</button>
                        <button onclick="editProject(${project.id})" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 text-center py-2 px-3 rounded text-sm">Edit</button>
                        <button onclick="updateProgress(${project.id})" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm">Progress</button>
                        <button onclick="deleteProject(${project.id})" class="bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded text-sm">Delete</button>
                    </div>
                </div>
            </div>
        `;
    });

    container.html(html);
}
// Helper functions
function getStatusColor(status) {
    const colors = {
        'draft': 'bg-gray-100 text-gray-800',
        'active': 'bg-blue-100 text-blue-800',
        'on_hold': 'bg-yellow-100 text-yellow-800',
        'completed': 'bg-green-100 text-green-800',
        'cancelled': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
}

function getProgressColor(progress) {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-yellow-500';
    if (progress >= 25) return 'bg-orange-500';
    return 'bg-red-500';
}

function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Modal functions
function openCreateModal() {
    $('#modalTitle').text('Create Project');
    $('#submitButtonText').text('Create Project');
    $('#projectForm')[0].reset();
    currentProjectId = null;
    $('#projectModal').removeClass('hidden');
}

function editProject(projectId) {
    currentProjectId = projectId;
    $('#modalTitle').text('Edit Project');
    $('#submitButtonText').text('Update Project');

    // Load project data
    $.get(`/projects/${projectId}`)
        .done(function(response) {
            if (response.success) {
                const project = response.data.project; // Fix: access project from data.project
                $('#projectTitle').val(project.title || '');
                $('#projectClient').val(project.client_id || '');
                $('#projectType').val(project.type || 'music');
                $('#projectStatus').val(project.status || 'draft');
                $('#projectBudget').val(project.budget || '');
                $('#projectDeadline').val(project.deadline ? project.deadline.split('T')[0] : '');
                $('#projectDescription').val(project.description || '');
                $('#projectModal').removeClass('hidden');
            } else {
                showNotification('Error loading project: ' + (response.message || 'Unknown error'), 'error');
            }
        })
        .fail(function(xhr) {
            console.error('Error loading project:', xhr);
            let errorMessage = 'Error loading project data';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage += ': ' + xhr.responseJSON.message;
            }
            showNotification(errorMessage, 'error');
        });
}

function closeProjectModal() {
    $('#projectModal').addClass('hidden');
    currentProjectId = null;
}

function viewProject(projectId) {
    $.get(`/projects/${projectId}`)
        .done(function(response) {
            console.log('Project details response:', response);
            if (response.success) {
                renderProjectDetails(response.data.project);
                $('#viewProjectModal').removeClass('hidden');
            } else {
                showNotification('Error loading project: ' + (response.message || 'Unknown error'), 'error');
            }
        })
        .fail(function(xhr) {
            console.error('Error loading project details:', xhr);
            let errorMessage = 'Error loading project details';
            if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage += ': ' + xhr.responseJSON.message;
            }
            showNotification(errorMessage, 'error');
        });
}

function closeViewModal() {
    $('#viewProjectModal').addClass('hidden');
}

function updateProgress(projectId) {
    currentProjectId = projectId;
    $('#progressModal').removeClass('hidden');
}

function closeProgressModal() {
    $('#progressModal').addClass('hidden');
    currentProjectId = null;
}

function updateProgressValue(value) {
    $('#progressValue').text(value + '%');
}

function updateProjectProgress(projectId, currentProgress) {
    currentProjectId = projectId;
    $('#progress').val(currentProgress);
    $('#progressValue').text(currentProgress + '%');
    $('#progressModal').removeClass('hidden');
}

function deleteProject(projectId) {
    currentProjectId = projectId;
    $('#deleteModal').removeClass('hidden');
}

function closeDeleteModal() {
    $('#deleteModal').addClass('hidden');
    currentProjectId = null;
}

function confirmDelete() {
    if (!currentProjectId) return;

    $.ajax({
        url: `/projects/${currentProjectId}`,
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            showNotification('Project deleted successfully', 'success');
            closeDeleteModal();
            loadProjects();
        }
    })
    .fail(function() {
        showNotification('Error deleting project', 'error');
    });
}

// Form submissions
$('#projectForm').on('submit', function(e) {
    e.preventDefault();
    console.log('Form submitted');

    const formData = new FormData(this);
    const url = currentProjectId ? `/projects/${currentProjectId}` : '/projects';
    const method = currentProjectId ? 'PUT' : 'POST';

    console.log('Submitting to:', url, 'Method:', method);

    if (currentProjectId) {
        formData.append('_method', 'PUT');
    }

    // Show loading state
    const submitBtn = $('#projectForm button[type="submit"]');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true).text('Saving...');

    $.ajax({
        url: url,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        console.log('Form response:', response);
        if (response.success) {
            showNotification(currentProjectId ? 'Project updated successfully' : 'Project created successfully', 'success');
            closeProjectModal();
            loadProjects();
        } else {
            console.error('Form error:', response);
            showNotification('Error: ' + (response.message || 'Unknown error'), 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Form submission failed:', xhr);
        console.error('Response text:', xhr.responseText);
        const errors = xhr.responseJSON?.errors;
        if (errors) {
            let errorMessage = 'Please fix the following errors:\n';
            Object.values(errors).forEach(error => {
                errorMessage += '- ' + error[0] + '\n';
            });
            showNotification(errorMessage, 'error');
        } else {
            showNotification('Error saving project (Status: ' + xhr.status + ')', 'error');
        }
    })
    .always(function() {
        // Reset button state
        submitBtn.prop('disabled', false).text(originalText);
    });
});

$('#progressForm').on('submit', function(e) {
    e.preventDefault();

    if (!currentProjectId) return;

    // Show loading state
    const submitBtn = $('#progressSubmitBtn');
    const submitText = $('#progressSubmitText');
    const spinner = $('#progressSpinner');

    submitBtn.prop('disabled', true);
    submitText.text('Updating...');
    spinner.removeClass('hidden');

    const formData = {
        progress: $('#progressSlider').val(),
        notes: $('#progressNotes').val()
    };

    $.ajax({
        url: `/projects/${currentProjectId}/update-progress`,
        method: 'POST',
        data: formData,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            showNotification('Progress updated successfully', 'success');
            closeProgressModal();
            loadProjects();
        } else {
            showNotification('Error: ' + (response.message || 'Unknown error'), 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Progress update failed:', xhr);
        let errorMessage = 'Error updating progress';
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage += ': ' + xhr.responseJSON.message;
        } else if (xhr.status) {
            errorMessage += ' (Status: ' + xhr.status + ')';
        }
        showNotification(errorMessage, 'error');
    })
    .always(function() {
        // Reset button state
        submitBtn.prop('disabled', false);
        submitText.text('Update Progress');
        spinner.addClass('hidden');
    });
});

// Utility functions
function applyFilters() {
    currentPage = 1;
    loadProjects();
}

function renderPagination(pagination) {
    // Implementation for pagination rendering
    $('#paginationContainer').html(''); // Simplified for now
}

function updateSummaryCards(summary) {
    console.log('updateSummaryCards called with:', summary);

    if (!summary) {
        console.error('Summary is undefined or null');
        return;
    }

    $('#totalProjects').text(summary.total || 0);
    $('#inProgressProjects').text(summary.in_progress || 0);
    $('#completedProjects').text(summary.completed || 0);
    $('#totalValue').text('₦' + formatNumber(summary.total_value || 0));
}

function renderProjectDetails(project) {
    console.log('Rendering project details:', project);

    const statusColor = getStatusColor(project.status);
    const progressColor = getProgressColor(project.progress_percentage || 0);

    $('#projectDetails').html(`
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex justify-between items-start">
                <div>
                    <h4 class="text-2xl font-bold text-gray-900">${project.title || 'Untitled Project'}</h4>
                    <p class="text-gray-600 mt-1">${project.description || 'No description available'}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusColor}">
                            ${(project.status || 'draft').replace('_', ' ').toUpperCase()}
                        </span>
                        <span class="text-sm text-gray-500">ID: ${project.id}</span>
                        <span class="text-sm text-gray-500">Created: ${formatDate(project.created_at)}</span>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <button onclick="editProject(${project.id})" class="bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1 rounded text-sm">
                        <i class="fas fa-edit mr-1"></i>Edit
                    </button>
                    <button onclick="deleteProject(${project.id})" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                        <i class="fas fa-trash mr-1"></i>Delete
                    </button>
                </div>
            </div>

            <!-- Basic Info -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Client</label>
                        <div class="mt-1">
                            ${project.client ? `
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                        ${project.client.name.charAt(0)}
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">${project.client.name}</p>
                                        <p class="text-sm text-gray-500">${project.client.email}</p>
                                        ${project.client.company ? `<p class="text-xs text-gray-400">${project.client.company}</p>` : ''}
                                    </div>
                                </div>
                            ` : '<p class="text-sm text-gray-500">No client assigned</p>'}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Project Manager</label>
                        <div class="mt-1">
                            ${project.project_manager ? `
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                        ${project.project_manager.first_name.charAt(0)}${project.project_manager.last_name.charAt(0)}
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">${project.project_manager.first_name} ${project.project_manager.last_name}</p>
                                        <p class="text-sm text-gray-500">${project.project_manager.email}</p>
                                    </div>
                                </div>
                            ` : '<p class="text-sm text-gray-500">Not assigned</p>'}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Type & Priority</label>
                        <div class="mt-1 flex space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                ${(project.type || 'Not specified').replace('_', ' ').toUpperCase()}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                project.priority === 'high' ? 'bg-red-100 text-red-800' :
                                project.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                            }">
                                ${(project.priority || 'medium').toUpperCase()}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Budget & Cost</label>
                        <div class="mt-1 space-y-1">
                            <p class="text-sm text-gray-900">Budget: <span class="font-medium">₦${formatNumber(project.budget || 0)}</span></p>
                            <p class="text-sm text-gray-900">Total Cost: <span class="font-medium">₦${formatNumber(project.total_cost || 0)}</span></p>
                            <p class="text-xs text-gray-500">Remaining: ₦${formatNumber((project.budget || 0) - (project.total_cost || 0))}</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Timeline</label>
                        <div class="mt-1 space-y-1">
                            <p class="text-sm text-gray-900">Start: ${project.start_date ? formatDate(project.start_date) : '<span class="text-gray-500">Not set</span>'}</p>
                            <p class="text-sm text-gray-900">Deadline: ${project.deadline ? `<span class="${new Date(project.deadline) < new Date() ? 'text-red-600 font-medium' : ''}">${formatDate(project.deadline)}</span>` : '<span class="text-gray-500">Not set</span>'}</p>
                            ${project.completed_at ? `<p class="text-sm text-gray-900">Completed: ${formatDate(project.completed_at)}</p>` : ''}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Slug</label>
                        <p class="mt-1 text-sm text-gray-500 font-mono">${project.slug || 'Not generated'}</p>
                    </div>
                </div>
            </div>

            <!-- Progress -->
            <div>
                <div class="flex justify-between items-center mb-2">
                    <label class="block text-sm font-medium text-gray-700">Progress</label>
                    <button onclick="updateProjectProgress(${project.id}, ${project.progress_percentage || 0})" class="text-indigo-600 hover:text-indigo-900 text-sm">
                        <i class="fas fa-edit mr-1"></i>Update
                    </button>
                </div>
                <div class="flex items-center">
                    <div class="flex-1 bg-gray-200 rounded-full h-3 mr-3">
                        <div class="h-3 rounded-full ${progressColor}" style="width: ${project.progress_percentage || 0}%"></div>
                    </div>
                    <span class="text-sm font-medium text-gray-900">${project.progress_percentage || 0}%</span>
                </div>
            </div>

            <!-- Requirements & Deliverables -->
            ${project.requirements || project.deliverables ? `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    ${project.requirements ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Requirements</label>
                            <div class="bg-gray-50 rounded-lg p-3">
                                <p class="text-sm text-gray-700 whitespace-pre-wrap">${project.requirements}</p>
                            </div>
                        </div>
                    ` : ''}
                    ${project.deliverables ? `
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Deliverables</label>
                            <div class="bg-gray-50 rounded-lg p-3">
                                <p class="text-sm text-gray-700 whitespace-pre-wrap">${project.deliverables}</p>
                            </div>
                        </div>
                    ` : ''}
                </div>
            ` : ''}

            <!-- Team Members -->
            ${project.team_members && project.team_members.length > 0 ? `
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Team Members (${project.team_members.length})</label>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        ${project.team_members.map(member => `
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0 h-8 w-8 bg-indigo-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                    ${member.first_name.charAt(0)}${member.last_name.charAt(0)}
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">${member.first_name} ${member.last_name}</p>
                                    <p class="text-sm text-gray-500">${member.email}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : '<div><p class="text-sm text-gray-500">No team members assigned</p></div>'}

            <!-- Tasks Summary -->
            ${project.tasks && project.tasks.length > 0 ? `
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tasks (${project.tasks.length})</label>
                    <div class="space-y-2 max-h-40 overflow-y-auto">
                        ${project.tasks.slice(0, 5).map(task => `
                            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <span class="text-sm text-gray-900">${task.title}</span>
                                <span class="text-xs px-2 py-1 rounded-full ${getTaskStatusColor(task.status)}">
                                    ${(task.status || 'pending').replace('_', ' ').toUpperCase()}
                                </span>
                            </div>
                        `).join('')}
                        ${project.tasks.length > 5 ? `<p class="text-sm text-gray-500">... and ${project.tasks.length - 5} more tasks</p>` : ''}
                    </div>
                </div>
            ` : ''}
        </div>
    `);
}

function getTaskStatusColor(status) {
    const colors = {
        'pending': 'bg-gray-100 text-gray-800',
        'in_progress': 'bg-blue-100 text-blue-800',
        'completed': 'bg-green-100 text-green-800',
        'cancelled': 'bg-red-100 text-red-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
}

function showNotification(message, type) {
    // Simple notification system
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const notification = $(`
        <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50">
            ${message}
        </div>
    `);

    $('body').append(notification);
    setTimeout(() => notification.remove(), 3000);
}
</script>
@endpush
