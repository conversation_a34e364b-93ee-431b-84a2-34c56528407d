@extends('layouts.app')

@section('title', 'Create Workflow')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Create Workflow</h1>
                    <p class="mt-1 text-sm text-gray-600">Design a new project workflow template</p>
                </div>
                <div>
                    <a href="{{ route('workflows.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Back to Workflows
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form action="{{ route('workflows.store') }}" method="POST" class="space-y-6">
            @csrf
            
            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Basic Information</h3>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Workflow Name</label>
                            <input type="text" name="name" id="name" value="{{ old('name') }}" 
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('name') border-red-300 @enderror" 
                                   required>
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div>
                            <label for="project_type" class="block text-sm font-medium text-gray-700">Project Type</label>
                            <select name="project_type" id="project_type" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('project_type') border-red-300 @enderror" 
                                    required>
                                <option value="">Select Project Type</option>
                                <option value="music_production" {{ old('project_type', request('template')) === 'music_production' ? 'selected' : '' }}>Music Production</option>
                                <option value="video_production" {{ old('project_type', request('template')) === 'video_production' ? 'selected' : '' }}>Video Production</option>
                                <option value="podcast" {{ old('project_type', request('template')) === 'podcast' ? 'selected' : '' }}>Podcast</option>
                                <option value="mixing_mastering" {{ old('project_type', request('template')) === 'mixing_mastering' ? 'selected' : '' }}>Mixing & Mastering</option>
                                <option value="voice_over" {{ old('project_type') === 'voice_over' ? 'selected' : '' }}>Voice Over</option>
                                <option value="sound_design" {{ old('project_type') === 'sound_design' ? 'selected' : '' }}>Sound Design</option>
                                <option value="live_recording" {{ old('project_type') === 'live_recording' ? 'selected' : '' }}>Live Recording</option>
                            </select>
                            @error('project_type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea name="description" id="description" rows="3" 
                                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 @error('description') border-red-300 @enderror">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Default Workflow</label>
                            <div class="mt-1">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="is_default" value="1" {{ old('is_default') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Use as default for this project type</span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <div class="mt-1">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" name="is_active" value="1" {{ old('is_active', true) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">Active</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Workflow Stages -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Workflow Stages</h3>
                            <p class="mt-1 text-sm text-gray-600">Define the stages of your workflow</p>
                        </div>
                        <button type="button" onclick="addStage()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-md text-sm font-medium">
                            Add Stage
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="stages-container" class="space-y-4">
                        <!-- Stages will be added here dynamically -->
                    </div>
                    @error('stages')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Automation Rules -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">Automation Rules</h3>
                            <p class="mt-1 text-sm text-gray-600">Optional automation rules for this workflow</p>
                        </div>
                        <button type="button" onclick="addAutomationRule()" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md text-sm font-medium">
                            Add Rule
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div id="automation-container" class="space-y-4">
                        <!-- Automation rules will be added here dynamically -->
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-end space-x-3">
                <a href="{{ route('workflows.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium">
                    Cancel
                </a>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium">
                    Create Workflow
                </button>
            </div>
        </form>
    </main>
</div>
@endsection

@push('scripts')
<script>
    let stageCount = 0;
    let automationCount = 0;

    // Add initial stage
    document.addEventListener('DOMContentLoaded', function() {
        addStage();
        
        // Load template if specified
        const template = '{{ request("template") }}';
        if (template) {
            loadTemplate(template);
        }
    });

    function addStage() {
        stageCount++;
        const container = document.getElementById('stages-container');
        const stageDiv = document.createElement('div');
        stageDiv.className = 'border border-gray-200 rounded-lg p-4';
        stageDiv.id = `stage-${stageCount}`;
        
        stageDiv.innerHTML = `
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-md font-medium text-gray-900">Stage ${stageCount}</h4>
                <button type="button" onclick="removeStage(${stageCount})" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Stage Name</label>
                    <input type="text" name="stages[${stageCount}][name]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Order</label>
                    <input type="number" name="stages[${stageCount}][order]" value="${stageCount}" min="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Duration (days)</label>
                    <input type="number" name="stages[${stageCount}][estimated_duration]" min="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
            </div>
            
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700">Description</label>
                <textarea name="stages[${stageCount}][description]" rows="2" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
            </div>
            
            <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Required Skills (comma-separated)</label>
                    <input type="text" name="stages[${stageCount}][required_skills]" placeholder="audio editing, mixing" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Department</label>
                    <select name="stages[${stageCount}][department_id]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">Any Department</option>
                        <!-- Add department options here -->
                    </select>
                </div>
            </div>
        `;
        
        container.appendChild(stageDiv);
    }

    function removeStage(stageId) {
        const stageDiv = document.getElementById(`stage-${stageId}`);
        if (stageDiv) {
            stageDiv.remove();
        }
    }

    function addAutomationRule() {
        automationCount++;
        const container = document.getElementById('automation-container');
        const ruleDiv = document.createElement('div');
        ruleDiv.className = 'border border-gray-200 rounded-lg p-4';
        ruleDiv.id = `automation-${automationCount}`;
        
        ruleDiv.innerHTML = `
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-md font-medium text-gray-900">Automation Rule ${automationCount}</h4>
                <button type="button" onclick="removeAutomationRule(${automationCount})" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Trigger</label>
                    <select name="automation_rules[${automationCount}][trigger]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                        <option value="">Select Trigger</option>
                        <option value="stage_completed">Stage Completed</option>
                        <option value="stage_started">Stage Started</option>
                        <option value="deadline_approaching">Deadline Approaching</option>
                        <option value="task_overdue">Task Overdue</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Action</label>
                    <select name="automation_rules[${automationCount}][action]" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" required>
                        <option value="">Select Action</option>
                        <option value="send_notification">Send Notification</option>
                        <option value="create_task">Create Task</option>
                        <option value="assign_user">Assign User</option>
                        <option value="update_status">Update Status</option>
                    </select>
                </div>
            </div>
            
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700">Configuration (JSON)</label>
                <textarea name="automation_rules[${automationCount}][config]" rows="3" placeholder='{"message": "Stage completed", "recipients": ["manager"]}' class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"></textarea>
            </div>
        `;
        
        container.appendChild(ruleDiv);
    }

    function removeAutomationRule(ruleId) {
        const ruleDiv = document.getElementById(`automation-${ruleId}`);
        if (ruleDiv) {
            ruleDiv.remove();
        }
    }

    function loadTemplate(template) {
        const templates = {
            'music_production': {
                stages: [
                    { name: 'Pre-Production', description: 'Planning and preparation', duration: 2 },
                    { name: 'Recording', description: 'Audio recording sessions', duration: 5 },
                    { name: 'Editing', description: 'Audio editing and cleanup', duration: 3 },
                    { name: 'Mixing', description: 'Audio mixing and balancing', duration: 4 },
                    { name: 'Mastering', description: 'Final mastering and polish', duration: 2 },
                    { name: 'Delivery', description: 'Final delivery to client', duration: 1 }
                ]
            },
            'video_production': {
                stages: [
                    { name: 'Pre-Production', description: 'Planning and scripting', duration: 3 },
                    { name: 'Filming', description: 'Video recording sessions', duration: 5 },
                    { name: 'Editing', description: 'Video editing and cuts', duration: 7 },
                    { name: 'Audio Post', description: 'Audio editing and mixing', duration: 3 },
                    { name: 'Color Grading', description: 'Color correction and grading', duration: 2 },
                    { name: 'Final Export', description: 'Rendering and delivery', duration: 1 }
                ]
            },
            'podcast': {
                stages: [
                    { name: 'Planning', description: 'Episode planning and research', duration: 1 },
                    { name: 'Recording', description: 'Podcast recording session', duration: 1 },
                    { name: 'Editing', description: 'Audio editing and cleanup', duration: 2 },
                    { name: 'Post-Production', description: 'Adding intro/outro, music', duration: 1 },
                    { name: 'Publishing', description: 'Upload and distribution', duration: 1 }
                ]
            },
            'mixing_mastering': {
                stages: [
                    { name: 'Analysis', description: 'Track analysis and planning', duration: 1 },
                    { name: 'Mixing', description: 'Audio mixing process', duration: 3 },
                    { name: 'Client Review', description: 'Client feedback and revisions', duration: 2 },
                    { name: 'Mastering', description: 'Final mastering process', duration: 2 },
                    { name: 'Delivery', description: 'Final delivery to client', duration: 1 }
                ]
            }
        };

        if (templates[template]) {
            // Clear existing stages
            document.getElementById('stages-container').innerHTML = '';
            stageCount = 0;
            
            // Add template stages
            templates[template].stages.forEach(stage => {
                addStage();
                const currentStage = document.getElementById(`stage-${stageCount}`);
                currentStage.querySelector('input[name*="[name]"]').value = stage.name;
                currentStage.querySelector('textarea[name*="[description]"]').value = stage.description;
                currentStage.querySelector('input[name*="[estimated_duration]"]').value = stage.duration;
            });
        }
    }
</script>
@endpush
