@extends('layouts.app')

@section('title', 'Edit Studio Room')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Edit Studio Room: {{ $room->name }}</h1>
                <div class="btn-group">
                    <a href="{{ route('studio-rooms.show', $room) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Room
                    </a>
                    <a href="{{ route('studio-rooms.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> All Rooms
                    </a>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-body">
                    <form action="{{ route('studio-rooms.update', $room) }}" method="POST" id="roomForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Basic Information</h5>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">Room Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $room->name) }}" required>
                                    @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="type" class="form-label">Room Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('type') is-invalid @enderror" id="type" name="type" required>
                                        <option value="">Select Room Type</option>
                                        <option value="recording" {{ old('type', $room->type) == 'recording' ? 'selected' : '' }}>Recording Studio</option>
                                        <option value="mixing" {{ old('type', $room->type) == 'mixing' ? 'selected' : '' }}>Mixing Room</option>
                                        <option value="mastering" {{ old('type', $room->type) == 'mastering' ? 'selected' : '' }}>Mastering Suite</option>
                                        <option value="podcast" {{ old('type', $room->type) == 'podcast' ? 'selected' : '' }}>Podcast Studio</option>
                                        <option value="video" {{ old('type', $room->type) == 'video' ? 'selected' : '' }}>Video Production</option>
                                        <option value="rehearsal" {{ old('type', $room->type) == 'rehearsal' ? 'selected' : '' }}>Rehearsal Room</option>
                                    </select>
                                    @error('type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="4" 
                                              placeholder="Describe the room features, acoustics, and ideal use cases">{{ old('description', $room->description) }}</textarea>
                                    @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="capacity" class="form-label">Capacity <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control @error('capacity') is-invalid @enderror" 
                                                   id="capacity" name="capacity" value="{{ old('capacity', $room->capacity) }}" min="1" required>
                                            <div class="form-text">Maximum number of people</div>
                                            @error('capacity')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="hourly_rate" class="form-label">Hourly Rate ($) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control @error('hourly_rate') is-invalid @enderror" 
                                                   id="hourly_rate" name="hourly_rate" value="{{ old('hourly_rate', $room->hourly_rate) }}" 
                                                   min="0" step="0.01" required>
                                            @error('hourly_rate')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_available" 
                                               name="is_available" value="1" {{ old('is_available', $room->is_available) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_available">
                                            Room is Available for Booking
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requires_engineer" 
                                               name="requires_engineer" value="1" {{ old('requires_engineer', $room->requires_engineer) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="requires_engineer">
                                            Requires Engineer
                                        </label>
                                        <div class="form-text">Check if this room requires a sound engineer</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Equipment & Features -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Equipment & Features</h5>
                                
                                <div class="mb-3">
                                    <label for="equipment" class="form-label">Equipment</label>
                                    <div id="equipment-container">
                                        @if(old('equipment', $room->equipment))
                                            @foreach(old('equipment', $room->equipment) as $equipment)
                                            <div class="input-group mb-2">
                                                <input type="text" class="form-control" name="equipment[]" value="{{ $equipment }}">
                                                <button type="button" class="btn btn-outline-danger" onclick="removeEquipment(this)">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                            </div>
                                            @endforeach
                                        @else
                                            <div class="input-group mb-2">
                                                <input type="text" class="form-control" name="equipment[]" placeholder="Enter equipment item">
                                                <button type="button" class="btn btn-outline-danger" onclick="removeEquipment(this)">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addEquipment()">
                                        <i class="fas fa-plus"></i> Add Equipment
                                    </button>
                                </div>

                                <div class="mb-3">
                                    <label for="features" class="form-label">Features</label>
                                    <div id="features-container">
                                        @if(old('features', $room->features))
                                            @foreach(old('features', $room->features) as $feature)
                                            <div class="input-group mb-2">
                                                <input type="text" class="form-control" name="features[]" value="{{ $feature }}">
                                                <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                            </div>
                                            @endforeach
                                        @else
                                            <div class="input-group mb-2">
                                                <input type="text" class="form-control" name="features[]" placeholder="Enter feature">
                                                <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
                                                    <i class="fas fa-minus"></i>
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addFeature()">
                                        <i class="fas fa-plus"></i> Add Feature
                                    </button>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Availability Schedule</label>
                                    <div class="form-text mb-2">Set default operating hours for this room</div>
                                    
                                    @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
                                    @php
                                        $schedule = old("availability_schedule.{$day}", $room->availability_schedule[$day] ?? []);
                                        $enabled = $schedule['enabled'] ?? false;
                                        $start = $schedule['start'] ?? '09:00';
                                        $end = $schedule['end'] ?? '18:00';
                                    @endphp
                                    <div class="row mb-2">
                                        <div class="col-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="{{ $day }}_enabled" name="availability_schedule[{{ $day }}][enabled]" 
                                                       value="1" {{ $enabled ? 'checked' : '' }}>
                                                <label class="form-check-label" for="{{ $day }}_enabled">
                                                    {{ ucfirst($day) }}
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <input type="time" class="form-control form-control-sm" 
                                                   name="availability_schedule[{{ $day }}][start]" value="{{ $start }}">
                                        </div>
                                        <div class="col-1 text-center">to</div>
                                        <div class="col-4">
                                            <input type="time" class="form-control form-control-sm" 
                                                   name="availability_schedule[{{ $day }}][end]" value="{{ $end }}">
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('studio-rooms.show', $room) }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Update Room</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function addEquipment() {
    const container = document.getElementById('equipment-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="equipment[]" placeholder="Enter equipment item">
        <button type="button" class="btn btn-outline-danger" onclick="removeEquipment(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeEquipment(button) {
    const container = document.getElementById('equipment-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function addFeature() {
    const container = document.getElementById('features-container');
    const div = document.createElement('div');
    div.className = 'input-group mb-2';
    div.innerHTML = `
        <input type="text" class="form-control" name="features[]" placeholder="Enter feature">
        <button type="button" class="btn btn-outline-danger" onclick="removeFeature(this)">
            <i class="fas fa-minus"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeFeature(button) {
    const container = document.getElementById('features-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}
</script>
@endpush
