<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ClientController extends Controller
{
    public function index(Request $request)
    {
        $query = Client::orderBy('created_at', 'desc');

        // apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company', 'like', "%{$search}%");
            });
        }

        if ($request->has('high_value')) {
            $query->highValue();
        }

        if ($request->has('recently_active')) {
            $query->recentlyActive();
        }

        $clients = $query->paginate($request->get('per_page', 15));

        // Calculate summary statistics
        $summary = [
            'total' => Client::count(),
            'active' => Client::where('status', 'active')->count(),
            'vip' => Client::where('status', 'vip')->count(),
            'total_spent' => Client::sum('total_spent')
        ];

        // Return JSON for AJAX requests
        if ($request->expectsJson() || $request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => [
                    'clients' => $clients->items(),
                    'pagination' => [
                        'current_page' => $clients->currentPage(),
                        'last_page' => $clients->lastPage(),
                        'per_page' => $clients->perPage(),
                        'total' => $clients->total()
                    ],
                    'summary' => $summary
                ]
            ]);
        }

        // Return view for web requests
        return view('clients.index', compact('clients', 'summary'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:clients,email',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'type' => 'required|in:individual,artist,label,brand,agency',
            'bio' => 'nullable|string',
            'social_links' => 'nullable|array',
            'preferences' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['status'] = 'active';

        $client = Client::create($data);

        // track client creation
        AnalyticsEvent::track('client_created', 'client', $client);

        return response()->json([
            'success' => true,
            'message' => 'Client created successfully',
            'data' => ['client' => $client]
        ], 201);
    }

    public function show(Request $request, Client $client)
    {
        $client->load(['projects', 'bookings']);

        return response()->json([
            'success' => true,
            'data' => ['client' => $client]
        ]);
    }

    public function update(Request $request, Client $client)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:clients,email,' . $client->id,
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'type' => 'sometimes|in:individual,artist,label,brand,agency',
            'bio' => 'nullable|string',
            'status' => 'sometimes|in:active,inactive,blacklisted',
            'social_links' => 'nullable|array',
            'preferences' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $client->update($validator->validated());

        return response()->json([
            'success' => true,
            'message' => 'Client updated successfully',
            'data' => ['client' => $client->fresh()]
        ]);
    }

    public function destroy(Request $request, Client $client)
    {
        $user = $request->user();

        if (!$user->hasPermission('*')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // check if client has active projects
        if ($client->projects()->whereIn('status', ['active', 'on_hold'])->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete client with active projects'
            ], 422);
        }

        $client->delete();

        return response()->json([
            'success' => true,
            'message' => 'Client deleted successfully'
        ]);
    }

    public function stats(Request $request, Client $client)
    {
        $stats = [
            'total_projects' => $client->projects()->count(),
            'active_projects' => $client->projects()->where('status', 'active')->count(),
            'completed_projects' => $client->projects()->where('status', 'completed')->count(),
            'total_bookings' => $client->bookings()->count(),
            'upcoming_bookings' => $client->bookings()->upcoming()->count(),
            'total_spent' => $client->total_spent,
            'average_project_value' => $client->getAverageProjectValue(),
            'client_tier' => $client->getClientTier(),
            'rating' => $client->rating,
            'last_project_date' => $client->last_project_at
        ];

        return response()->json([
            'success' => true,
            'data' => ['stats' => $stats]
        ]);
    }

    public function projects(Request $request, Client $client)
    {
        $projects = $client->projects()
            ->with(['projectManager', 'tasks'])
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json([
            'success' => true,
            'data' => [
                'projects' => $projects->items(),
                'pagination' => [
                    'current_page' => $projects->currentPage(),
                    'last_page' => $projects->lastPage(),
                    'per_page' => $projects->perPage(),
                    'total' => $projects->total()
                ]
            ]
        ]);
    }

    public function bookings(Request $request, Client $client)
    {
        $bookings = $client->bookings()
            ->with(['studioRoom', 'engineer'])
            ->orderBy('start_time', 'desc')
            ->paginate($request->get('per_page', 10));

        return response()->json([
            'success' => true,
            'data' => [
                'bookings' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total()
                ]
            ]
        ]);
    }

    public function updateStats(Request $request, Client $client)
    {
        $client->updateProjectStats();

        return response()->json([
            'success' => true,
            'message' => 'Client stats updated successfully',
            'data' => ['client' => $client->fresh()]
        ]);
    }

    public function search(Request $request)
    {
        $query = Client::query();

        if ($request->filled('q')) {
            $search = $request->q;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('company', 'like', "%{$search}%");
            });
        }

        $clients = $query->where('status', 'active')
                        ->orderBy('name')
                        ->limit(30)
                        ->get(['id', 'name', 'email', 'company']);

        return response()->json([
            'success' => true,
            'data' => $clients,
            'total' => $clients->count()
        ]);
    }
}
