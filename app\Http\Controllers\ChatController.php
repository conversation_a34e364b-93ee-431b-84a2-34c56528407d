<?php

namespace App\Http\Controllers;

use App\Models\ChatChannel;
use App\Models\ChatMessage;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ChatController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return $this->channels($request);
        }

        // Get channels for the view
        $query = ChatChannel::with(['department', 'project', 'creator'])
            ->active()
            ->orderBy('last_message_at', 'desc');

        // filter channels user can access
        $query->where(function ($q) use ($user) {
            $q->where('is_private', false)
              ->orWhereHas('members', function ($memberQuery) use ($user) {
                  $memberQuery->where('user_id', $user->id);
              });
        });

        $channels = $query->get()->map(function ($channel) use ($user) {
            return [
                'id' => $channel->id,
                'name' => $channel->name,
                'slug' => $channel->slug,
                'description' => $channel->description,
                'type' => $channel->type,
                'is_private' => $channel->is_private,
                'department' => $channel->department ? [
                    'id' => $channel->department->id,
                    'name' => $channel->department->name
                ] : null,
                'project' => $channel->project ? [
                    'id' => $channel->project->id,
                    'title' => $channel->project->title
                ] : null,
                'last_message_at' => $channel->last_message_at,
                'unread_count' => $channel->getUnreadCount($user),
                'member_count' => $channel->members()->count()
            ];
        });

        // Return chat interface view
        return view('chat.index', compact('channels'));
    }

    public function channels(Request $request)
    {
        $user = $request->user();
        
        $query = ChatChannel::with(['department', 'project', 'creator'])
            ->active()
            ->orderBy('last_message_at', 'desc');

        // filter channels user can access
        $query->where(function ($q) use ($user) {
            $q->where('is_private', false)
              ->orWhereHas('members', function ($memberQuery) use ($user) {
                  $memberQuery->where('user_id', $user->id);
              });
        });

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('department_id')) {
            $query->where('department_id', $request->department_id);
        }

        $channels = $query->get()->map(function ($channel) use ($user) {
            return [
                'id' => $channel->id,
                'name' => $channel->name,
                'slug' => $channel->slug,
                'description' => $channel->description,
                'type' => $channel->type,
                'is_private' => $channel->is_private,
                'department' => $channel->department ? [
                    'id' => $channel->department->id,
                    'name' => $channel->department->name
                ] : null,
                'project' => $channel->project ? [
                    'id' => $channel->project->id,
                    'title' => $channel->project->title
                ] : null,
                'last_message_at' => $channel->last_message_at,
                'unread_count' => $channel->getUnreadCount($user),
                'member_count' => $channel->members()->count()
            ];
        });

        return response()->json([
            'success' => true,
            'data' => ['channels' => $channels]
        ]);
    }

    public function createChannel(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:department,project,direct,general',
            'department_id' => 'nullable|exists:departments,id',
            'project_id' => 'nullable|exists:projects,id',
            'is_private' => 'boolean',
            'member_ids' => 'nullable|array',
            'member_ids.*' => 'exists:users,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['slug'] = Str::slug($data['name']) . '-' . time();
        $data['created_by'] = $request->user()->id;

        $channel = ChatChannel::create($data);

        // add creator as admin
        $channel->addMember($request->user(), 'admin');

        // add specified members
        if (isset($data['member_ids'])) {
            foreach ($data['member_ids'] as $memberId) {
                $channel->addMember(\App\Models\User::find($memberId));
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Channel created successfully',
            'data' => ['channel' => $channel->load(['department', 'project', 'members'])]
        ], 201);
    }

    public function messages(Request $request, ChatChannel $channel)
    {
        $user = $request->user();

        if (!$channel->canUserAccess($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $query = $channel->messages()
            ->with(['user', 'replyTo.user'])
            ->orderBy('created_at', 'desc');

        if ($request->has('before')) {
            $query->where('id', '<', $request->before);
        }

        $messages = $query->limit($request->get('limit', 50))->get()->reverse()->values();

        // mark channel as read
        $channel->markAsRead($user);

        return response()->json([
            'success' => true,
            'data' => ['messages' => $messages]
        ]);
    }

    public function sendMessage(Request $request, ChatChannel $channel)
    {
        $user = $request->user();

        if (!$channel->canUserAccess($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:2000',
            'type' => 'in:text,file,image,audio,system',
            'attachments' => 'nullable|array',
            'reply_to' => 'nullable|exists:chat_messages,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['channel_id'] = $channel->id;
        $data['user_id'] = $user->id;
        $data['type'] = $data['type'] ?? 'text';

        $message = ChatMessage::create($data);

        // load relationships for response
        $message->load(['user', 'replyTo.user']);

        // broadcast message to channel members (would use WebSockets in production)
        // event(new MessageSent($message));

        return response()->json([
            'success' => true,
            'message' => 'Message sent successfully',
            'data' => ['message' => $message]
        ], 201);
    }

    public function editMessage(Request $request, ChatMessage $message)
    {
        $user = $request->user();

        if (!$message->canBeEditedBy($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot edit this message'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:2000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $message->edit($request->message);

        return response()->json([
            'success' => true,
            'message' => 'Message updated successfully',
            'data' => ['message' => $message->fresh(['user'])]
        ]);
    }

    public function deleteMessage(Request $request, ChatMessage $message)
    {
        $user = $request->user();

        if (!$message->canBeDeletedBy($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete this message'
            ], 403);
        }

        $message->delete();

        return response()->json([
            'success' => true,
            'message' => 'Message deleted successfully'
        ]);
    }

    public function addReaction(Request $request, ChatMessage $message)
    {
        $validator = Validator::make($request->all(), [
            'emoji' => 'required|string|max:10'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $message->addReaction($request->user(), $request->emoji);

        return response()->json([
            'success' => true,
            'message' => 'Reaction added successfully',
            'data' => ['message' => $message->fresh()]
        ]);
    }

    public function removeReaction(Request $request, ChatMessage $message)
    {
        $validator = Validator::make($request->all(), [
            'emoji' => 'required|string|max:10'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $message->removeReaction($request->user(), $request->emoji);

        return response()->json([
            'success' => true,
            'message' => 'Reaction removed successfully',
            'data' => ['message' => $message->fresh()]
        ]);
    }

    public function joinChannel(Request $request, ChatChannel $channel)
    {
        $user = $request->user();

        if ($channel->is_private) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot join private channel'
            ], 403);
        }

        if ($channel->isMember($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Already a member of this channel'
            ], 422);
        }

        $channel->addMember($user);

        return response()->json([
            'success' => true,
            'message' => 'Joined channel successfully'
        ]);
    }

    public function leaveChannel(Request $request, ChatChannel $channel)
    {
        $user = $request->user();

        if (!$channel->isMember($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Not a member of this channel'
            ], 422);
        }

        $channel->removeMember($user);

        return response()->json([
            'success' => true,
            'message' => 'Left channel successfully'
        ]);
    }

    public function channelMembers(Request $request, ChatChannel $channel)
    {
        $user = $request->user();

        if (!$channel->canUserAccess($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $members = $channel->members()
            ->with(['department', 'role'])
            ->get()
            ->map(function ($member) use ($channel) {
                return [
                    'id' => $member->id,
                    'first_name' => $member->first_name,
                    'last_name' => $member->last_name,
                    'full_name' => $member->full_name,
                    'avatar' => $member->avatar,
                    'department' => $member->department,
                    'role' => $member->role,
                    'channel_role' => $member->pivot->role,
                    'joined_at' => $member->pivot->joined_at,
                    'last_read_at' => $member->pivot->last_read_at,
                    'is_online' => $member->last_active_at && $member->last_active_at->diffInMinutes(now()) < 5
                ];
            });

        return response()->json([
            'success' => true,
            'data' => ['members' => $members]
        ]);
    }
}
