<?php $__env->startSection('title', 'Profit & Loss Report'); ?>

<?php $__env->startSection('dashboard-content'); ?>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Profit & Loss Report</h1>
            <p class="text-gray-600"><?php echo e($startDate->format('M d, Y')); ?> - <?php echo e($endDate->format('M d, Y')); ?></p>
        </div>
        <div class="flex space-x-3">
            <a href="<?php echo e(route('accounting.reports.index')); ?>" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Back to Reports
            </a>
            <button onclick="openExportModal('profit-loss')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                <i class="fas fa-download mr-2"></i>Export
            </button>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Revenue</h2>
        </div>
        <div class="px-6 py-4">
            <div class="space-y-3">
                <?php $__currentLoopData = $report['revenue']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($key !== 'total'): ?>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700"><?php echo e(ucwords(str_replace('_', ' ', $key))); ?></span>
                        <span class="font-medium text-green-600">$<?php echo e(number_format($value, 2)); ?></span>
                    </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <div class="border-t pt-3 mt-3">
                    <div class="flex justify-between items-center font-semibold">
                        <span class="text-gray-900">Total Revenue</span>
                        <span class="text-green-600 text-lg">$<?php echo e(number_format($report['revenue']['total'], 2)); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden mt-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900">Expenses</h2>
        </div>
        <div class="px-6 py-4">
            <div class="space-y-3">
                <?php $__currentLoopData = $report['expenses']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if($key !== 'total'): ?>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-700"><?php echo e(ucwords(str_replace('_', ' ', $key))); ?></span>
                        <span class="font-medium text-red-600">$<?php echo e(number_format($value, 2)); ?></span>
                    </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <div class="border-t pt-3 mt-3">
                    <div class="flex justify-between items-center font-semibold">
                        <span class="text-gray-900">Total Expenses</span>
                        <span class="text-red-600 text-lg">$<?php echo e(number_format($report['expenses']['total'], 2)); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden mt-6">
        <div class="px-6 py-4">
            <div class="flex justify-between items-center">
                <h2 class="text-2xl font-bold text-gray-900">Net Profit</h2>
                <span class="text-2xl font-bold <?php echo e($report['net_profit'] >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                    $<?php echo e(number_format($report['net_profit'], 2)); ?>

                </span>
            </div>
            <div class="mt-2">
                <div class="text-sm text-gray-600">
                    Profit Margin: <?php echo e($report['revenue']['total'] > 0 ? number_format(($report['net_profit'] / $report['revenue']['total']) * 100, 1) : 0); ?>%
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Export Report</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeExportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select Export Format</label>
                <div class="space-y-2">
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="pdf" class="mr-2" checked>
                        <i class="fas fa-file-pdf text-red-500 mr-2"></i>
                        PDF Document
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="export_format" value="csv" class="mr-2">
                        <i class="fas fa-file-csv text-green-500 mr-2"></i>
                        CSV Spreadsheet
                    </label>
                </div>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors" onclick="closeExportModal()">Cancel</button>
                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" onclick="exportReport()">
                    <i class="fas fa-download mr-2"></i>Export
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let currentReportType = 'profit-loss';

function openExportModal(reportType) {
    currentReportType = reportType;
    document.getElementById('exportModal').classList.remove('hidden');
}

function closeExportModal() {
    document.getElementById('exportModal').classList.add('hidden');
}

function exportReport() {
    const format = document.querySelector('input[name="export_format"]:checked').value;
    const startDate = '<?php echo e($startDate->format("Y-m-d")); ?>';
    const endDate = '<?php echo e($endDate->format("Y-m-d")); ?>';
    
    // Show loading state
    const exportBtn = document.querySelector('#exportModal button[onclick="exportReport()"]');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Exporting...';
    exportBtn.disabled = true;
    
    // Create download URL
    const params = new URLSearchParams({
        report_type: currentReportType,
        format: format,
        start_date: startDate,
        end_date: endDate
    });
    
    // Create a temporary link to trigger download
    const downloadUrl = `/accounting/reports/export?${params.toString()}`;
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${currentReportType}-report-${startDate}-to-${endDate}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Reset button state and close modal
    setTimeout(() => {
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
        closeExportModal();
    }, 1000);
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\workspace\.php\glitchafrica\resources\views/accounting/reports/profit-loss.blade.php ENDPATH**/ ?>