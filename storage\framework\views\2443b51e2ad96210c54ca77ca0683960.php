<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="h-full bg-gray-50">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'Dashboard'); ?> - <?php echo e(config('app.name', 'Glitch Africa')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#64748b',
                        accent: '#f59e0b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        error: '#ef4444',
                        info: '#3b82f6'
                    }
                }
            }
        }
    </script>
    
    <!-- Additional Styles -->
    <?php echo $__env->yieldPushContent('styles'); ?>
    
    <style>
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        

        
        /* Fade in animation */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Hover effects */
        .hover-scale {
            transition: transform 0.2s ease-in-out;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
        
        /* Mobile menu animation */
        .mobile-menu-enter {
            transform: translateX(-100%);
        }
        .mobile-menu-enter-active {
            transform: translateX(0);
            transition: transform 0.3s ease-out;
        }
        .mobile-menu-exit {
            transform: translateX(0);
        }
        .mobile-menu-exit-active {
            transform: translateX(-100%);
            transition: transform 0.3s ease-in;
        }
    </style>
</head>
<body class="h-full font-sans antialiased">


    <div id="app" class="h-screen bg-gray-50">


        <!-- Global Alert Modal -->
        <div id="alertModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
                <div class="flex items-center mb-4">
                    <div id="alertIcon" class="flex-shrink-0 mr-3">
                        <!-- Icon will be inserted here -->
                    </div>
                    <h3 id="alertTitle" class="text-lg font-medium text-gray-900">Alert</h3>
                </div>
                <div id="alertMessage" class="text-sm text-gray-600 mb-6">
                    <!-- Message will be inserted here -->
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="alertCancel" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg hidden">Cancel</button>
                    <button id="alertConfirm" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">OK</button>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        <?php if(session('success')): ?>
            <div id="flash-message" class="fixed top-4 right-4 z-40 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg fade-in">
                <div class="flex items-center justify-between">
                    <span><?php echo e(session('success')); ?></span>
                    <button onclick="closeFlashMessage()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div id="flash-message" class="fixed top-4 right-4 z-40 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg fade-in">
                <div class="flex items-center justify-between">
                    <span><?php echo e(session('error')); ?></span>
                    <button onclick="closeFlashMessage()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if(session('warning')): ?>
            <div id="flash-message" class="fixed top-4 right-4 z-40 bg-yellow-500 text-white px-6 py-3 rounded-lg shadow-lg fade-in">
                <div class="flex items-center justify-between">
                    <span><?php echo e(session('warning')); ?></span>
                    <button onclick="closeFlashMessage()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <!-- Main Content -->
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <!-- Alpine.js for interactivity -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>


    
    <!-- Global JavaScript -->
    <script>
        // CSRF Token for AJAX requests
        window.Laravel = {
            csrfToken: '<?php echo e(csrf_token()); ?>'
        };
        
        // Set CSRF token for all AJAX requests
        if (window.axios) {
            window.axios.defaults.headers.common['X-CSRF-TOKEN'] = window.Laravel.csrfToken;
        }
        
        // Global functions
        function showLoading() {
            // Removed global loading overlay
        }

        function hideLoading() {
            // Removed global loading overlay
        }
        
        function closeFlashMessage() {
            const flashMessage = document.getElementById('flash-message');
            if (flashMessage) {
                flashMessage.style.opacity = '0';
                flashMessage.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    flashMessage.remove();
                }, 300);
            }
        }
        
        // Auto-close flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessage = document.getElementById('flash-message');
            if (flashMessage) {
                setTimeout(() => {
                    closeFlashMessage();
                }, 5000);
            }
        });
        
        // Global AJAX error handler
        document.addEventListener('DOMContentLoaded', function() {
            // Removed automatic loading states that interfere with AJAX
        });
        
        // Utility functions
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-NG', {
                style: 'currency',
                currency: 'NGN'
            }).format(amount);
        }
        
        function formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // API helper functions
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': window.Laravel.csrfToken,
                    'Accept': 'application/json'
                }
            };
            
            const mergedOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            try {
                const response = await fetch(url, mergedOptions);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API request failed:', error);
                throw error;
            }
        }
        
        // Notification system
        function showNotification(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                warning: 'bg-yellow-500',
                info: 'bg-blue-500'
            };
            
            notification.className = `fixed top-4 right-4 z-40 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg fade-in`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, duration);
        }

        // Global Alert Modal Functions
        function showAlert(message, title = 'Alert', type = 'info') {
            const modal = document.getElementById('alertModal');
            const titleEl = document.getElementById('alertTitle');
            const messageEl = document.getElementById('alertMessage');
            const iconEl = document.getElementById('alertIcon');
            const confirmBtn = document.getElementById('alertConfirm');
            const cancelBtn = document.getElementById('alertCancel');

            titleEl.textContent = title;
            messageEl.textContent = message;

            // Set icon based on type
            const icons = {
                success: '<i class="fas fa-check-circle text-green-500 text-2xl"></i>',
                error: '<i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>',
                warning: '<i class="fas fa-exclamation-triangle text-yellow-500 text-2xl"></i>',
                info: '<i class="fas fa-info-circle text-blue-500 text-2xl"></i>'
            };
            iconEl.innerHTML = icons[type] || icons.info;

            // Hide cancel button for simple alerts
            cancelBtn.classList.add('hidden');

            modal.classList.remove('hidden');
            confirmBtn.focus();

            return new Promise((resolve) => {
                confirmBtn.onclick = () => {
                    modal.classList.add('hidden');
                    resolve(true);
                };
            });
        }

        function showConfirm(message, title = 'Confirm', type = 'warning') {
            const modal = document.getElementById('alertModal');
            const titleEl = document.getElementById('alertTitle');
            const messageEl = document.getElementById('alertMessage');
            const iconEl = document.getElementById('alertIcon');
            const confirmBtn = document.getElementById('alertConfirm');
            const cancelBtn = document.getElementById('alertCancel');

            titleEl.textContent = title;
            messageEl.textContent = message;

            // Set icon based on type
            const icons = {
                success: '<i class="fas fa-check-circle text-green-500 text-2xl"></i>',
                error: '<i class="fas fa-exclamation-triangle text-red-500 text-2xl"></i>',
                warning: '<i class="fas fa-exclamation-triangle text-yellow-500 text-2xl"></i>',
                info: '<i class="fas fa-info-circle text-blue-500 text-2xl"></i>'
            };
            iconEl.innerHTML = icons[type] || icons.warning;

            // Show cancel button for confirmations
            cancelBtn.classList.remove('hidden');
            confirmBtn.textContent = 'Confirm';

            modal.classList.remove('hidden');
            cancelBtn.focus();

            return new Promise((resolve) => {
                confirmBtn.onclick = () => {
                    modal.classList.add('hidden');
                    resolve(true);
                };
                cancelBtn.onclick = () => {
                    modal.classList.add('hidden');
                    resolve(false);
                };
            });
        }

        // Override native alert and confirm
        window.alert = showAlert;
        window.confirm = showConfirm;
    </script>
    
    <!-- Additional Scripts -->
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH D:\workspace\.php\glitchafrica\resources\views/layouts/app.blade.php ENDPATH**/ ?>