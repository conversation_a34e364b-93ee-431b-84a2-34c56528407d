<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->foreignId('department_id')->nullable()->constrained()->onDelete('set null');
            $table->json('permissions')->nullable(); // role permissions
            $table->integer('level')->default(1); // hierarchy level
            $table->boolean('is_system_role')->default(false); // system vs custom roles
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['is_active', 'department_id']);
            $table->index(['level', 'is_system_role']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('roles');
    }
};
