@extends('layouts.dashboard')

@section('title', 'Workflow Management')

@push('styles')
<style>
.workflow-canvas {
    background-image: radial-gradient(circle, #e5e7eb 1px, transparent 1px);
    background-size: 20px 20px;
    background-color: #f9fafb;
}

.workflow-node {
    cursor: move;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

.workflow-node:hover {
    transform: scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.workflow-node.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.connection-line {
    stroke: #6b7280;
    stroke-width: 2;
    fill: none;
    marker-end: url(#arrowhead);
}

.connection-line.active {
    stroke: #3b82f6;
    stroke-width: 3;
}

.node-connector {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #6b7280;
    border: 2px solid white;
    cursor: crosshair;
    transition: all 0.2s ease;
}

.node-connector:hover {
    background: #3b82f6;
    transform: scale(1.2);
}

.node-connector.input {
    position: absolute;
    left: -6px;
    top: 50%;
    transform: translateY(-50%);
}

.node-connector.output {
    position: absolute;
    right: -6px;
    top: 50%;
    transform: translateY(-50%);
}
</style>
@endpush

@section('dashboard-content')
<!-- Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-3xl font-bold text-gray-900">Workflow Management</h1>
        <p class="mt-1 text-sm text-gray-600">Design and manage visual workflows</p>
    </div>
    <div class="flex space-x-3">
        <button onclick="openCreateWorkflowModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-plus"></i>
            <span>Create Workflow</span>
        </button>
        <button onclick="saveWorkflow()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-save"></i>
            <span>Save</span>
        </button>
    </div>
</div>
<!-- Workflow Builder Interface -->
<div class="flex h-screen bg-white rounded-lg shadow overflow-hidden">
    <!-- Node Palette -->
    <div class="w-64 bg-gray-50 border-r border-gray-200 p-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Node Library</h3>

        <!-- Node Categories -->
        <div class="space-y-4">
            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Triggers</h4>
                <div class="space-y-2">
                    <div class="node-template bg-blue-100 border border-blue-200 rounded-lg p-3 cursor-pointer hover:bg-blue-200"
                         data-node-type="trigger" data-node-subtype="project_start">
                        <div class="flex items-center">
                            <i class="fas fa-play text-blue-600 mr-2"></i>
                            <span class="text-sm font-medium text-blue-800">Project Start</span>
                        </div>
                    </div>
                    <div class="node-template bg-blue-100 border border-blue-200 rounded-lg p-3 cursor-pointer hover:bg-blue-200"
                         data-node-type="trigger" data-node-subtype="task_complete">
                        <div class="flex items-center">
                            <i class="fas fa-check text-blue-600 mr-2"></i>
                            <span class="text-sm font-medium text-blue-800">Task Complete</span>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Actions</h4>
                <div class="space-y-2">
                    <div class="node-template bg-green-100 border border-green-200 rounded-lg p-3 cursor-pointer hover:bg-green-200"
                         data-node-type="action" data-node-subtype="create_task">
                        <div class="flex items-center">
                            <i class="fas fa-plus text-green-600 mr-2"></i>
                            <span class="text-sm font-medium text-green-800">Create Task</span>
                        </div>
                    </div>
                    <div class="node-template bg-green-100 border border-green-200 rounded-lg p-3 cursor-pointer hover:bg-green-200"
                         data-node-type="action" data-node-subtype="send_notification">
                        <div class="flex items-center">
                            <i class="fas fa-bell text-green-600 mr-2"></i>
                            <span class="text-sm font-medium text-green-800">Send Notification</span>
                        </div>
                    </div>
                    <div class="node-template bg-green-100 border border-green-200 rounded-lg p-3 cursor-pointer hover:bg-green-200"
                         data-node-type="action" data-node-subtype="assign_user">
                        <div class="flex items-center">
                            <i class="fas fa-user text-green-600 mr-2"></i>
                            <span class="text-sm font-medium text-green-800">Assign User</span>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <h4 class="text-sm font-medium text-gray-700 mb-2">Conditions</h4>
                <div class="space-y-2">
                    <div class="node-template bg-yellow-100 border border-yellow-200 rounded-lg p-3 cursor-pointer hover:bg-yellow-200"
                         data-node-type="condition" data-node-subtype="if_then">
                        <div class="flex items-center">
                            <i class="fas fa-code-branch text-yellow-600 mr-2"></i>
                            <span class="text-sm font-medium text-yellow-800">If/Then</span>
                        </div>
                    </div>
                    <div class="node-template bg-yellow-100 border border-yellow-200 rounded-lg p-3 cursor-pointer hover:bg-yellow-200"
                         data-node-type="condition" data-node-subtype="delay">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-yellow-600 mr-2"></i>
                            <span class="text-sm font-medium text-yellow-800">Delay</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Canvas Area -->
    <div class="flex-1 flex flex-col">
        <!-- Canvas Toolbar -->
        <div class="bg-white border-b border-gray-200 p-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <select id="workflowSelect" class="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                        <option value="">Select Workflow</option>
                        @foreach($workflows ?? [] as $workflow)
                            <option value="{{ $workflow->id }}">{{ $workflow->name }}</option>
                        @endforeach
                    </select>
                    <div class="flex items-center space-x-2">
                        <button onclick="zoomIn()" class="p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <span id="zoomLevel" class="text-sm text-gray-600">100%</span>
                        <button onclick="zoomOut()" class="p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button onclick="resetZoom()" class="p-2 text-gray-600 hover:text-gray-900">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="clearCanvas()" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm">
                        <i class="fas fa-trash mr-1"></i>
                        Clear
                    </button>
                    <button onclick="exportWorkflow()" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm">
                        <i class="fas fa-download mr-1"></i>
                        Export
                    </button>
                </div>
            </div>
        </div>

        <!-- Canvas -->
        <div class="flex-1 workflow-canvas relative overflow-hidden" id="workflowCanvas">
            <svg id="connectionSvg" class="absolute inset-0 w-full h-full pointer-events-none" style="z-index: 1;">
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
                    </marker>
                </defs>
            </svg>
            <div id="nodesContainer" class="absolute inset-0" style="z-index: 2;">
                <!-- Workflow nodes will be dynamically added here -->
            </div>
        </div>
    </div>

    <!-- Properties Panel -->
    <div class="w-80 bg-gray-50 border-l border-gray-200 p-4" id="propertiesPanel">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Properties</h3>
        <div id="nodeProperties" class="space-y-4">
            <div class="text-center text-gray-500 py-8">
                <i class="fas fa-mouse-pointer text-3xl mb-2"></i>
                <p>Select a node to edit its properties</p>
            </div>
        </div>
    </div>
</div>

<!-- Workflow List -->
<div class="bg-white rounded-lg shadow">
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @forelse($workflows ?? [] as $workflow)
                <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">{{ $workflow->name }}</h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            {{ $workflow->is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                            {{ $workflow->is_active ? 'Active' : 'Inactive' }}
                        </span>
                    </div>

                    <p class="text-gray-600 text-sm mb-4">{{ $workflow->description ?? 'No description available' }}</p>

                    <div class="mb-4">
                        <div class="flex items-center text-sm text-gray-500 mb-2">
                            <i class="fas fa-bolt mr-2"></i>
                            <span>Trigger: {{ ucfirst(str_replace('_', ' ', $workflow->trigger_type)) }}</span>
                        </div>
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="fas fa-tasks mr-2"></i>
                            <span>{{ count($workflow->actions ?? []) }} actions</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <a href="{{ route('workflows.show', $workflow) }}" class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-md text-sm font-medium text-center">
                            View
                        </a>
                        <a href="{{ route('workflows.edit', $workflow) }}" class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-2 rounded-md text-sm font-medium text-center">
                            Edit
                        </a>
                    </div>
                </div>
            @empty
                <div class="col-span-full">
                    <div class="text-center py-12">
                        <i class="fas fa-project-diagram text-gray-400 text-4xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No workflows found</h3>
                        <p class="text-gray-500 mb-4">Get started by creating your first workflow template.</p>
                        <a href="{{ route('workflows.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Create Workflow
                        </a>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Quick Templates -->
        @if(($workflows ?? collect())->isEmpty())
            <div class="mt-12">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Quick Start Templates</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createFromTemplate('music_production')">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-music text-blue-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Music Production</h4>
                        <p class="text-sm text-gray-600 mt-1">Complete music production workflow</p>
                    </div>

                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createFromTemplate('video_production')">
                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-video text-red-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Video Production</h4>
                        <p class="text-sm text-gray-600 mt-1">Video creation and editing workflow</p>
                    </div>

                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createFromTemplate('podcast')">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-microphone text-green-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Podcast</h4>
                        <p class="text-sm text-gray-600 mt-1">Podcast recording and publishing</p>
                    </div>

                    <div class="bg-white rounded-lg shadow p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="createFromTemplate('mixing_mastering')">
                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
                            <i class="fas fa-sliders-h text-purple-600"></i>
                        </div>
                        <h4 class="font-medium text-gray-900">Mixing & Mastering</h4>
                        <p class="text-sm text-gray-600 mt-1">Audio mixing and mastering workflow</p>
                    </div>
                </div>
            </div>
        @endif
    </div>
    </div>
</div>

<!-- Create Workflow Modal -->
<div id="createWorkflowModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Create New Workflow</h3>
            <button onclick="closeCreateWorkflowModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <form id="createWorkflowForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Workflow Name *</label>
                    <input type="text" id="workflowName" name="name" required
                           class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Project Type *</label>
                    <select id="workflowType" name="project_type" required
                            class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Select Type</option>
                        <option value="music">Music Production</option>
                        <option value="video">Video Production</option>
                        <option value="podcast">Podcast</option>
                        <option value="mixing">Mixing & Mastering</option>
                    </select>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="workflowDescription" name="description" rows="3"
                          class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeCreateWorkflowModal()"
                        class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
                <button type="submit"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">Create Workflow</button>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Global variables for workflow builder
let currentWorkflow = null;
let selectedNode = null;
let isConnecting = false;
let connectionStart = null;
let zoomLevel = 1;
let panX = 0;
let panY = 0;
let nodeCounter = 0;
let nodes = [];
let connections = [];

$(document).ready(function() {
    initializeWorkflowBuilder();
});

function initializeWorkflowBuilder() {
    // Initialize drag and drop for node templates
    $('.node-template').draggable({
        helper: 'clone',
        revert: 'invalid',
        start: function(event, ui) {
            ui.helper.addClass('dragging');
        }
    });

    // Make canvas droppable
    $('#workflowCanvas').droppable({
        accept: '.node-template',
        drop: function(event, ui) {
            const nodeType = ui.helper.data('node-type');
            const nodeSubtype = ui.helper.data('node-subtype');
            const offset = $(this).offset();
            const x = event.pageX - offset.left;
            const y = event.pageY - offset.top;

            createNode(nodeType, nodeSubtype, x, y);
        }
    });

    // Canvas panning and zooming
    $('#workflowCanvas').on('mousedown', function(e) {
        if (e.target === this) {
            let startX = e.pageX;
            let startY = e.pageY;
            let startPanX = panX;
            let startPanY = panY;

            $(document).on('mousemove.pan', function(e) {
                panX = startPanX + (e.pageX - startX);
                panY = startPanY + (e.pageY - startY);
                updateCanvasTransform();
            });

            $(document).on('mouseup.pan', function() {
                $(document).off('.pan');
            });
        }
    });

    // Workflow form submission
    $('#createWorkflowForm').on('submit', function(e) {
        e.preventDefault();
        createNewWorkflow();
    });
}

function createNode(type, subtype, x, y) {
    const nodeId = 'node_' + (++nodeCounter);
    const nodeConfig = getNodeConfig(type, subtype);

    const nodeHtml = `
        <div class="workflow-node absolute bg-white rounded-lg shadow-lg border-2 border-gray-200 p-4 cursor-move"
             id="${nodeId}"
             data-type="${type}"
             data-subtype="${subtype}"
             style="left: ${x}px; top: ${y}px; width: 200px;">
            <div class="node-connector input" data-node="${nodeId}" data-type="input"></div>
            <div class="node-connector output" data-node="${nodeId}" data-type="output"></div>
            <div class="flex items-center mb-2">
                <i class="${nodeConfig.icon} ${nodeConfig.color} mr-2"></i>
                <span class="font-medium text-gray-900">${nodeConfig.title}</span>
            </div>
            <p class="text-sm text-gray-600">${nodeConfig.description}</p>
            <div class="mt-2 flex justify-end">
                <button onclick="editNode('${nodeId}')" class="text-blue-600 hover:text-blue-800 text-xs">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="deleteNode('${nodeId}')" class="text-red-600 hover:text-red-800 text-xs ml-2">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    $('#nodesContainer').append(nodeHtml);

    // Store node data
    nodes.push({
        id: nodeId,
        type: type,
        subtype: subtype,
        x: x,
        y: y,
        config: nodeConfig
    });

    // Make node draggable
    $(`#${nodeId}`).draggable({
        containment: '#workflowCanvas',
        drag: function(event, ui) {
            updateConnections();
        },
        stop: function(event, ui) {
            // Update node position in data
            const node = nodes.find(n => n.id === nodeId);
            if (node) {
                node.x = ui.position.left;
                node.y = ui.position.top;
            }
        }
    });

    // Add click handler for selection
    $(`#${nodeId}`).on('click', function(e) {
        e.stopPropagation();
        selectNode(nodeId);
    });

    // Add connector click handlers
    $(`#${nodeId} .node-connector`).on('click', function(e) {
        e.stopPropagation();
        handleConnectorClick($(this));
    });
}

function getNodeConfig(type, subtype) {
    const configs = {
        trigger: {
            project_start: {
                title: 'Project Start',
                description: 'Triggers when a project begins',
                icon: 'fas fa-play',
                color: 'text-blue-600'
            },
            task_complete: {
                title: 'Task Complete',
                description: 'Triggers when a task is completed',
                icon: 'fas fa-check',
                color: 'text-blue-600'
            }
        },
        action: {
            create_task: {
                title: 'Create Task',
                description: 'Creates a new task',
                icon: 'fas fa-plus',
                color: 'text-green-600'
            },
            send_notification: {
                title: 'Send Notification',
                description: 'Sends a notification',
                icon: 'fas fa-bell',
                color: 'text-green-600'
            },
            assign_user: {
                title: 'Assign User',
                description: 'Assigns a user to a task',
                icon: 'fas fa-user',
                color: 'text-green-600'
            }
        },
        condition: {
            if_then: {
                title: 'If/Then',
                description: 'Conditional logic branch',
                icon: 'fas fa-code-branch',
                color: 'text-yellow-600'
            },
            delay: {
                title: 'Delay',
                description: 'Waits for specified time',
                icon: 'fas fa-clock',
                color: 'text-yellow-600'
            }
        }
    };

    return configs[type]?.[subtype] || {
        title: 'Unknown Node',
        description: 'Unknown node type',
        icon: 'fas fa-question',
        color: 'text-gray-600'
    };
}

function openCreateWorkflowModal() {
    $('#createWorkflowModal').removeClass('hidden');
}

function closeCreateWorkflowModal() {
    $('#createWorkflowModal').addClass('hidden');
    $('#createWorkflowForm')[0].reset();
}

function createNewWorkflow() {
    const formData = new FormData(document.getElementById('createWorkflowForm'));
    const data = Object.fromEntries(formData.entries());

    // Here you would typically send the data to the server
    console.log('Creating workflow:', data);

    // For now, just close the modal and show success
    closeCreateWorkflowModal();
    showNotification('Workflow created successfully!', 'success');

    // Clear the canvas for the new workflow
    clearCanvas();
}

function selectNode(nodeId) {
    // Remove previous selection
    $('.workflow-node').removeClass('selected');

    // Select new node
    $(`#${nodeId}`).addClass('selected');
    selectedNode = nodeId;

    // Update properties panel
    updatePropertiesPanel(nodeId);
}

function updatePropertiesPanel(nodeId) {
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;

    const propertiesHtml = `
        <div class="space-y-4">
            <div>
                <h4 class="font-medium text-gray-900">${node.config.title}</h4>
                <p class="text-sm text-gray-600">${node.config.description}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Node Name</label>
                <input type="text" value="${node.config.title}"
                       class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea rows="3"
                          class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm">${node.config.description}</textarea>
            </div>
            <button onclick="deleteNode('${nodeId}')"
                    class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm">
                Delete Node
            </button>
        </div>
    `;

    $('#nodeProperties').html(propertiesHtml);
}

function deleteNode(nodeId) {
    if (confirm('Are you sure you want to delete this node?')) {
        $(`#${nodeId}`).remove();
        nodes = nodes.filter(n => n.id !== nodeId);
        connections = connections.filter(c => c.from !== nodeId && c.to !== nodeId);
        updateConnections();

        if (selectedNode === nodeId) {
            selectedNode = null;
            $('#nodeProperties').html(`
                <div class="text-center text-gray-500 py-8">
                    <i class="fas fa-mouse-pointer text-3xl mb-2"></i>
                    <p>Select a node to edit its properties</p>
                </div>
            `);
        }
    }
}

function clearCanvas() {
    $('#nodesContainer').empty();
    $('#connectionSvg').empty().append(`
        <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#6b7280" />
            </marker>
        </defs>
    `);
    nodes = [];
    connections = [];
    selectedNode = null;
    nodeCounter = 0;

    $('#nodeProperties').html(`
        <div class="text-center text-gray-500 py-8">
            <i class="fas fa-mouse-pointer text-3xl mb-2"></i>
            <p>Select a node to edit its properties</p>
        </div>
    `);
}

function saveWorkflow() {
    if (nodes.length === 0) {
        showAlert('Please add some nodes to the workflow before saving.', 'Warning', 'warning');
        return;
    }

    const workflowData = {
        nodes: nodes,
        connections: connections,
        name: currentWorkflow?.name || 'Untitled Workflow',
        type: currentWorkflow?.type || 'custom'
    };

    console.log('Saving workflow:', workflowData);
    showNotification('Workflow saved successfully!', 'success');
}

function zoomIn() {
    zoomLevel = Math.min(zoomLevel * 1.2, 3);
    updateCanvasTransform();
    updateZoomDisplay();
}

function zoomOut() {
    zoomLevel = Math.max(zoomLevel / 1.2, 0.3);
    updateCanvasTransform();
    updateZoomDisplay();
}

function resetZoom() {
    zoomLevel = 1;
    panX = 0;
    panY = 0;
    updateCanvasTransform();
    updateZoomDisplay();
}

function updateCanvasTransform() {
    $('#nodesContainer').css('transform', `translate(${panX}px, ${panY}px) scale(${zoomLevel})`);
    $('#connectionSvg').css('transform', `translate(${panX}px, ${panY}px) scale(${zoomLevel})`);
}

function updateZoomDisplay() {
    $('#zoomLevel').text(Math.round(zoomLevel * 100) + '%');
}

function handleConnectorClick(connector) {
    const nodeId = connector.data('node');
    const connectorType = connector.data('type');

    if (!isConnecting) {
        // Start connection
        isConnecting = true;
        connectionStart = { nodeId, connectorType };
        connector.addClass('connecting');
    } else {
        // Complete connection
        const connectionEnd = { nodeId, connectorType };

        if (connectionStart.nodeId !== connectionEnd.nodeId &&
            connectionStart.connectorType !== connectionEnd.connectorType) {
            createConnection(connectionStart, connectionEnd);
        }

        // Reset connection state
        isConnecting = false;
        $('.node-connector').removeClass('connecting');
        connectionStart = null;
    }
}

function createConnection(start, end) {
    const connectionId = `connection_${start.nodeId}_${end.nodeId}`;

    connections.push({
        id: connectionId,
        from: start.nodeId,
        to: end.nodeId,
        fromType: start.connectorType,
        toType: end.connectorType
    });

    updateConnections();
}

function updateConnections() {
    const svg = $('#connectionSvg');
    svg.find('.connection-line').remove();

    connections.forEach(connection => {
        const fromNode = $(`#${connection.from}`);
        const toNode = $(`#${connection.to}`);

        if (fromNode.length && toNode.length) {
            const fromPos = fromNode.position();
            const toPos = toNode.position();

            const x1 = fromPos.left + fromNode.outerWidth();
            const y1 = fromPos.top + fromNode.outerHeight() / 2;
            const x2 = toPos.left;
            const y2 = toPos.top + toNode.outerHeight() / 2;

            const path = `M ${x1} ${y1} Q ${x1 + 50} ${y1} ${x2 - 50} ${y2} T ${x2} ${y2}`;

            svg.append(`
                <path d="${path}" class="connection-line" data-connection="${connection.id}"/>
            `);
        }
    });
}

function exportWorkflow() {
    const workflowData = {
        nodes: nodes,
        connections: connections,
        metadata: {
            name: currentWorkflow?.name || 'Exported Workflow',
            type: currentWorkflow?.type || 'custom',
            exportedAt: new Date().toISOString()
        }
    };

    const dataStr = JSON.stringify(workflowData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `workflow_${Date.now()}.json`;
    link.click();

    URL.revokeObjectURL(url);
}

// Auto-submit form on filter change
document.querySelectorAll('select[name="project_type"], select[name="status"]').forEach(select => {
    select.addEventListener('change', function() {
        this.form.submit();
    });
});

function createFromTemplate(type) {
    window.location.href = `{{ route('workflows.create') }}?template=${type}`;
}
</script>
@endpush
