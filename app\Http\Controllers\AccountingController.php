<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Expense;
use App\Models\Payment;
use App\Models\Client;
use App\Models\Project;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AccountingController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_accounting')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // Get invoices with filters
        $query = Invoice::with(['client', 'project', 'payments'])
            ->orderBy('created_at', 'desc');

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhereHas('client', function($clientQuery) use ($search) {
                      $clientQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $invoices = $query->paginate(15);
        $summary = $this->getInvoiceSummary();

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => [
                    'invoices' => $invoices->items(),
                    'pagination' => [
                        'current_page' => $invoices->currentPage(),
                        'last_page' => $invoices->lastPage(),
                        'per_page' => $invoices->perPage(),
                        'total' => $invoices->total()
                    ],
                    'summary' => $summary
                ]
            ]);
        }

        return view('accounting.invoices.index', compact('invoices', 'summary'));
    }

    public function create(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('create_accounting')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $clients = Client::where('status', 'active')->get();
        $projects = Project::where('status', '!=', 'completed')->get();

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => [
                    'clients' => $clients,
                    'projects' => $projects
                ]
            ]);
        }

        return view('accounting.invoices.create', compact('clients', 'projects'));
    }

    public function expensesCreate(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('create_accounting')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $categories = DB::table('expense_categories')->where('is_active', true)->get();
        $projects = Project::where('status', '!=', 'completed')->get();

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $categories,
                    'projects' => $projects
                ]
            ]);
        }

        return view('accounting.expenses.create', compact('categories', 'projects'));
    }

    public function dashboard(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_accounting')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $dashboard = [
            'accounts_receivable' => $this->getAccountsReceivable(),
            'accounts_payable' => $this->getAccountsPayable(),
            'cash_position' => $this->getCashPosition(),
            'monthly_summary' => $this->getMonthlySummary(),
            'overdue_invoices' => $this->getOverdueInvoices(),
            'recent_transactions' => $this->getRecentTransactions(),
            'tax_summary' => $this->getTaxSummary(),
            'budget_vs_actual' => $this->getBudgetVsActual()
        ];

        return view('accounting.dashboard', [
            'data' => $dashboard
        ]);
    }

    public function invoices(Request $request)
    {
        $query = Invoice::with(['client', 'project', 'payments'])
            ->orderBy('created_at', 'desc');

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        if ($request->has('overdue')) {
            $query->where('due_date', '<', now())
                  ->where('status', '!=', 'paid');
        }

        $invoices = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => [
                'invoices' => $invoices->items(),
                'pagination' => [
                    'current_page' => $invoices->currentPage(),
                    'last_page' => $invoices->lastPage(),
                    'per_page' => $invoices->perPage(),
                    'total' => $invoices->total()
                ],
                'summary' => $this->getInvoiceSummary()
            ]
        ]);
    }

    public function createInvoice(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'client_id' => 'required|exists:clients,id',
            'project_id' => 'nullable|exists:projects,id',
            'invoice_number' => 'nullable|string|unique:invoices,invoice_number',
            'issue_date' => 'required|date',
            'due_date' => 'required|date|after:issue_date',
            'line_items' => 'required|array|min:1',
            'line_items.*.description' => 'required|string',
            'line_items.*.quantity' => 'required|numeric|min:0',
            'line_items.*.rate' => 'required|numeric|min:0',
            'line_items.*.amount' => 'required|numeric|min:0',
            'tax_rate' => 'nullable|numeric|min:0|max:100',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        
        // Generate invoice number if not provided
        if (!isset($data['invoice_number'])) {
            $data['invoice_number'] = $this->generateInvoiceNumber();
        }

        // Calculate totals
        $subtotal = collect($data['line_items'])->sum('amount');
        $taxAmount = $subtotal * (($data['tax_rate'] ?? 0) / 100);
        $total = $subtotal + $taxAmount - ($data['discount_amount'] ?? 0);

        $data['subtotal'] = $subtotal;
        $data['tax_amount'] = $taxAmount;
        $data['total_amount'] = $total;
        $data['status'] = 'draft';
        $data['created_by'] = $request->user()->id;

        $invoice = Invoice::create($data);

        // Track invoice creation
        AnalyticsEvent::track('invoice_created', 'accounting', $invoice, [
            'amount' => $total,
            'client_id' => $data['client_id']
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Invoice created successfully',
            'data' => ['invoice' => $invoice->load(['client', 'project'])]
        ], 201);
    }

    public function sendInvoice(Request $request, Invoice $invoice)
    {
        if ($invoice->status !== 'draft') {
            return response()->json([
                'success' => false,
                'message' => 'Only draft invoices can be sent'
            ], 422);
        }

        $invoice->update([
            'status' => 'sent',
            'sent_at' => now()
        ]);

        // Send email notification (implement email service)
        // Mail::to($invoice->client->email)->send(new InvoiceEmail($invoice));

        // Track invoice sent
        AnalyticsEvent::track('invoice_sent', 'accounting', $invoice);

        return response()->json([
            'success' => true,
            'message' => 'Invoice sent successfully',
            'data' => ['invoice' => $invoice->fresh()]
        ]);
    }

    public function recordPayment(Request $request, Invoice $invoice)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:cash,bank_transfer,card,check,other',
            'payment_date' => 'required|date',
            'reference_number' => 'nullable|string',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['invoice_id'] = $invoice->id;
        $data['recorded_by'] = $request->user()->id;

        $payment = Payment::create($data);

        // Update invoice status
        $totalPaid = $invoice->payments()->sum('amount');
        if ($totalPaid >= $invoice->total_amount) {
            $invoice->update(['status' => 'paid', 'paid_at' => now()]);
        } elseif ($totalPaid > 0) {
            $invoice->update(['status' => 'partial']);
        }

        // Track payment
        AnalyticsEvent::track('payment_recorded', 'accounting', $payment, [
            'amount' => $data['amount'],
            'invoice_id' => $invoice->id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment recorded successfully',
            'data' => [
                'payment' => $payment,
                'invoice' => $invoice->fresh(['payments'])
            ]
        ], 201);
    }

    public function expenses(Request $request)
    {
        $query = Expense::with(['category', 'recordedBy'])
            ->orderBy('expense_date', 'desc');

        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->has('date_from')) {
            $query->whereDate('expense_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('expense_date', '<=', $request->date_to);
        }

        $expenses = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => [
                'expenses' => $expenses->items(),
                'pagination' => [
                    'current_page' => $expenses->currentPage(),
                    'last_page' => $expenses->lastPage(),
                    'per_page' => $expenses->perPage(),
                    'total' => $expenses->total()
                ],
                'summary' => $this->getExpenseSummary()
            ]
        ]);
    }

    public function createExpense(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'description' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0.01',
            'expense_date' => 'required|date',
            'category_id' => 'required|exists:expense_categories,id',
            'vendor' => 'nullable|string|max:255',
            'receipt_number' => 'nullable|string|max:100',
            'tax_amount' => 'nullable|numeric|min:0',
            'is_billable' => 'boolean',
            'project_id' => 'nullable|exists:projects,id',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $validator->validated();
        $data['recorded_by'] = $request->user()->id;

        $expense = Expense::create($data);

        // Track expense
        AnalyticsEvent::track('expense_recorded', 'accounting', $expense, [
            'amount' => $data['amount'],
            'category_id' => $data['category_id']
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Expense recorded successfully',
            'data' => ['expense' => $expense->load(['category', 'recordedBy'])]
        ], 201);
    }

    public function profitLossReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $revenue = $this->getRevenueForPeriod($startDate, $endDate);
        $expenses = $this->getExpensesForPeriod($startDate, $endDate);

        $report = [
            'period' => [
                'start_date' => $startDate,
                'end_date' => $endDate
            ],
            'revenue' => $revenue,
            'expenses' => $expenses,
            'gross_profit' => $revenue['total'] - $expenses['cost_of_goods_sold'],
            'operating_expenses' => $expenses['operating'],
            'net_profit' => $revenue['total'] - $expenses['total'],
            'profit_margin' => $revenue['total'] > 0 ? (($revenue['total'] - $expenses['total']) / $revenue['total']) * 100 : 0
        ];

        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => $report
            ]);
        }

        return view('accounting.reports.profit-loss', compact('report', 'startDate', 'endDate'));
    }

    public function balanceSheet(Request $request)
    {
        $asOfDate = $request->get('as_of_date', now());

        $balanceSheet = [
            'as_of_date' => $asOfDate,
            'assets' => [
                'current_assets' => [
                    'cash' => $this->getCashBalance($asOfDate),
                    'accounts_receivable' => $this->getAccountsReceivableBalance($asOfDate),
                    'total' => 0
                ],
                'fixed_assets' => [
                    'equipment' => $this->getEquipmentValue($asOfDate),
                    'total' => 0
                ],
                'total_assets' => 0
            ],
            'liabilities' => [
                'current_liabilities' => [
                    'accounts_payable' => $this->getAccountsPayableBalance($asOfDate),
                    'total' => 0
                ],
                'total_liabilities' => 0
            ],
            'equity' => [
                'retained_earnings' => $this->getRetainedEarnings($asOfDate),
                'total_equity' => 0
            ]
        ];

        // Calculate totals
        $balanceSheet['assets']['current_assets']['total'] = 
            $balanceSheet['assets']['current_assets']['cash'] + 
            $balanceSheet['assets']['current_assets']['accounts_receivable'];

        $balanceSheet['assets']['fixed_assets']['total'] = 
            $balanceSheet['assets']['fixed_assets']['equipment'];

        $balanceSheet['assets']['total_assets'] = 
            $balanceSheet['assets']['current_assets']['total'] + 
            $balanceSheet['assets']['fixed_assets']['total'];

        $balanceSheet['liabilities']['current_liabilities']['total'] = 
            $balanceSheet['liabilities']['current_liabilities']['accounts_payable'];

        $balanceSheet['liabilities']['total_liabilities'] = 
            $balanceSheet['liabilities']['current_liabilities']['total'];

        $balanceSheet['equity']['total_equity'] = 
            $balanceSheet['assets']['total_assets'] - $balanceSheet['liabilities']['total_liabilities'];

        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => $balanceSheet
            ]);
        }

        return view('accounting.reports.balance-sheet', compact('balanceSheet', 'asOfDate'));
    }

    public function expensesIndex(Request $request)
    {
        $user = $request->user();

        $query = \App\Models\Expense::with(['category', 'recordedBy'])
            ->orderBy('expense_date', 'desc');

        // Apply filters
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }

        if ($request->has('date_from') && $request->date_from) {
            $query->whereDate('expense_date', '>=', $request->date_from);
        }

        if ($request->has('date_to') && $request->date_to) {
            $query->whereDate('expense_date', '<=', $request->date_to);
        }

        if ($request->has('search') && $request->search) {
            $query->where(function($q) use ($request) {
                $q->where('description', 'like', '%' . $request->search . '%')
                  ->orWhere('vendor', 'like', '%' . $request->search . '%');
            });
        }

        $expenses = $query->paginate($request->get('per_page', 15));

        // Calculate summary data
        $summary = [
            'total_expenses' => \App\Models\Expense::sum('amount'),
            'this_month' => \App\Models\Expense::whereMonth('expense_date', now()->month)->sum('amount'),
            'pending_approval' => \App\Models\Expense::where('status', 'pending')->sum('amount'),
            'approved_count' => \App\Models\Expense::where('status', 'approved')->count()
        ];

        // Get categories for filter
        $categories = \App\Models\ExpenseCategory::orderBy('name')->get();

        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => [
                    'expenses' => $expenses->items(),
                    'pagination' => [
                        'current_page' => $expenses->currentPage(),
                        'last_page' => $expenses->lastPage(),
                        'per_page' => $expenses->perPage(),
                        'total' => $expenses->total()
                    ],
                    'summary' => $summary
                ]
            ]);
        }

        return view('accounting.expenses.index', compact('expenses', 'categories', 'summary'));
    }

    // Private helper methods
    private function generateInvoiceNumber(): string
    {
        $prefix = 'INV-' . date('Y') . '-';
        $lastInvoice = Invoice::where('invoice_number', 'like', $prefix . '%')
            ->orderBy('invoice_number', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastNumber = intval(substr($lastInvoice->invoice_number, strlen($prefix)));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    private function getAccountsReceivable(): array
    {
        $total = Invoice::whereIn('status', ['sent', 'partial'])->sum('total_amount');
        $overdue = Invoice::whereIn('status', ['sent', 'partial'])
            ->where('due_date', '<', now())
            ->sum('total_amount');

        return [
            'total' => $total,
            'overdue' => $overdue,
            'current' => $total - $overdue
        ];
    }

    private function getAccountsPayable(): array
    {
        // This would be implemented with a proper expense/bill tracking system
        return [
            'total' => 0,
            'overdue' => 0,
            'current' => 0
        ];
    }

    private function getCashPosition(): array
    {
        $totalReceived = Payment::sum('amount');
        $totalExpenses = Expense::sum('amount');

        return [
            'balance' => $totalReceived - $totalExpenses,
            'monthly_inflow' => Payment::whereMonth('payment_date', now()->month)->sum('amount'),
            'monthly_outflow' => Expense::whereMonth('expense_date', now()->month)->sum('amount')
        ];
    }

    private function getMonthlySummary(): array
    {
        $currentMonth = now()->startOfMonth();
        
        return [
            'revenue' => Invoice::whereMonth('issue_date', $currentMonth->month)->sum('total_amount'),
            'expenses' => Expense::whereMonth('expense_date', $currentMonth->month)->sum('amount'),
            'invoices_sent' => Invoice::whereMonth('issue_date', $currentMonth->month)->count(),
            'payments_received' => Payment::whereMonth('payment_date', $currentMonth->month)->count()
        ];
    }

    private function getOverdueInvoices(): array
    {
        return Invoice::with(['client'])
            ->whereIn('status', ['sent', 'partial'])
            ->where('due_date', '<', now())
            ->orderBy('due_date')
            ->limit(10)
            ->get()
            ->toArray();
    }

    private function getRecentTransactions(): array
    {
        $payments = Payment::with(['invoice.client'])
            ->orderBy('payment_date', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($payment) {
                return [
                    'type' => 'payment',
                    'date' => $payment->payment_date,
                    'description' => 'Payment from ' . $payment->invoice->client->name,
                    'amount' => $payment->amount,
                    'reference' => $payment->reference_number
                ];
            });

        $expenses = Expense::with(['category'])
            ->orderBy('expense_date', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($expense) {
                return [
                    'type' => 'expense',
                    'date' => $expense->expense_date,
                    'description' => $expense->description,
                    'amount' => -$expense->amount,
                    'reference' => $expense->receipt_number
                ];
            });

        return $payments->concat($expenses)
            ->sortByDesc('date')
            ->take(10)
            ->values()
            ->toArray();
    }

    private function getTaxSummary(): array
    {
        $currentQuarter = now()->quarter;
        $quarterStart = now()->startOfQuarter();
        
        return [
            'quarter' => $currentQuarter,
            'tax_collected' => Invoice::whereBetween('issue_date', [$quarterStart, now()])->sum('tax_amount'),
            'tax_paid' => Expense::whereBetween('expense_date', [$quarterStart, now()])->sum('tax_amount'),
            'net_tax_liability' => 0 // Would be calculated based on tax rules
        ];
    }

    private function getBudgetVsActual(): array
    {
        // This would compare against predefined budgets
        return [
            'revenue_budget' => 500000,
            'revenue_actual' => Invoice::whereMonth('issue_date', now()->month)->sum('total_amount'),
            'expense_budget' => 300000,
            'expense_actual' => Expense::whereMonth('expense_date', now()->month)->sum('amount')
        ];
    }

    private function getInvoiceSummary(): array
    {
        return [
            'total_outstanding' => Invoice::whereIn('status', ['sent', 'partial'])->sum('total_amount'),
            'overdue_amount' => Invoice::whereIn('status', ['sent', 'partial'])
                ->where('due_date', '<', now())->sum('total_amount'),
            'paid_this_month' => Invoice::where('status', 'paid')
                ->whereMonth('paid_at', now()->month)->sum('total_amount')
        ];
    }

    private function getExpenseSummary(): array
    {
        return [
            'total_this_month' => Expense::whereMonth('expense_date', now()->month)->sum('amount'),
            'total_this_year' => Expense::whereYear('expense_date', now()->year)->sum('amount'),
            'average_monthly' => Expense::whereYear('expense_date', now()->year)
                ->groupBy(DB::raw('MONTH(expense_date)'))
                ->selectRaw('AVG(amount) as avg_amount')
                ->value('avg_amount') ?? 0
        ];
    }

    // Additional helper methods for financial calculations...
    private function getRevenueForPeriod($startDate, $endDate): array
    {
        return [
            'total' => Invoice::whereBetween('issue_date', [$startDate, $endDate])->sum('total_amount'),
            'by_service' => [], // Would break down by service type
            'recurring' => 0 // Recurring revenue
        ];
    }

    private function getExpensesForPeriod($startDate, $endDate): array
    {
        $total = Expense::whereBetween('expense_date', [$startDate, $endDate])->sum('amount');
        
        return [
            'total' => $total,
            'cost_of_goods_sold' => $total * 0.4, // Estimate
            'operating' => $total * 0.6 // Estimate
        ];
    }

    private function getCashBalance($asOfDate): float
    {
        $received = Payment::where('payment_date', '<=', $asOfDate)->sum('amount');
        $spent = Expense::where('expense_date', '<=', $asOfDate)->sum('amount');
        
        return $received - $spent;
    }

    private function getAccountsReceivableBalance($asOfDate): float
    {
        return Invoice::whereIn('status', ['sent', 'partial'])
            ->where('issue_date', '<=', $asOfDate)
            ->sum('total_amount');
    }

    private function getAccountsPayableBalance($asOfDate): float
    {
        // Would be calculated from bills/payables system
        return 0;
    }

    private function getEquipmentValue($asOfDate): float
    {
        // Would be calculated from asset management system
        return 500000; // Estimated equipment value
    }

    private function getRetainedEarnings($asOfDate): float
    {
        $revenue = Invoice::where('issue_date', '<=', $asOfDate)->sum('total_amount');
        $expenses = Expense::where('expense_date', '<=', $asOfDate)->sum('amount');

        return $revenue - $expenses;
    }

    public function transactionsIndex(Request $request)
    {
        // Get all transactions (payments and expenses)
        $payments = Payment::with(['invoice.client'])
            ->orderBy('payment_date', 'desc')
            ->get()
            ->map(function ($payment) {
                return [
                    'id' => $payment->id,
                    'type' => 'payment',
                    'date' => $payment->payment_date,
                    'description' => 'Payment from ' . $payment->invoice->client->name,
                    'amount' => $payment->amount,
                    'reference' => $payment->reference_number,
                    'method' => $payment->payment_method
                ];
            });

        $expenses = Expense::with(['category'])
            ->orderBy('expense_date', 'desc')
            ->get()
            ->map(function ($expense) {
                return [
                    'id' => $expense->id,
                    'type' => 'expense',
                    'date' => $expense->expense_date,
                    'description' => $expense->description,
                    'amount' => -$expense->amount,
                    'reference' => $expense->receipt_number,
                    'category' => $expense->category->name ?? 'Uncategorized'
                ];
            });

        $transactions = $payments->concat($expenses)
            ->sortByDesc('date')
            ->values();

        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => [
                    'transactions' => $transactions
                ]
            ]);
        }

        return view('accounting.transactions.index', compact('transactions'));
    }

    public function reportsIndex(Request $request)
    {
        // Return view for web requests, JSON for API requests
        if ($request->expectsJson() || $request->is('api/*')) {
            return response()->json([
                'success' => true,
                'data' => [
                    'available_reports' => [
                        'profit_loss' => 'Profit & Loss Statement',
                        'balance_sheet' => 'Balance Sheet',
                        'cash_flow' => 'Cash Flow Statement'
                    ]
                ]
            ]);
        }

        return view('accounting.reports.index');
    }

    public function exportReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'report_type' => 'required|in:profit-loss,balance-sheet,cash-flow',
            'format' => 'required|in:pdf,csv,zip',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid parameters',
                'errors' => $validator->errors()
            ], 422);
        }

        $reportType = $request->report_type;
        $format = $request->format;
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        try {
            // Generate report data based on type
            $reportData = $this->generateReportData($reportType, $startDate, $endDate);

            // Generate filename
            $filename = "{$reportType}-report-{$startDate->format('Y-m-d')}-to-{$endDate->format('Y-m-d')}";

            switch ($format) {
                case 'pdf':
                    return $this->exportToPdf($reportData, $filename, $reportType);
                case 'csv':
                    return $this->exportToCsv($reportData, $filename, $reportType);
                case 'zip':
                    return $this->exportToZip($reportData, $filename, $reportType, $startDate, $endDate);
                default:
                    return response()->json(['success' => false, 'message' => 'Invalid format'], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating report: ' . $e->getMessage()
            ], 500);
        }
    }

    private function generateReportData($reportType, $startDate, $endDate)
    {
        switch ($reportType) {
            case 'profit-loss':
                return $this->generateProfitLossData($startDate, $endDate);
            case 'balance-sheet':
                return $this->generateBalanceSheetData($startDate, $endDate);
            case 'cash-flow':
                return $this->generateCashFlowData($startDate, $endDate);
            default:
                throw new \Exception('Invalid report type');
        }
    }

    private function generateProfitLossData($startDate, $endDate)
    {
        // Sample data - replace with actual financial data queries
        return [
            'period' => $startDate->format('M d, Y') . ' - ' . $endDate->format('M d, Y'),
            'revenue' => [
                'studio_bookings' => 15000.00,
                'equipment_rental' => 3000.00,
                'other_income' => 500.00,
                'total' => 18500.00
            ],
            'expenses' => [
                'equipment_maintenance' => 2000.00,
                'utilities' => 1500.00,
                'staff_salaries' => 8000.00,
                'marketing' => 1000.00,
                'other_expenses' => 500.00,
                'total' => 13000.00
            ],
            'net_profit' => 5500.00
        ];
    }

    private function generateBalanceSheetData($startDate, $endDate)
    {
        // Sample data - replace with actual financial data queries
        return [
            'as_of' => $endDate->format('M d, Y'),
            'assets' => [
                'current_assets' => [
                    'cash' => 25000.00,
                    'accounts_receivable' => 8000.00,
                    'inventory' => 5000.00,
                    'total' => 38000.00
                ],
                'fixed_assets' => [
                    'equipment' => 150000.00,
                    'furniture' => 10000.00,
                    'total' => 160000.00
                ],
                'total_assets' => 198000.00
            ],
            'liabilities' => [
                'current_liabilities' => [
                    'accounts_payable' => 5000.00,
                    'accrued_expenses' => 2000.00,
                    'total' => 7000.00
                ],
                'long_term_liabilities' => [
                    'equipment_loans' => 50000.00,
                    'total' => 50000.00
                ],
                'total_liabilities' => 57000.00
            ],
            'equity' => [
                'owner_equity' => 141000.00,
                'total_equity' => 141000.00
            ]
        ];
    }

    private function generateCashFlowData($startDate, $endDate)
    {
        // Sample data - replace with actual financial data queries
        return [
            'period' => $startDate->format('M d, Y') . ' - ' . $endDate->format('M d, Y'),
            'operating_activities' => [
                'cash_from_customers' => 18000.00,
                'cash_to_suppliers' => -8000.00,
                'cash_to_employees' => -7000.00,
                'net_operating_cash' => 3000.00
            ],
            'investing_activities' => [
                'equipment_purchases' => -15000.00,
                'net_investing_cash' => -15000.00
            ],
            'financing_activities' => [
                'loan_proceeds' => 20000.00,
                'loan_payments' => -2000.00,
                'net_financing_cash' => 18000.00
            ],
            'net_cash_flow' => 6000.00,
            'beginning_cash' => 19000.00,
            'ending_cash' => 25000.00
        ];
    }

    private function exportToPdf($data, $filename, $reportType)
    {
        // For now, return a simple response. In production, you'd use a PDF library like DomPDF
        $content = $this->generatePdfContent($data, $reportType);

        return response($content)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}.pdf\"");
    }

    private function exportToCsv($data, $filename, $reportType)
    {
        $csvContent = $this->generateCsvContent($data, $reportType);

        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}.csv\"");
    }

    private function exportToZip($data, $filename, $reportType, $startDate, $endDate)
    {
        // Create a temporary zip file with PDF and CSV versions
        $zipPath = storage_path("app/temp/{$filename}.zip");
        $zip = new \ZipArchive();

        if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
            // Add PDF version
            $pdfContent = $this->generatePdfContent($data, $reportType);
            $zip->addFromString("{$filename}.pdf", $pdfContent);

            // Add CSV version
            $csvContent = $this->generateCsvContent($data, $reportType);
            $zip->addFromString("{$filename}.csv", $csvContent);

            $zip->close();

            return response()->download($zipPath)->deleteFileAfterSend(true);
        }

        throw new \Exception('Could not create ZIP file');
    }

    private function generatePdfContent($data, $reportType)
    {
        // Simple text-based PDF content for demo
        $content = "Financial Report: " . ucwords(str_replace('-', ' ', $reportType)) . "\n";
        $content .= "Generated on: " . now()->format('M d, Y H:i:s') . "\n\n";
        $content .= json_encode($data, JSON_PRETTY_PRINT);

        return $content;
    }

    private function generateCsvContent($data, $reportType)
    {
        $csv = "Financial Report," . ucwords(str_replace('-', ' ', $reportType)) . "\n";
        $csv .= "Generated," . now()->format('M d, Y H:i:s') . "\n\n";

        // Convert data to CSV format
        $csv .= $this->arrayToCsv($data);

        return $csv;
    }

    private function arrayToCsv($array, $prefix = '')
    {
        $csv = '';
        foreach ($array as $key => $value) {
            $fullKey = $prefix ? $prefix . '.' . $key : $key;
            if (is_array($value)) {
                $csv .= $this->arrayToCsv($value, $fullKey);
            } else {
                $csv .= $fullKey . ',' . $value . "\n";
            }
        }
        return $csv;
    }
}
