@extends('layouts.app')

@section('title', 'User Profile - ' . $user->first_name . ' ' . $user->last_name)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mr-4">
                        @if($user->avatar)
                            <img src="{{ $user->avatar }}" alt="{{ $user->first_name }}" class="w-16 h-16 rounded-full">
                        @else
                            <span class="text-xl font-medium text-gray-600">
                                {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
                            </span>
                        @endif
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">{{ $user->first_name }} {{ $user->last_name }}</h1>
                        <p class="mt-1 text-sm text-gray-600">
                            {{ $user->role->name ?? 'No Role' }} • {{ $user->department->name ?? 'No Department' }}
                        </p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('users.edit', $user) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Edit Profile
                    </a>
                    <a href="{{ route('users.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Back to Users
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- User Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Personal Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Personal Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->first_name }} {{ $user->last_name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Email Address</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->email }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->phone ?: 'Not provided' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($user->status === 'active') bg-green-100 text-green-800
                                        @elseif($user->status === 'inactive') bg-red-100 text-red-800
                                        @else bg-yellow-100 text-yellow-800 @endif">
                                        {{ ucfirst($user->status) }}
                                    </span>
                                    @if($user->is_available)
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            Available
                                        </span>
                                    @endif
                                </dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Professional Information -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Professional Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Department</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    @if($user->department)
                                        <div class="flex items-center">
                                            <i class="{{ $user->department->icon ?? 'fas fa-building' }} text-{{ $user->department->color_code ?? 'blue' }}-600 mr-2"></i>
                                            {{ $user->department->name }}
                                        </div>
                                    @else
                                        <span class="text-gray-500">No department assigned</span>
                                    @endif
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Role</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    @if($user->role)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            {{ $user->role->name }} (Level {{ $user->role->level }})
                                        </span>
                                    @else
                                        <span class="text-gray-500">No role assigned</span>
                                    @endif
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Hourly Rate</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    @if($user->hourly_rate)
                                        ${{ number_format($user->hourly_rate, 2) }}/hour
                                    @else
                                        Not set
                                    @endif
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Availability</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium {{ $user->is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $user->is_available ? 'Available' : 'Unavailable' }}
                                    </span>
                                </dd>
                            </div>
                        </dl>

                        @if($user->skills && count($user->skills) > 0)
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500 mb-2">Skills</dt>
                                <dd class="flex flex-wrap gap-2">
                                    @foreach($user->skills as $skill)
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            {{ $skill }}
                                        </span>
                                    @endforeach
                                </dd>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Account Activity -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Account Activity</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Member Since</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->created_at->format('M j, Y') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Active</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    @if($user->last_active_at)
                                        {{ $user->last_active_at->diffForHumans() }}
                                    @else
                                        Never
                                    @endif
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Profile Updated</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $user->updated_at->diffForHumans() }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="{{ route('users.edit', $user) }}" class="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Edit Profile
                        </a>
                        
                        @if($user->id !== auth()->id())
                            <button onclick="toggleUserStatus()" class="w-full bg-{{ $user->status === 'active' ? 'red' : 'green' }}-100 hover:bg-{{ $user->status === 'active' ? 'red' : 'green' }}-200 text-{{ $user->status === 'active' ? 'red' : 'green' }}-700 px-4 py-2 rounded-md text-sm font-medium">
                                {{ $user->status === 'active' ? 'Deactivate User' : 'Activate User' }}
                            </button>
                        @endif
                        
                        <a href="mailto:{{ $user->email }}" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Send Email
                        </a>
                        
                        @if($user->phone)
                            <a href="tel:{{ $user->phone }}" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                                Call Phone
                            </a>
                        @endif
                        
                        <a href="{{ route('users.index') }}" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Back to Users
                        </a>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Statistics</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Projects:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $user->projects->count() ?? 0 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Tasks:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $user->tasks->count() ?? 0 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Completed Tasks:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $user->tasks->where('status', 'completed')->count() ?? 0 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Success Rate:</span>
                            <span class="text-sm font-medium text-gray-900">
                                @if($user->tasks && $user->tasks->count() > 0)
                                    {{ round(($user->tasks->where('status', 'completed')->count() / $user->tasks->count()) * 100) }}%
                                @else
                                    N/A
                                @endif
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Permissions -->
                @if($user->role && $user->role->permissions)
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Permissions</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-2 max-h-48 overflow-y-auto">
                                @foreach($user->role->permissions as $permission)
                                    <div class="flex items-center text-sm">
                                        <i class="fas fa-check text-green-500 mr-2"></i>
                                        <span class="text-gray-700">{{ str_replace('_', ' ', $permission) }}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </main>
</div>
@endsection

@push('scripts')
<script>
    async function toggleUserStatus() {
        const confirmed = await showConfirm('Are you sure you want to change this user\'s status?', 'Change User Status', 'warning');
        if (confirmed) {
            fetch(`{{ route('users.toggle-status', $user) }}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('User status updated successfully!', 'Success', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert('Error: ' + data.message, 'Error', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('An error occurred while updating the user status', 'Error', 'error');
            });
        }
    }
</script>
@endpush
