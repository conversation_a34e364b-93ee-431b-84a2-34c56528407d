<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('company')->nullable();
            $table->enum('type', ['individual', 'artist', 'label', 'brand', 'agency'])->default('individual');
            $table->text('bio')->nullable();
            $table->string('avatar')->nullable();
            $table->json('social_links')->nullable();
            $table->enum('status', ['active', 'inactive', 'blacklisted'])->default('active');
            $table->decimal('total_spent', 12, 2)->default(0);
            $table->integer('total_projects')->default(0);
            $table->decimal('rating', 3, 2)->nullable(); // average rating
            $table->json('preferences')->nullable();
            $table->timestamp('last_project_at')->nullable();
            $table->timestamps();
            
            $table->index(['status', 'type']);
            $table->index(['total_spent', 'rating']);
            $table->index('last_project_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};
