<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StudioRoomSeeder extends Seeder
{
    public function run(): void
    {
        $rooms = [
            [
                'name' => 'Studio A - Main Recording',
                'slug' => 'studio-a-main',
                'description' => 'Our flagship recording studio with premium equipment and acoustics',
                'type' => 'recording',
                'capacity' => 8,
                'hourly_rate' => 150.00,
                'requires_engineer' => true,
                'equipment' => json_encode([
                    'console' => 'SSL 4000 E Series',
                    'monitors' => 'Yamaha NS-10M, KRK Rokit 8',
                    'microphones' => ['Neumann U87', 'Shure SM57', 'AKG C414'],
                    'instruments' => ['Yamaha Grand Piano', 'Fender Stratocaster', 'Gibson Les Paul'],
                    'software' => ['Pro Tools HDX', 'Logic Pro X', 'Ableton Live']
                ]),
                'features' => json_encode([
                    'isolation_booth',
                    'live_room',
                    'control_room',
                    'lounge_area',
                    'kitchen',
                    'air_conditioning'
                ]),
                'availability_schedule' => json_encode([
                    'monday' => ['09:00-22:00'],
                    'tuesday' => ['09:00-22:00'],
                    'wednesday' => ['09:00-22:00'],
                    'thursday' => ['09:00-22:00'],
                    'friday' => ['09:00-24:00'],
                    'saturday' => ['10:00-24:00'],
                    'sunday' => ['12:00-20:00']
                ])
            ],
            [
                'name' => 'Studio B - Mixing Suite',
                'slug' => 'studio-b-mixing',
                'description' => 'Professional mixing and mastering suite',
                'type' => 'mixing',
                'capacity' => 4,
                'hourly_rate' => 120.00,
                'requires_engineer' => true,
                'equipment' => json_encode([
                    'console' => 'Avid S6 Control Surface',
                    'monitors' => 'Genelec 1032A, Auratone 5C',
                    'processors' => ['Universal Audio LA-2A', 'Empirical Labs Distressor'],
                    'software' => ['Pro Tools Ultimate', 'Waves Complete Bundle']
                ]),
                'features' => json_encode([
                    'acoustically_treated',
                    'surround_sound_capable',
                    'mastering_grade_monitoring'
                ])
            ],
            [
                'name' => 'Podcast Studio',
                'slug' => 'podcast-studio',
                'description' => 'Dedicated podcast recording space',
                'type' => 'podcast',
                'capacity' => 6,
                'hourly_rate' => 80.00,
                'requires_engineer' => false,
                'equipment' => json_encode([
                    'microphones' => ['Shure SM7B x4', 'Audio-Technica AT2020'],
                    'interface' => 'Focusrite Scarlett 18i20',
                    'headphones' => 'Sony MDR-7506 x6',
                    'software' => ['Hindenburg Pro', 'Adobe Audition']
                ]),
                'features' => json_encode([
                    'soundproof_booth',
                    'video_recording_capability',
                    'live_streaming_setup',
                    'comfortable_seating'
                ])
            ],
            [
                'name' => 'Video Production Suite',
                'slug' => 'video-production',
                'description' => 'Full video production and editing facility',
                'type' => 'video',
                'capacity' => 10,
                'hourly_rate' => 200.00,
                'requires_engineer' => true,
                'equipment' => json_encode([
                    'cameras' => ['Sony FX6', 'Canon C70', 'Blackmagic URSA Mini'],
                    'lighting' => ['ARRI SkyPanel', 'Aputure 300D', 'LED Panel Kit'],
                    'audio' => ['Sennheiser MKE 600', 'Rode VideoMic Pro'],
                    'editing' => ['DaVinci Resolve Studio', 'Final Cut Pro', 'Adobe Premiere Pro']
                ]),
                'features' => json_encode([
                    'green_screen',
                    'professional_lighting_grid',
                    'multiple_camera_angles',
                    'live_streaming_capability'
                ])
            ],
            [
                'name' => 'Rehearsal Room',
                'slug' => 'rehearsal-room',
                'description' => 'Band rehearsal and practice space',
                'type' => 'rehearsal',
                'capacity' => 6,
                'hourly_rate' => 50.00,
                'requires_engineer' => false,
                'equipment' => json_encode([
                    'instruments' => ['Full Drum Kit', 'Bass Amp', 'Guitar Amps x2'],
                    'pa_system' => 'Yamaha Stagepas 600i',
                    'microphones' => ['Shure SM58 x4']
                ]),
                'features' => json_encode([
                    'sound_isolation',
                    'instrument_storage',
                    'mirror_wall'
                ])
            ]
        ];

        foreach ($rooms as $room) {
            DB::table('studio_rooms')->insert(array_merge($room, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}
