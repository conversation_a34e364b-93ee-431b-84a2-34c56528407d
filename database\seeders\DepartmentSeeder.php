<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DepartmentSeeder extends Seeder
{
    public function run(): void
    {
        $departments = [
            [
                'name' => 'Administration',
                'slug' => 'admin',
                'description' => 'Overall management and administration',
                'color_code' => '#1F2937',
                'icon' => 'shield-check',
                'settings' => json_encode(['can_access_all_projects' => true])
            ],
            [
                'name' => 'Project Management',
                'slug' => 'project-management',
                'description' => 'Project coordination and management',
                'color_code' => '#3B82F6',
                'icon' => 'clipboard-list',
                'settings' => json_encode(['default_project_access' => true])
            ],
            [
                'name' => 'Audio Engineering',
                'slug' => 'audio-engineering',
                'description' => 'Recording, mixing, and mastering',
                'color_code' => '#10B981',
                'icon' => 'volume-up',
                'settings' => json_encode(['equipment_access' => ['mixing_console', 'monitors', 'microphones']])
            ],
            [
                'name' => 'Music Production',
                'slug' => 'music-production',
                'description' => 'Beat making, arrangement, and production',
                'color_code' => '#F59E0B',
                'icon' => 'music-note',
                'settings' => json_encode(['software_access' => ['pro_tools', 'logic_pro', 'ableton']])
            ],
            [
                'name' => 'Video Production',
                'slug' => 'video-production',
                'description' => 'Video recording, editing, and post-production',
                'color_code' => '#EF4444',
                'icon' => 'video-camera',
                'settings' => json_encode(['equipment_access' => ['cameras', 'lighting', 'editing_suites']])
            ],
            [
                'name' => 'Post Production',
                'slug' => 'post-production',
                'description' => 'Final editing, color grading, and delivery',
                'color_code' => '#8B5CF6',
                'icon' => 'film',
                'settings' => json_encode(['software_access' => ['final_cut', 'davinci_resolve', 'after_effects']])
            ],
            [
                'name' => 'Client Relations',
                'slug' => 'client-relations',
                'description' => 'Client communication and support',
                'color_code' => '#06B6D4',
                'icon' => 'user-group',
                'settings' => json_encode(['client_access' => true, 'booking_management' => true])
            ],
            [
                'name' => 'Marketing',
                'slug' => 'marketing',
                'description' => 'Promotion and marketing activities',
                'color_code' => '#EC4899',
                'icon' => 'megaphone',
                'settings' => json_encode(['social_media_access' => true])
            ]
        ];

        foreach ($departments as $dept) {
            DB::table('departments')->insert(array_merge($dept, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}
