@extends('layouts.app')

@section('title', 'Studio Rooms')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Studio Rooms</h1>
                <a href="{{ route('studio-rooms.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Room
                </a>
            </div>

            <!-- Filters -->
            <div class="card shadow mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('studio-rooms.index') }}" class="row g-3">
                        <div class="col-md-3">
                            <label for="type" class="form-label">Room Type</label>
                            <select name="type" id="type" class="form-select">
                                <option value="">All Types</option>
                                <option value="recording" {{ request('type') == 'recording' ? 'selected' : '' }}>Recording</option>
                                <option value="mixing" {{ request('type') == 'mixing' ? 'selected' : '' }}>Mixing</option>
                                <option value="mastering" {{ request('type') == 'mastering' ? 'selected' : '' }}>Mastering</option>
                                <option value="podcast" {{ request('type') == 'podcast' ? 'selected' : '' }}>Podcast</option>
                                <option value="video" {{ request('type') == 'video' ? 'selected' : '' }}>Video</option>
                                <option value="rehearsal" {{ request('type') == 'rehearsal' ? 'selected' : '' }}>Rehearsal</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="capacity" class="form-label">Min Capacity</label>
                            <input type="number" name="capacity" id="capacity" class="form-control" value="{{ request('capacity') }}" min="1">
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" name="search" id="search" class="form-control" value="{{ request('search') }}" placeholder="Room name or description">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-outline-primary">Filter</button>
                                <a href="{{ route('studio-rooms.index') }}" class="btn btn-outline-secondary">Clear</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Rooms Grid -->
            <div class="row" id="rooms-grid">
                @forelse($rooms as $room)
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100 shadow">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">{{ $room->name }}</h5>
                            <span class="badge bg-{{ $room->is_available ? 'success' : 'danger' }}">
                                {{ $room->is_available ? 'Available' : 'Unavailable' }}
                            </span>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <span class="badge bg-info">{{ ucfirst($room->type) }}</span>
                                <span class="badge bg-secondary">{{ $room->capacity }} people</span>
                                @if($room->requires_engineer)
                                <span class="badge bg-warning">Engineer Required</span>
                                @endif
                            </div>
                            
                            <p class="text-muted small">{{ Str::limit($room->description, 100) }}</p>
                            
                            <div class="mb-3">
                                <strong class="text-primary">${{ number_format($room->hourly_rate, 2) }}/hour</strong>
                            </div>

                            @if($room->equipment && count($room->equipment) > 0)
                            <div class="mb-3">
                                <small class="text-muted">Equipment:</small>
                                <div class="mt-1">
                                    @foreach(array_slice($room->equipment, 0, 3) as $equipment)
                                    <span class="badge bg-light text-dark">{{ $equipment }}</span>
                                    @endforeach
                                    @if(count($room->equipment) > 3)
                                    <span class="badge bg-light text-dark">+{{ count($room->equipment) - 3 }} more</span>
                                    @endif
                                </div>
                            </div>
                            @endif
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="{{ route('studio-rooms.show', $room) }}" class="btn btn-outline-primary btn-sm">View</a>
                                <a href="{{ route('studio-rooms.edit', $room) }}" class="btn btn-outline-secondary btn-sm">Edit</a>
                                <a href="{{ route('studio-rooms.schedule', $room) }}" class="btn btn-outline-info btn-sm">Schedule</a>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteRoom({{ $room->id }})">Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
                @empty
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-microphone fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No studio rooms found</h4>
                        <p class="text-muted">Create your first studio room to get started.</p>
                        <a href="{{ route('studio-rooms.create') }}" class="btn btn-primary">Add New Room</a>
                    </div>
                </div>
                @endforelse
            </div>

            <!-- Pagination -->
            @if($rooms->hasPages())
            <div class="d-flex justify-content-center">
                {{ $rooms->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Studio Room</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this studio room? This action cannot be undone.</p>
                <p class="text-danger"><strong>Warning:</strong> Rooms with future bookings cannot be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete Room</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteRoom(roomId) {
    const form = document.getElementById('deleteForm');
    form.action = `/studio-rooms/${roomId}`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type');
    const capacityInput = document.getElementById('capacity');
    
    typeSelect.addEventListener('change', function() {
        this.form.submit();
    });
});
</script>
@endpush
