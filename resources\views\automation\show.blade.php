@extends('layouts.app')

@section('title', 'Automation Details - ' . $workflow->name)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ $workflow->name }}</h1>
                    <p class="mt-1 text-sm text-gray-600">Automation workflow details and execution history</p>
                </div>
                <div class="flex space-x-3">
                    <button onclick="toggleWorkflow()" class="bg-{{ $workflow->is_active ? 'red' : 'green' }}-600 hover:bg-{{ $workflow->is_active ? 'red' : 'green' }}-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        {{ $workflow->is_active ? 'Deactivate' : 'Activate' }}
                    </button>
                    <a href="{{ route('automation.edit', $workflow) }}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Edit Workflow
                    </a>
                    <a href="{{ route('automation.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        Back to Workflows
                    </a>
                </div>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Workflow Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Info -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Workflow Information</h3>
                    </div>
                    <div class="p-6">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Name</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $workflow->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Trigger Type</dt>
                                <dd class="mt-1 text-sm text-gray-900">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ ucfirst(str_replace('_', ' ', $workflow->trigger_type)) }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Status</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $workflow->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $workflow->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created by</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $workflow->creator->first_name ?? 'Unknown' }} {{ $workflow->creator->last_name ?? '' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Created</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $workflow->created_at->format('M j, Y g:i A') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $workflow->updated_at->format('M j, Y g:i A') }}</dd>
                            </div>
                        </dl>
                        
                        @if($workflow->description)
                            <div class="mt-6">
                                <dt class="text-sm font-medium text-gray-500">Description</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $workflow->description }}</dd>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Trigger Conditions -->
                @if($workflow->trigger_conditions && count($workflow->trigger_conditions) > 0)
                    <div class="bg-white rounded-lg shadow">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-medium text-gray-900">Trigger Conditions</h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-2">
                                @foreach($workflow->trigger_conditions as $key => $value)
                                    <div class="flex justify-between">
                                        <span class="text-sm font-medium text-gray-700">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                        <span class="text-sm text-gray-900">{{ $value }}</span>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Actions -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Actions ({{ count($workflow->actions ?? []) }})</h3>
                    </div>
                    <div class="p-6">
                        @if($workflow->actions && count($workflow->actions) > 0)
                            <div class="space-y-4">
                                @foreach($workflow->actions as $index => $action)
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <h4 class="text-sm font-medium text-gray-900">Action {{ $index + 1 }}</h4>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                                {{ ucfirst(str_replace('_', ' ', $action['type'])) }}
                                            </span>
                                        </div>
                                        
                                        @if(isset($action['config']) && count($action['config']) > 0)
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                                @foreach($action['config'] as $key => $value)
                                                    <div>
                                                        <span class="font-medium text-gray-700">{{ ucfirst(str_replace('_', ' ', $key)) }}:</span>
                                                        <span class="text-gray-900 ml-2">
                                                            @if(is_array($value))
                                                                {{ implode(', ', $value) }}
                                                            @else
                                                                {{ $value }}
                                                            @endif
                                                        </span>
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-cogs text-gray-400 text-3xl mb-4"></i>
                                <p class="text-gray-500">No actions configured for this workflow</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                    </div>
                    <div class="p-6 space-y-3">
                        <button onclick="executeWorkflow()" class="w-full bg-green-100 hover:bg-green-200 text-green-700 px-4 py-2 rounded-md text-sm font-medium">
                            Test Execute
                        </button>
                        
                        <button onclick="toggleWorkflow()" class="w-full bg-{{ $workflow->is_active ? 'red' : 'green' }}-100 hover:bg-{{ $workflow->is_active ? 'red' : 'green' }}-200 text-{{ $workflow->is_active ? 'red' : 'green' }}-700 px-4 py-2 rounded-md text-sm font-medium">
                            {{ $workflow->is_active ? 'Deactivate' : 'Activate' }}
                        </button>
                        
                        <a href="{{ route('automation.edit', $workflow) }}" class="w-full bg-blue-100 hover:bg-blue-200 text-blue-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Edit Workflow
                        </a>
                        
                        <button onclick="deleteWorkflow()" class="w-full bg-red-100 hover:bg-red-200 text-red-700 px-4 py-2 rounded-md text-sm font-medium">
                            Delete Workflow
                        </button>
                        
                        <a href="{{ route('automation.index') }}" class="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium text-center block">
                            Back to Workflows
                        </a>
                    </div>
                </div>

                <!-- Execution History -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Recent Executions</h3>
                    </div>
                    <div class="p-6">
                        @if($workflow->executions && $workflow->executions->count() > 0)
                            <div class="space-y-3">
                                @foreach($workflow->executions as $execution)
                                    <div class="flex items-center justify-between text-sm">
                                        <div>
                                            <p class="font-medium text-gray-900">{{ $execution->status }}</p>
                                            <p class="text-gray-500">{{ $execution->created_at->format('M j, g:i A') }}</p>
                                        </div>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $execution->status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($execution->status) }}
                                        </span>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-history text-gray-400 text-2xl mb-2"></i>
                                <p class="text-sm text-gray-500">No executions yet</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Statistics -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">Statistics</h3>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Executions:</span>
                            <span class="text-sm font-medium text-gray-900">{{ $workflow->executions->count() ?? 0 }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Success Rate:</span>
                            <span class="text-sm font-medium text-gray-900">
                                @if($workflow->executions && $workflow->executions->count() > 0)
                                    {{ round(($workflow->executions->where('status', 'success')->count() / $workflow->executions->count()) * 100) }}%
                                @else
                                    N/A
                                @endif
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Last Execution:</span>
                            <span class="text-sm font-medium text-gray-900">
                                @if($workflow->executions && $workflow->executions->count() > 0)
                                    {{ $workflow->executions->first()->created_at->diffForHumans() }}
                                @else
                                    Never
                                @endif
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Workflow</h3>
        <p class="text-sm text-gray-600 mb-6">
            Are you sure you want to delete this automation workflow? This action cannot be undone.
        </p>
        <div class="flex justify-end space-x-3">
            <button onclick="closeDeleteModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium">
                Cancel
            </button>
            <form action="{{ route('automation.destroy', $workflow) }}" method="POST" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    function toggleWorkflow() {
        fetch(`{{ route('automation.toggle', $workflow) }}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                showAlert('Error: ' + data.message, 'Error', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while toggling the workflow', 'Error', 'error');
        });
    }

    function executeWorkflow() {
        const testData = { test: true, timestamp: new Date().toISOString() };
        
        fetch(`{{ route('automation.execute', $workflow) }}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ trigger_data: testData })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Workflow executed successfully!', 'Success', 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showAlert('Error: ' + data.message, 'Error', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while executing the workflow', 'Error', 'error');
        });
    }

    function deleteWorkflow() {
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.add('flex');
    }

    function closeDeleteModal() {
        document.getElementById('deleteModal').classList.add('hidden');
        document.getElementById('deleteModal').classList.remove('flex');
    }
</script>
@endpush
