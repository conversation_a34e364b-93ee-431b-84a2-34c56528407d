@extends('layouts.app')

@section('title', 'Edit Project')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Edit Project: {{ $project->name }}</h1>
                <div class="btn-group">
                    <a href="{{ route('projects.show', $project) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Project
                    </a>
                    <a href="{{ route('projects.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> All Projects
                    </a>
                </div>
            </div>

            <div class="card shadow">
                <div class="card-body">
                    <form action="{{ route('projects.update', $project) }}" method="POST" id="projectForm">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Project Details</h5>
                                
                                <div class="mb-3">
                                    <label for="name" class="form-label">Project Name <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $project->name) }}" required>
                                    @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="client_id" class="form-label">Client <span class="text-danger">*</span></label>
                                    <select class="form-select @error('client_id') is-invalid @enderror" id="client_id" name="client_id" required>
                                        <option value="">Select Client</option>
                                        @foreach($clients as $client)
                                        <option value="{{ $client->id }}" {{ old('client_id', $project->client_id) == $client->id ? 'selected' : '' }}>
                                            {{ $client->name }}
                                        </option>
                                        @endforeach
                                    </select>
                                    @error('client_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">Description</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="4" 
                                              placeholder="Describe the project goals, requirements, and deliverables">{{ old('description', $project->description) }}</textarea>
                                    @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="start_date" class="form-label">Start Date</label>
                                            <input type="date" class="form-control @error('start_date') is-invalid @enderror" 
                                                   id="start_date" name="start_date" value="{{ old('start_date', $project->start_date?->format('Y-m-d')) }}">
                                            @error('start_date')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="deadline" class="form-label">Deadline</label>
                                            <input type="date" class="form-control @error('deadline') is-invalid @enderror" 
                                                   id="deadline" name="deadline" value="{{ old('deadline', $project->deadline?->format('Y-m-d')) }}">
                                            @error('deadline')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="budget" class="form-label">Budget ($)</label>
                                            <input type="number" class="form-control @error('budget') is-invalid @enderror" 
                                                   id="budget" name="budget" value="{{ old('budget', $project->budget) }}" 
                                                   min="0" step="0.01">
                                            @error('budget')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="priority" class="form-label">Priority</label>
                                            <select class="form-select @error('priority') is-invalid @enderror" id="priority" name="priority">
                                                <option value="low" {{ old('priority', $project->priority) == 'low' ? 'selected' : '' }}>Low</option>
                                                <option value="medium" {{ old('priority', $project->priority) == 'medium' ? 'selected' : '' }}>Medium</option>
                                                <option value="high" {{ old('priority', $project->priority) == 'high' ? 'selected' : '' }}>High</option>
                                                <option value="urgent" {{ old('priority', $project->priority) == 'urgent' ? 'selected' : '' }}>Urgent</option>
                                            </select>
                                            @error('priority')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select @error('status') is-invalid @enderror" id="status" name="status">
                                        <option value="planning" {{ old('status', $project->status) == 'planning' ? 'selected' : '' }}>Planning</option>
                                        <option value="in_progress" {{ old('status', $project->status) == 'in_progress' ? 'selected' : '' }}>In Progress</option>
                                        <option value="on_hold" {{ old('status', $project->status) == 'on_hold' ? 'selected' : '' }}>On Hold</option>
                                        <option value="completed" {{ old('status', $project->status) == 'completed' ? 'selected' : '' }}>Completed</option>
                                        <option value="cancelled" {{ old('status', $project->status) == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    </select>
                                    @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Team & Settings -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Team & Settings</h5>
                                
                                <div class="mb-3">
                                    <label for="project_manager_id" class="form-label">Project Manager</label>
                                    <select class="form-select @error('project_manager_id') is-invalid @enderror" id="project_manager_id" name="project_manager_id">
                                        <option value="">Select Project Manager</option>
                                        @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ old('project_manager_id', $project->project_manager_id) == $user->id ? 'selected' : '' }}>
                                            {{ $user->name }}
                                        </option>
                                        @endforeach
                                    </select>
                                    @error('project_manager_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="tags" class="form-label">Tags</label>
                                    <input type="text" class="form-control @error('tags') is-invalid @enderror" 
                                           id="tags" name="tags" value="{{ old('tags', $project->tags) }}" 
                                           placeholder="Enter tags separated by commas">
                                    <div class="form-text">e.g., music video, commercial, post-production</div>
                                    @error('tags')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="requirements" class="form-label">Requirements & Deliverables</label>
                                    <textarea class="form-control @error('requirements') is-invalid @enderror" 
                                              id="requirements" name="requirements" rows="4" 
                                              placeholder="List specific requirements, deliverables, and milestones">{{ old('requirements', $project->requirements) }}</textarea>
                                    @error('requirements')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Project Settings</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_billable" 
                                               name="is_billable" value="1" {{ old('is_billable', $project->is_billable) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_billable">
                                            Billable Project
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="requires_approval" 
                                               name="requires_approval" value="1" {{ old('requires_approval', $project->requires_approval) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="requires_approval">
                                            Requires Client Approval
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_confidential" 
                                               name="is_confidential" value="1" {{ old('is_confidential', $project->is_confidential) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_confidential">
                                            Confidential Project
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Internal Notes</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" 
                                              id="notes" name="notes" rows="3" 
                                              placeholder="Internal notes not visible to client">{{ old('notes', $project->notes) }}</textarea>
                                    @error('notes')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Progress Update -->
                                <div class="mb-3">
                                    <label for="progress" class="form-label">Progress (%)</label>
                                    <input type="range" class="form-range" id="progress" name="progress" 
                                           min="0" max="100" value="{{ old('progress', $project->progress ?? 0) }}" 
                                           oninput="updateProgressValue(this.value)">
                                    <div class="d-flex justify-content-between">
                                        <span>0%</span>
                                        <span id="progress-value">{{ old('progress', $project->progress ?? 0) }}%</span>
                                        <span>100%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('projects.show', $project) }}" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Update Project</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function updateProgressValue(value) {
    document.getElementById('progress-value').textContent = value + '%';
}

// Form validation
document.getElementById('projectForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const clientId = document.getElementById('client_id').value;
    
    if (!name || !clientId) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    // Validate dates
    const startDate = document.getElementById('start_date').value;
    const deadline = document.getElementById('deadline').value;
    
    if (startDate && deadline && new Date(startDate) > new Date(deadline)) {
        e.preventDefault();
        alert('Start date cannot be after the deadline.');
        return false;
    }
});

// Budget formatting
document.getElementById('budget').addEventListener('blur', function() {
    if (this.value) {
        const value = parseFloat(this.value);
        if (!isNaN(value)) {
            this.value = value.toFixed(2);
        }
    }
});
</script>
@endpush
