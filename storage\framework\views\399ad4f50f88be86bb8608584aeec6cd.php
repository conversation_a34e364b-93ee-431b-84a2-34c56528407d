<?php $__env->startSection('title', 'Accounting Dashboard'); ?>

<?php $__env->startSection('dashboard-content'); ?>
<!-- Header -->
<div class="flex justify-between items-center mb-6">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Accounting Dashboard</h1>
                        <p class="text-gray-600">Manage invoices, payments, and financial records</p>
                    </div>
                    <div class="flex space-x-3">
                        <a href="<?php echo e(route('accounting.invoices.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            New Invoice
                        </a>
                        <a href="<?php echo e(route('accounting.expenses.create')); ?>" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            Record Expense
                        </a>
                    </div>
                </div>

                <!-- Key Metrics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <!-- Accounts Receivable -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Accounts Receivable</p>
                                    <p class="text-2xl font-semibold text-gray-900">
                                        ₦<?php echo e(number_format($data['accounts_receivable']['total'] ?? 0, 2)); ?>

                                    </p>
                                    <p class="text-sm text-red-600">
                                        ₦<?php echo e(number_format($data['accounts_receivable']['overdue'] ?? 0, 2)); ?> overdue
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Accounts Payable -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Accounts Payable</p>
                                    <p class="text-2xl font-semibold text-gray-900">
                                        ₦<?php echo e(number_format($data['accounts_payable']['total'] ?? 0, 2)); ?>

                                    </p>
                                    <p class="text-sm text-gray-500">
                                        ₦<?php echo e(number_format($data['accounts_payable']['current'] ?? 0, 2)); ?> current
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Position -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 <?php echo e(($data['cash_position']['balance'] ?? 0) >= 0 ? 'bg-green-100' : 'bg-red-100'); ?> rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 <?php echo e(($data['cash_position']['balance'] ?? 0) >= 0 ? 'text-green-600' : 'text-red-600'); ?>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Cash Position</p>
                                    <p class="text-2xl font-semibold <?php echo e(($data['cash_position']['balance'] ?? 0) >= 0 ? 'text-gray-900' : 'text-red-600'); ?>">
                                        ₦<?php echo e(number_format($data['cash_position']['balance'] ?? 0, 2)); ?>

                                    </p>
                                    <p class="text-sm text-gray-500">
                                        ₦<?php echo e(number_format($data['cash_position']['monthly_inflow'] ?? 0, 2)); ?> inflow
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monthly Summary -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-600">Monthly Revenue</p>
                                    <p class="text-2xl font-semibold text-gray-900">
                                        ₦<?php echo e(number_format($data['monthly_summary']['revenue'] ?? 0, 2)); ?>

                                    </p>
                                    <p class="text-sm text-gray-500">
                                        <?php echo e($data['monthly_summary']['invoices_sent'] ?? 0); ?> invoices sent
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions & Overdue Invoices -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <!-- Quick Actions -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <button onclick="openCreateInvoiceModal()" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                    <svg class="w-8 h-8 text-blue-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm font-medium">Create Invoice</span>
                                </button>
                                <button onclick="openRecordExpenseModal()" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                    <svg class="w-8 h-8 text-green-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                    </svg>
                                    <span class="text-sm font-medium">Record Expense</span>
                                </button>
                                <button onclick="openRecordPaymentModal()" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                    <svg class="w-8 h-8 text-purple-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                    <span class="text-sm font-medium">Record Payment</span>
                                </button>
                                <button onclick="openViewReportsModal()" class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                                    <svg class="w-8 h-8 text-orange-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                    <span class="text-sm font-medium">View Reports</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Overdue Invoices -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">Overdue Invoices</h3>
                                <a href="<?php echo e(route('accounting.invoices.index', ['overdue' => true])); ?>" class="text-blue-600 hover:text-blue-800 text-sm">
                                    View All
                                </a>
                            </div>
                            <div class="space-y-3">
                                <?php $__empty_1 = true; $__currentLoopData = $data['overdue_invoices'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $invoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                        <div class="flex-1">
                                            <p class="text-sm font-medium text-gray-900"><?php echo e($invoice['invoice_number']); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo e($invoice['client']['name']); ?></p>
                                            <p class="text-xs text-red-600">Due: <?php echo e(\Carbon\Carbon::parse($invoice['due_date'])->format('M j, Y')); ?></p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-semibold text-gray-900">₦<?php echo e(number_format($invoice['total_amount'], 2)); ?></p>
                                            <p class="text-xs text-red-600"><?php echo e(\Carbon\Carbon::parse($invoice['due_date'])->diffForHumans()); ?></p>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <div class="text-center py-8">
                                        <svg class="w-12 h-12 text-green-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-gray-500">No overdue invoices</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Recent Transactions</h3>
                            <a href="<?php echo e(route('accounting.transactions.index')); ?>" class="text-blue-600 hover:text-blue-800 text-sm">
                                View All
                            </a>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__empty_1 = true; $__currentLoopData = $data['recent_transactions'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e(\Carbon\Carbon::parse($transaction['date'])->format('M j, Y')); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 py-1 text-xs rounded-full <?php echo e($transaction['type'] === 'payment' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                                    <?php echo e(ucfirst($transaction['type'])); ?>

                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-900"><?php echo e($transaction['description']); ?></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium <?php echo e($transaction['amount'] >= 0 ? 'text-green-600' : 'text-red-600'); ?>">
                                                <?php echo e($transaction['amount'] >= 0 ? '+' : ''); ?>₦<?php echo e(number_format(abs($transaction['amount']), 2)); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?php echo e($transaction['reference'] ?? '-'); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <tr>
                                            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No recent transactions</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Tax Summary & Budget vs Actual -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Tax Summary -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Tax Summary (Q<?php echo e($data['tax_summary']['quarter'] ?? 1); ?>)</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Tax Collected</span>
                                    <span class="font-medium">₦<?php echo e(number_format($data['tax_summary']['tax_collected'] ?? 0, 2)); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">Tax Paid</span>
                                    <span class="font-medium">₦<?php echo e(number_format($data['tax_summary']['tax_paid'] ?? 0, 2)); ?></span>
                                </div>
                                <div class="border-t pt-2">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm font-medium text-gray-900">Net Tax Liability</span>
                                        <span class="font-semibold">₦<?php echo e(number_format($data['tax_summary']['net_tax_liability'] ?? 0, 2)); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Budget vs Actual -->
                    <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div class="p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Budget vs Actual</h3>
                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-sm text-gray-600">Revenue</span>
                                        <span class="text-sm font-medium">
                                            ₦<?php echo e(number_format($data['budget_vs_actual']['revenue_actual'] ?? 0, 2)); ?> / ₦<?php echo e(number_format($data['budget_vs_actual']['revenue_budget'] ?? 0, 2)); ?>

                                        </span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: <?php echo e(min(100, (($data['budget_vs_actual']['revenue_actual'] ?? 0) / max(1, $data['budget_vs_actual']['revenue_budget'] ?? 1)) * 100)); ?>%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-sm text-gray-600">Expenses</span>
                                        <span class="text-sm font-medium">
                                            ₦<?php echo e(number_format($data['budget_vs_actual']['expense_actual'] ?? 0, 2)); ?> / ₦<?php echo e(number_format($data['budget_vs_actual']['expense_budget'] ?? 0, 2)); ?>

                                        </span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-red-500 h-2 rounded-full" style="width: <?php echo e(min(100, (($data['budget_vs_actual']['expense_actual'] ?? 0) / max(1, $data['budget_vs_actual']['expense_budget'] ?? 1)) * 100)); ?>%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<!-- Create Invoice Modal -->
<div id="createInvoiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Create Invoice</h3>
            <button onclick="closeCreateInvoiceModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="invoiceFormContainer">
            <!-- Invoice form will be loaded here -->
        </div>
    </div>
</div>

<!-- Record Expense Modal -->
<div id="recordExpenseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Record Expense</h3>
            <button onclick="closeRecordExpenseModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="expenseFormContainer">
            <!-- Expense form will be loaded here -->
        </div>
    </div>
</div>

<!-- Record Payment Modal -->
<div id="recordPaymentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Record Payment</h3>
            <button onclick="closeRecordPaymentModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="paymentFormContainer">
            <!-- Payment form will be loaded here -->
        </div>
    </div>
</div>

<!-- View Reports Modal -->
<div id="viewReportsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Financial Reports</h3>
            <button onclick="closeViewReportsModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="reportsContainer">
            <!-- Reports will be loaded here -->
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Modal functions
function openCreateInvoiceModal() {
    loadInvoiceForm();
    $('#createInvoiceModal').removeClass('hidden');
}

function openRecordExpenseModal() {
    loadExpenseForm();
    $('#recordExpenseModal').removeClass('hidden');
}

function openRecordPaymentModal() {
    loadPaymentForm();
    $('#recordPaymentModal').removeClass('hidden');
}

function openViewReportsModal() {
    loadReportsView();
    $('#viewReportsModal').removeClass('hidden');
}

function closeCreateInvoiceModal() {
    $('#createInvoiceModal').addClass('hidden');
}

function closeRecordExpenseModal() {
    $('#recordExpenseModal').addClass('hidden');
}

function closeRecordPaymentModal() {
    $('#recordPaymentModal').addClass('hidden');
}

function closeViewReportsModal() {
    $('#viewReportsModal').addClass('hidden');
}

function loadInvoiceForm() {
    const formHtml = `
        <form id="createInvoiceForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Client</label>
                    <select name="client_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        <option value="">Select Client</option>
                        <!-- Options will be loaded via AJAX -->
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Invoice Date</label>
                    <input type="date" name="invoice_date" value="${new Date().toISOString().split('T')[0]}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Due Date</label>
                    <input type="date" name="due_date" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Project (Optional)</label>
                    <select name="project_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                        <option value="">Select Project</option>
                        <!-- Options will be loaded via AJAX -->
                    </select>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Description</label>
                <textarea name="description" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" placeholder="Invoice description..."></textarea>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Amount</label>
                    <input type="number" name="amount" step="0.01" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Tax Rate (%)</label>
                    <input type="number" name="tax_rate" step="0.01" value="7.5" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Total Amount</label>
                    <input type="number" name="total_amount" step="0.01" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50" readonly>
                </div>
            </div>
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeCreateInvoiceModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">Create Invoice</button>
            </div>
        </form>
    `;
    $('#invoiceFormContainer').html(formHtml);

    // Load clients and projects
    loadClientsForSelect();
    loadProjectsForSelect();

    // Handle form submission
    $('#createInvoiceForm').on('submit', function(e) {
        e.preventDefault();
        submitInvoiceForm(new FormData(this));
    });
}

function loadExpenseForm() {
    const formHtml = `
        <form id="recordExpenseForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    <input type="text" name="description" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Amount</label>
                    <input type="number" name="amount" step="0.01" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Category</label>
                    <select name="category_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        <option value="">Select Category</option>
                        <!-- Options will be loaded via AJAX -->
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Expense Date</label>
                    <input type="date" name="expense_date" value="${new Date().toISOString().split('T')[0]}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Vendor</label>
                    <input type="text" name="vendor" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Receipt Number</label>
                    <input type="text" name="receipt_number" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Notes</label>
                <textarea name="notes" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" placeholder="Additional notes..."></textarea>
            </div>
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeRecordExpenseModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg">Record Expense</button>
            </div>
        </form>
    `;
    $('#expenseFormContainer').html(formHtml);

    // Load expense categories
    loadExpenseCategoriesForSelect();

    // Handle form submission
    $('#recordExpenseForm').on('submit', function(e) {
        e.preventDefault();
        submitExpenseForm(new FormData(this));
    });
}

function loadPaymentForm() {
    const formHtml = `
        <form id="recordPaymentForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Invoice</label>
                    <select name="invoice_id" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        <option value="">Select Invoice</option>
                        <!-- Options will be loaded via AJAX -->
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Payment Amount</label>
                    <input type="number" name="amount" step="0.01" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Payment Date</label>
                    <input type="date" name="payment_date" value="${new Date().toISOString().split('T')[0]}" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Payment Method</label>
                    <select name="payment_method" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                        <option value="">Select Method</option>
                        <option value="cash">Cash</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="check">Check</option>
                        <option value="card">Card</option>
                    </select>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Reference Number</label>
                <input type="text" name="reference_number" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Notes</label>
                <textarea name="notes" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" placeholder="Payment notes..."></textarea>
            </div>
            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeRecordPaymentModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
                <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg">Record Payment</button>
            </div>
        </form>
    `;
    $('#paymentFormContainer').html(formHtml);

    // Load outstanding invoices
    loadOutstandingInvoicesForSelect();

    // Handle form submission
    $('#recordPaymentForm').on('submit', function(e) {
        e.preventDefault();
        submitPaymentForm(new FormData(this));
    });
}

function loadReportsView() {
    const reportsHtml = `
        <div class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button onclick="generateReport('profit-loss')" class="p-6 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                    <i class="fas fa-chart-line text-blue-600 text-3xl mb-3"></i>
                    <h4 class="font-medium text-gray-900">Profit & Loss</h4>
                    <p class="text-sm text-gray-600 mt-1">Income and expense summary</p>
                </button>
                <button onclick="generateReport('balance-sheet')" class="p-6 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                    <i class="fas fa-balance-scale text-green-600 text-3xl mb-3"></i>
                    <h4 class="font-medium text-gray-900">Balance Sheet</h4>
                    <p class="text-sm text-gray-600 mt-1">Assets, liabilities & equity</p>
                </button>
                <button onclick="generateReport('cash-flow')" class="p-6 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                    <i class="fas fa-money-bill-wave text-purple-600 text-3xl mb-3"></i>
                    <h4 class="font-medium text-gray-900">Cash Flow</h4>
                    <p class="text-sm text-gray-600 mt-1">Cash in and out analysis</p>
                </button>
                <button onclick="generateReport('accounts-receivable')" class="p-6 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                    <i class="fas fa-file-invoice-dollar text-orange-600 text-3xl mb-3"></i>
                    <h4 class="font-medium text-gray-900">Accounts Receivable</h4>
                    <p class="text-sm text-gray-600 mt-1">Outstanding invoices</p>
                </button>
                <button onclick="generateReport('expense-summary')" class="p-6 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                    <i class="fas fa-receipt text-red-600 text-3xl mb-3"></i>
                    <h4 class="font-medium text-gray-900">Expense Summary</h4>
                    <p class="text-sm text-gray-600 mt-1">Expense breakdown by category</p>
                </button>
                <button onclick="generateReport('tax-summary')" class="p-6 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                    <i class="fas fa-calculator text-indigo-600 text-3xl mb-3"></i>
                    <h4 class="font-medium text-gray-900">Tax Summary</h4>
                    <p class="text-sm text-gray-600 mt-1">Tax collected and paid</p>
                </button>
            </div>
            <div id="reportOutput" class="hidden">
                <!-- Generated report will appear here -->
            </div>
        </div>
    `;
    $('#reportsContainer').html(reportsHtml);
}

// Helper functions for loading data
function loadClientsForSelect() {
    $.ajax({
        url: '/api/clients',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            const select = $('select[name="client_id"]');
            response.data.forEach(client => {
                select.append(`<option value="${client.id}">${client.name}</option>`);
            });
        }
    });
}

function loadProjectsForSelect() {
    $.ajax({
        url: '/api/projects',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            const select = $('select[name="project_id"]');
            response.data.forEach(project => {
                select.append(`<option value="${project.id}">${project.name}</option>`);
            });
        }
    });
}

function loadExpenseCategoriesForSelect() {
    $.ajax({
        url: '/api/expense-categories',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            const select = $('select[name="category_id"]');
            response.data.forEach(category => {
                select.append(`<option value="${category.id}">${category.name}</option>`);
            });
        }
    });
}

function loadOutstandingInvoicesForSelect() {
    $.ajax({
        url: '/api/invoices?status=outstanding',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            const select = $('select[name="invoice_id"]');
            response.data.forEach(invoice => {
                select.append(`<option value="${invoice.id}">${invoice.invoice_number} - ₦${invoice.total_amount} (${invoice.client.name})</option>`);
            });
        }
    });
}

// Form submission functions
function submitInvoiceForm(formData) {
    $.ajax({
        url: '/api/accounting/invoices',
        method: 'POST',
        data: Object.fromEntries(formData),
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            closeCreateInvoiceModal();
            showNotification('Invoice created successfully', 'success');
            location.reload();
        } else {
            showNotification('Error creating invoice: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Create invoice error:', xhr);
        showNotification('Failed to create invoice', 'error');
    });
}

function submitExpenseForm(formData) {
    $.ajax({
        url: '/api/accounting/expenses',
        method: 'POST',
        data: Object.fromEntries(formData),
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            closeRecordExpenseModal();
            showNotification('Expense recorded successfully', 'success');
            location.reload();
        } else {
            showNotification('Error recording expense: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Record expense error:', xhr);
        showNotification('Failed to record expense', 'error');
    });
}

function submitPaymentForm(formData) {
    $.ajax({
        url: '/api/accounting/payments',
        method: 'POST',
        data: Object.fromEntries(formData),
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            closeRecordPaymentModal();
            showNotification('Payment recorded successfully', 'success');
            location.reload();
        } else {
            showNotification('Error recording payment: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Record payment error:', xhr);
        showNotification('Failed to record payment', 'error');
    });
}

function generateReport(reportType) {
    $.ajax({
        url: `/api/accounting/reports/${reportType}`,
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            displayReport(response.data, reportType);
        } else {
            showNotification('Error generating report: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Generate report error:', xhr);
        showNotification('Failed to generate report', 'error');
    });
}

function displayReport(data, reportType) {
    // This would display the report data in a formatted way
    const reportOutput = $('#reportOutput');
    reportOutput.html(`<div class="p-4 bg-gray-50 rounded-lg"><h4 class="font-medium mb-2">${reportType.replace('-', ' ').toUpperCase()} Report</h4><pre>${JSON.stringify(data, null, 2)}</pre></div>`);
    reportOutput.removeClass('hidden');
}

function showNotification(message, type) {
    // Simple notification function
    alert(message);
}

    document.addEventListener('DOMContentLoaded', function() {
        // Auto-refresh dashboard every 5 minutes
        setInterval(function() {
            if (document.visibilityState === 'visible') {
                window.location.reload();
            }
        }, 300000);
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\workspace\.php\glitchafrica\resources\views/accounting/dashboard.blade.php ENDPATH**/ ?>