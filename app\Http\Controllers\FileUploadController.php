<?php

namespace App\Http\Controllers;

use App\Models\FileUpload;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class FileUploadController extends Controller
{
    public function upload(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:512000', // 500MB max
            'uploadable_type' => 'required|string',
            'uploadable_id' => 'required|integer',
            'type' => 'nullable|in:audio,video,image,document,other'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $file = $request->file('file');
        $uploadableType = $request->uploadable_type;
        $uploadableId = $request->uploadable_id;

        // validate uploadable entity exists
        if (!class_exists($uploadableType) || !$uploadableType::find($uploadableId)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid uploadable entity'
            ], 422);
        }

        // determine file type
        $mimeType = $file->getMimeType();
        $fileType = $this->determineFileType($mimeType);

        // generate unique filename
        $originalName = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $filename = Str::uuid() . '.' . $extension;

        // determine storage path
        $path = $this->getStoragePath($fileType, $uploadableType);
        $fullPath = $path . '/' . $filename;

        // store file
        $disk = config('filesystems.default');
        $file->storeAs($path, $filename, $disk);

        // create file record
        $fileUpload = FileUpload::create([
            'filename' => $filename,
            'original_name' => $originalName,
            'path' => $fullPath,
            'disk' => $disk,
            'mime_type' => $mimeType,
            'size' => $file->getSize(),
            'uploadable_type' => $uploadableType,
            'uploadable_id' => $uploadableId,
            'uploaded_by' => $request->user()->id,
            'type' => $fileType,
            'metadata' => $this->extractMetadata($file, $fileType)
        ]);

        // track upload event
        AnalyticsEvent::track('file_uploaded', 'file', $fileUpload, [
            'file_type' => $fileType,
            'file_size' => $file->getSize(),
            'uploadable_type' => $uploadableType
        ]);

        return response()->json([
            'success' => true,
            'message' => 'File uploaded successfully',
            'data' => ['file' => $fileUpload]
        ], 201);
    }

    public function download(Request $request, FileUpload $fileUpload)
    {
        $user = $request->user();

        // check if user can access this file
        if (!$this->canUserAccessFile($user, $fileUpload)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if (!Storage::disk($fileUpload->disk)->exists($fileUpload->path)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        // track download event
        AnalyticsEvent::track('file_downloaded', 'file', $fileUpload);

        return Storage::disk($fileUpload->disk)->download(
            $fileUpload->path,
            $fileUpload->original_name
        );
    }

    public function stream(Request $request, FileUpload $fileUpload)
    {
        $user = $request->user();

        if (!$this->canUserAccessFile($user, $fileUpload)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        if (!$fileUpload->isAudio() && !$fileUpload->isVideo()) {
            return response()->json([
                'success' => false,
                'message' => 'File is not streamable'
            ], 422);
        }

        if (!Storage::disk($fileUpload->disk)->exists($fileUpload->path)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        $filePath = Storage::disk($fileUpload->disk)->path($fileUpload->path);
        $fileSize = filesize($filePath);
        $start = 0;
        $end = $fileSize - 1;

        // handle range requests for streaming
        if ($request->hasHeader('Range')) {
            $range = $request->header('Range');
            if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
                $start = intval($matches[1]);
                if (!empty($matches[2])) {
                    $end = intval($matches[2]);
                }
            }
        }

        $length = $end - $start + 1;

        return response()->stream(function () use ($filePath, $start, $length) {
            $file = fopen($filePath, 'rb');
            fseek($file, $start);
            echo fread($file, $length);
            fclose($file);
        }, 206, [
            'Content-Type' => $fileUpload->mime_type,
            'Content-Length' => $length,
            'Content-Range' => "bytes $start-$end/$fileSize",
            'Accept-Ranges' => 'bytes',
        ]);
    }

    public function delete(Request $request, FileUpload $fileUpload)
    {
        $user = $request->user();

        if (!$this->canUserDeleteFile($user, $fileUpload)) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        // track deletion event
        AnalyticsEvent::track('file_deleted', 'file', $fileUpload);

        $fileUpload->delete(); // This will also delete the physical file

        return response()->json([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }

    public function list(Request $request)
    {
        $user = $request->user();
        
        $query = FileUpload::with(['uploadedBy', 'uploadable'])
            ->orderBy('created_at', 'desc');

        // filter by uploadable entity
        if ($request->has('uploadable_type') && $request->has('uploadable_id')) {
            $query->where('uploadable_type', $request->uploadable_type)
                  ->where('uploadable_id', $request->uploadable_id);
        }

        // filter by file type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // filter by user's accessible files if not admin
        if (!$user->hasPermission('*')) {
            $query->where(function ($q) use ($user) {
                $q->where('uploaded_by', $user->id)
                  ->orWhereHasMorph('uploadable', ['App\Models\Project'], function ($projectQuery) use ($user) {
                      $projectQuery->where('project_manager_id', $user->id)
                               ->orWhereHas('teamMembers', function ($teamQuery) use ($user) {
                                   $teamQuery->where('user_id', $user->id);
                               });
                  });
            });
        }

        $files = $query->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => [
                'files' => $files->items(),
                'pagination' => [
                    'current_page' => $files->currentPage(),
                    'last_page' => $files->lastPage(),
                    'per_page' => $files->perPage(),
                    'total' => $files->total()
                ]
            ]
        ]);
    }

    public function createVersion(Request $request, FileUpload $fileUpload)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:512000',
            'version_name' => 'required|string|max:100',
            'description' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $file = $request->file('file');
        $versionName = $request->version_name;

        // generate unique filename for version
        $extension = $file->getClientOriginalExtension();
        $filename = Str::uuid() . '.' . $extension;
        $path = dirname($fileUpload->path) . '/versions/' . $filename;

        // store version file
        $file->storeAs(dirname($path), basename($path), $fileUpload->disk);

        // add version to file record
        $fileUpload->addVersion($versionName, $path, [
            'description' => $request->description,
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'File version created successfully',
            'data' => ['file' => $fileUpload->fresh()]
        ]);
    }

    private function determineFileType(string $mimeType): string
    {
        if (str_starts_with($mimeType, 'audio/')) {
            return 'audio';
        } elseif (str_starts_with($mimeType, 'video/')) {
            return 'video';
        } elseif (str_starts_with($mimeType, 'image/')) {
            return 'image';
        } elseif (in_array($mimeType, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        ])) {
            return 'document';
        }

        return 'other';
    }

    private function getStoragePath(string $fileType, string $uploadableType): string
    {
        $baseType = class_basename($uploadableType);
        return "uploads/{$baseType}/{$fileType}/" . date('Y/m');
    }

    private function extractMetadata($file, string $fileType): array
    {
        $metadata = [];

        if ($fileType === 'image') {
            $imageInfo = getimagesize($file->getPathname());
            if ($imageInfo) {
                $metadata['width'] = $imageInfo[0];
                $metadata['height'] = $imageInfo[1];
            }
        }

        // Add more metadata extraction for audio/video files if needed
        // This would require additional libraries like getID3

        return $metadata;
    }

    private function canUserAccessFile($user, FileUpload $fileUpload): bool
    {
        // admin can access all files
        if ($user->hasPermission('*')) {
            return true;
        }

        // uploader can access their files
        if ($fileUpload->uploaded_by === $user->id) {
            return true;
        }

        // check if user can access the uploadable entity
        $uploadable = $fileUpload->uploadable;
        
        if ($uploadable instanceof \App\Models\Project) {
            return $user->canAccessProject($uploadable);
        }

        // add more access checks for other uploadable types

        return false;
    }

    private function canUserDeleteFile($user, FileUpload $fileUpload): bool
    {
        // admin can delete all files
        if ($user->hasPermission('*')) {
            return true;
        }

        // uploader can delete their files
        return $fileUpload->uploaded_by === $user->id;
    }
}
