@extends('layouts.app')

@section('title', $client->name)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div class="d-flex align-items-center">
                    <div class="avatar-lg me-3">
                        @if($client->profile_picture)
                        <img src="{{ Storage::url($client->profile_picture) }}" alt="{{ $client->name }}" class="avatar-img rounded-circle">
                        @else
                        <div class="avatar-title rounded-circle bg-primary text-white">
                            {{ substr($client->name, 0, 2) }}
                        </div>
                        @endif
                    </div>
                    <div>
                        <h1 class="h3 mb-0 text-gray-800">{{ $client->name }}</h1>
                        <span class="badge bg-{{ 
                            $client->status == 'active' ? 'success' : 
                            ($client->status == 'vip' ? 'warning' : 'secondary') 
                        }} fs-6 mt-1">
                            {{ ucfirst($client->status ?? 'active') }}
                        </span>
                    </div>
                </div>
                <div class="btn-group">
                    <a href="{{ route('clients.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Clients
                    </a>
                    <a href="{{ route('clients.edit', $client) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Client
                    </a>
                    <div class="btn-group">
                        <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-plus"></i> Quick Actions
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('projects.create', ['client' => $client->id]) }}">
                                <i class="fas fa-project-diagram"></i> Create Project
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('bookings.create', ['client' => $client->id]) }}">
                                <i class="fas fa-calendar-plus"></i> Create Booking
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="mailto:{{ $client->email }}">
                                <i class="fas fa-envelope"></i> Send Email
                            </a></li>
                            @if($client->phone)
                            <li><a class="dropdown-item" href="tel:{{ $client->phone }}">
                                <i class="fas fa-phone"></i> Call Client
                            </a></li>
                            @endif
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Client Details -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Client Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Email:</strong></td>
                                            <td><a href="mailto:{{ $client->email }}">{{ $client->email }}</a></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Phone:</strong></td>
                                            <td>
                                                @if($client->phone)
                                                <a href="tel:{{ $client->phone }}">{{ $client->phone }}</a>
                                                @else
                                                <span class="text-muted">Not provided</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Type:</strong></td>
                                            <td>{{ ucfirst($client->type ?? 'individual') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Company:</strong></td>
                                            <td>{{ $client->company ?? 'N/A' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Website:</strong></td>
                                            <td>
                                                @if($client->website)
                                                <a href="{{ $client->website }}" target="_blank">{{ $client->website }}</a>
                                                @else
                                                <span class="text-muted">Not provided</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Genres:</strong></td>
                                            <td>
                                                @if($client->genres)
                                                @foreach(explode(',', $client->genres) as $genre)
                                                <span class="badge bg-light text-dark me-1">{{ trim($genre) }}</span>
                                                @endforeach
                                                @else
                                                <span class="text-muted">Not specified</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Preferred Contact:</strong></td>
                                            <td>{{ ucfirst($client->preferred_contact_method ?? 'email') }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Member Since:</strong></td>
                                            <td>{{ $client->created_at->format('M d, Y') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            @if($client->address)
                            <div class="mt-3">
                                <h6>Address</h6>
                                <p class="text-muted">{{ $client->address }}</p>
                            </div>
                            @endif

                            @if($client->notes)
                            <div class="mt-3">
                                <h6>Notes</h6>
                                <p class="text-muted">{{ $client->notes }}</p>
                            </div>
                            @endif

                            @if($client->social_media)
                            <div class="mt-3">
                                <h6>Social Media</h6>
                                <div class="d-flex gap-2">
                                    @foreach($client->social_media as $platform => $url)
                                    @if($url)
                                    <a href="{{ $url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-{{ $platform }}"></i> {{ ucfirst($platform) }}
                                    </a>
                                    @endif
                                    @endforeach
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Projects -->
                    <div class="card shadow mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Projects ({{ $client->projects->count() }})</h5>
                            <a href="{{ route('projects.create', ['client' => $client->id]) }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> New Project
                            </a>
                        </div>
                        <div class="card-body">
                            @if($client->projects->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Project</th>
                                            <th>Status</th>
                                            <th>Progress</th>
                                            <th>Budget</th>
                                            <th>Deadline</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($client->projects->take(5) as $project)
                                        <tr>
                                            <td>
                                                <a href="{{ route('projects.show', $project) }}">{{ $project->name }}</a>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 
                                                    $project->status == 'completed' ? 'success' : 
                                                    ($project->status == 'in_progress' ? 'warning' : 'secondary') 
                                                }}">
                                                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 15px;">
                                                    <div class="progress-bar" style="width: {{ $project->progress ?? 0 }}%">
                                                        {{ $project->progress ?? 0 }}%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>${{ number_format($project->budget ?? 0, 2) }}</td>
                                            <td>{{ $project->deadline ? $project->deadline->format('M d, Y') : 'No deadline' }}</td>
                                            <td>
                                                <a href="{{ route('projects.show', $project) }}" class="btn btn-sm btn-outline-primary">View</a>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @else
                            <div class="text-center py-4">
                                <i class="fas fa-project-diagram fa-2x text-muted mb-3"></i>
                                <p class="text-muted">No projects yet</p>
                                <a href="{{ route('projects.create', ['client' => $client->id]) }}" class="btn btn-primary">
                                    Create First Project
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Recent Bookings -->
                    <div class="card shadow mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Recent Bookings</h5>
                            <a href="{{ route('bookings.create', ['client' => $client->id]) }}" class="btn btn-sm btn-success">
                                <i class="fas fa-calendar-plus"></i> New Booking
                            </a>
                        </div>
                        <div class="card-body">
                            @if($client->bookings && $client->bookings->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Room</th>
                                            <th>Time</th>
                                            <th>Status</th>
                                            <th>Cost</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($client->bookings->take(5) as $booking)
                                        <tr>
                                            <td>{{ $booking->start_time->format('M d, Y') }}</td>
                                            <td>{{ $booking->studioRoom->name ?? 'N/A' }}</td>
                                            <td>{{ $booking->start_time->format('g:i A') }} - {{ $booking->end_time->format('g:i A') }}</td>
                                            <td>
                                                <span class="badge bg-{{ 
                                                    $booking->status == 'confirmed' ? 'success' : 
                                                    ($booking->status == 'pending' ? 'warning' : 'secondary') 
                                                }}">
                                                    {{ ucfirst($booking->status) }}
                                                </span>
                                            </td>
                                            <td>${{ number_format($booking->total_cost ?? 0, 2) }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @else
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-2x text-muted mb-3"></i>
                                <p class="text-muted">No bookings yet</p>
                                <a href="{{ route('bookings.create', ['client' => $client->id]) }}" class="btn btn-success">
                                    Create First Booking
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Stats -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Client Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary">{{ $client->projects->count() }}</h4>
                                        <small class="text-muted">Projects</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-info">{{ $client->bookings->count() ?? 0 }}</h4>
                                    <small class="text-muted">Bookings</small>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-success">${{ number_format($client->total_spent ?? 0, 2) }}</h4>
                                        <small class="text-muted">Total Spent</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-warning">${{ number_format($client->average_project_value ?? 0, 2) }}</h4>
                                    <small class="text-muted">Avg Project</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('projects.create', ['client' => $client->id]) }}" class="btn btn-primary">
                                    <i class="fas fa-project-diagram"></i> Create Project
                                </a>
                                <a href="{{ route('bookings.create', ['client' => $client->id]) }}" class="btn btn-success">
                                    <i class="fas fa-calendar-plus"></i> Create Booking
                                </a>
                                <a href="mailto:{{ $client->email }}" class="btn btn-info">
                                    <i class="fas fa-envelope"></i> Send Email
                                </a>
                                @if($client->phone)
                                <a href="tel:{{ $client->phone }}" class="btn btn-warning">
                                    <i class="fas fa-phone"></i> Call Client
                                </a>
                                @endif
                                <a href="{{ route('clients.edit', $client) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-edit"></i> Edit Client
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                @if($client->last_booking_date)
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Last Booking</h6>
                                        <p class="timeline-text">{{ $client->last_booking_date->format('M d, Y') }}</p>
                                    </div>
                                </div>
                                @endif
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-primary"></div>
                                    <div class="timeline-content">
                                        <h6 class="timeline-title">Client Added</h6>
                                        <p class="timeline-text">{{ $client->created_at->format('M d, Y') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.avatar-lg {
    width: 4rem;
    height: 4rem;
}

.avatar-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e3e6f0;
}
</style>
@endpush
