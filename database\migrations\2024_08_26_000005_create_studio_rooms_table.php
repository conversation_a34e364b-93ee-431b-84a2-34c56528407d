<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('studio_rooms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['recording', 'mixing', 'mastering', 'podcast', 'video', 'rehearsal'])->default('recording');
            $table->integer('capacity')->default(1);
            $table->decimal('hourly_rate', 10, 2);
            $table->json('equipment')->nullable(); // available equipment
            $table->json('features')->nullable(); // room features
            $table->string('image')->nullable();
            $table->json('images')->nullable(); // multiple images
            $table->boolean('is_available')->default(true);
            $table->boolean('requires_engineer')->default(false);
            $table->json('availability_schedule')->nullable(); // weekly schedule
            $table->timestamps();
            
            $table->index(['type', 'is_available']);
            $table->index(['hourly_rate', 'capacity']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('studio_rooms');
    }
};
