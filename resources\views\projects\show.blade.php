@extends('layouts.app')

@section('title', 'Project Details - ' . $project->title)

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex justify-between items-start">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $project->title }}</h1>
                <p class="mt-2 text-gray-600">{{ $project->description ?: 'No description available' }}</p>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2
                    {{ $project->status == 'completed' ? 'bg-green-100 text-green-800' :
                       ($project->status == 'active' ? 'bg-blue-100 text-blue-800' :
                       ($project->status == 'on_hold' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800')) }}">
                    {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                </span>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('projects.edit', $project) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-edit mr-2"></i>Edit Project
                </a>
                <a href="{{ route('projects.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Projects
                </a>
            </div>
        </div>
    </div>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ route('tasks.create', ['project' => $project->id]) }}">
                                <i class="fas fa-tasks"></i> Add Task
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="addTeamMember()">
                                <i class="fas fa-user-plus"></i> Add Team Member
                            </a></li>
                            <li><a class="dropdown-item" href="{{ route('bookings.create', ['project' => $project->id]) }}">
                                <i class="fas fa-calendar-plus"></i> Book Studio
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-8">
                    <!-- Project Overview -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Project Overview</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Client:</strong></td>
                                            <td>
                                                @if($project->client)
                                                <a href="{{ route('clients.show', $project->client) }}">{{ $project->client->name }}</a>
                                                @else
                                                <span class="text-muted">No client assigned</span>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Project Manager:</strong></td>
                                            <td>{{ $project->projectManager->name ?? 'Not assigned' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Priority:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ 
                                                    $project->priority == 'urgent' ? 'danger' : 
                                                    ($project->priority == 'high' ? 'warning' : 
                                                    ($project->priority == 'medium' ? 'info' : 'secondary')) 
                                                }}">
                                                    {{ ucfirst($project->priority) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Budget:</strong></td>
                                            <td class="text-success"><strong>${{ number_format($project->budget ?? 0, 2) }}</strong></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Start Date:</strong></td>
                                            <td>{{ $project->start_date ? $project->start_date->format('M d, Y') : 'Not set' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Deadline:</strong></td>
                                            <td class="{{ $project->deadline && $project->deadline->isPast() ? 'text-danger' : '' }}">
                                                {{ $project->deadline ? $project->deadline->format('M d, Y') : 'Not set' }}
                                                @if($project->deadline && $project->deadline->isPast())
                                                <i class="fas fa-exclamation-triangle text-danger"></i>
                                                @endif
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Progress:</strong></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="progress flex-grow-1 me-2" style="height: 20px;">
                                                        <div class="progress-bar" role="progressbar" 
                                                             style="width: {{ $project->progress ?? 0 }}%"
                                                             aria-valuenow="{{ $project->progress ?? 0 }}" 
                                                             aria-valuemin="0" aria-valuemax="100">
                                                            {{ $project->progress ?? 0 }}%
                                                        </div>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="updateProgress({{ $project->id }})">
                                                        Update
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Billable:</strong></td>
                                            <td>
                                                @if($project->is_billable)
                                                <span class="badge bg-success">Yes</span>
                                                @else
                                                <span class="badge bg-secondary">No</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            @if($project->description)
                            <div class="mt-3">
                                <h6>Description</h6>
                                <p class="text-muted">{{ $project->description }}</p>
                            </div>
                            @endif

                            @if($project->requirements)
                            <div class="mt-3">
                                <h6>Requirements & Deliverables</h6>
                                <p class="text-muted">{{ $project->requirements }}</p>
                            </div>
                            @endif

                            @if($project->tags)
                            <div class="mt-3">
                                <h6>Tags</h6>
                                @foreach(explode(',', $project->tags) as $tag)
                                <span class="badge bg-light text-dark me-1">{{ trim($tag) }}</span>
                                @endforeach
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Tasks -->
                    <div class="card shadow mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Tasks ({{ $project->tasks->count() }})</h5>
                            <a href="{{ route('tasks.create', ['project' => $project->id]) }}" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus"></i> Add Task
                            </a>
                        </div>
                        <div class="card-body">
                            @if($project->tasks->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Task</th>
                                            <th>Assignee</th>
                                            <th>Status</th>
                                            <th>Due Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($project->tasks->take(10) as $task)
                                        <tr>
                                            <td>
                                                <a href="{{ route('tasks.show', $task) }}">{{ $task->title }}</a>
                                                @if($task->priority == 'high' || $task->priority == 'urgent')
                                                <span class="badge bg-danger ms-1">{{ ucfirst($task->priority) }}</span>
                                                @endif
                                            </td>
                                            <td>{{ $task->assignee->name ?? 'Unassigned' }}</td>
                                            <td>
                                                <span class="badge bg-{{ 
                                                    $task->status == 'completed' ? 'success' : 
                                                    ($task->status == 'in_progress' ? 'warning' : 'secondary') 
                                                }}">
                                                    {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                                </span>
                                            </td>
                                            <td>{{ $task->due_date ? $task->due_date->format('M d') : '-' }}</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{{ route('tasks.show', $task) }}" class="btn btn-outline-primary">View</a>
                                                    <a href="{{ route('tasks.edit', $task) }}" class="btn btn-outline-secondary">Edit</a>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @if($project->tasks->count() > 10)
                            <div class="text-center mt-3">
                                <a href="{{ route('tasks.index', ['project' => $project->id]) }}" class="btn btn-outline-primary">
                                    View All Tasks ({{ $project->tasks->count() }})
                                </a>
                            </div>
                            @endif
                            @else
                            <div class="text-center py-4">
                                <i class="fas fa-tasks fa-2x text-muted mb-3"></i>
                                <p class="text-muted">No tasks created yet</p>
                                <a href="{{ route('tasks.create', ['project' => $project->id]) }}" class="btn btn-primary">
                                    Create First Task
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Team Members -->
                    <div class="card shadow mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Team Members</h5>
                            <button class="btn btn-sm btn-outline-primary" onclick="addTeamMember()">
                                <i class="fas fa-user-plus"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            @if($project->teamMembers && $project->teamMembers->count() > 0)
                            @foreach($project->teamMembers as $member)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-2">
                                        <div class="avatar-title rounded-circle bg-primary text-white">
                                            {{ substr($member->name, 0, 1) }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ $member->name }}</div>
                                        <small class="text-muted">{{ $member->role ?? 'Team Member' }}</small>
                                    </div>
                                </div>
                                <button class="btn btn-sm btn-outline-danger" onclick="removeTeamMember({{ $member->id }})">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            @endforeach
                            @else
                            <div class="text-center py-3">
                                <i class="fas fa-users fa-2x text-muted mb-2"></i>
                                <p class="text-muted">No team members assigned</p>
                                <button class="btn btn-sm btn-primary" onclick="addTeamMember()">
                                    Add Team Members
                                </button>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- Project Stats -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Project Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary">{{ $project->tasks->count() }}</h4>
                                        <small class="text-muted">Total Tasks</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success">{{ $project->tasks->where('status', 'completed')->count() }}</h4>
                                    <small class="text-muted">Completed</small>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-info">{{ $project->bookings->count() ?? 0 }}</h4>
                                        <small class="text-muted">Studio Sessions</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-warning">{{ $project->files->count() ?? 0 }}</h4>
                                    <small class="text-muted">Files</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <!-- You can add activity timeline here -->
                                <div class="text-center py-3">
                                    <i class="fas fa-clock fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">No recent activity</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Team Member Modal -->
<div class="modal fade" id="addTeamMemberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Team Member</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('projects.add-team-member', $project) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="user_id" class="form-label">Select User</label>
                        <select class="form-select" id="user_id" name="user_id" required>
                            <option value="">Choose a user</option>
                            @foreach($availableUsers ?? [] as $user)
                            <option value="{{ $user->id }}">{{ $user->name }} - {{ $user->role ?? 'No role' }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">Role in Project</label>
                        <input type="text" class="form-control" id="role" name="role" placeholder="e.g., Sound Engineer, Producer">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Member</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Update Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Project Progress</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ route('projects.update-progress', $project) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="progress" class="form-label">Progress (%)</label>
                        <input type="range" class="form-range" id="progress" name="progress" min="0" max="100" value="{{ $project->progress ?? 0 }}" oninput="updateProgressValue(this.value)">
                        <div class="d-flex justify-content-between">
                            <span>0%</span>
                            <span id="progress-value">{{ $project->progress ?? 0 }}%</span>
                            <span>100%</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="progress_notes" class="form-label">Progress Notes</label>
                        <textarea class="form-control" id="progress_notes" name="notes" rows="3" placeholder="Optional notes about the progress update"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Progress</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function addTeamMember() {
    const modal = new bootstrap.Modal(document.getElementById('addTeamMemberModal'));
    modal.show();
}

function removeTeamMember(userId) {
    if (confirm('Are you sure you want to remove this team member?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/projects/{{ $project->id }}/team-members/${userId}`;
        
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        
        const methodField = document.createElement('input');
        methodField.type = 'hidden';
        methodField.name = '_method';
        methodField.value = 'DELETE';
        
        form.appendChild(csrfToken);
        form.appendChild(methodField);
        document.body.appendChild(form);
        form.submit();
    }
}

function updateProgress(projectId) {
    const modal = new bootstrap.Modal(document.getElementById('progressModal'));
    modal.show();
}

function updateProgressValue(value) {
    document.getElementById('progress-value').textContent = value + '%';
}
</script>
@endpush
