<?php $__env->startSection('title', 'Add Expense'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">Add Expense</h1>
                <a href="<?php echo e(route('accounting.expenses.index')); ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Expenses
                </a>
            </div>

            <div class="card shadow">
                <div class="card-body">
                    <form action="<?php echo e(route('accounting.expenses.store')); ?>" method="POST" enctype="multipart/form-data" id="expenseForm">
                        <?php echo csrf_field(); ?>
                        
                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Expense Details</h5>
                                
                                <div class="mb-3">
                                    <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="description" name="description" value="<?php echo e(old('description')); ?>" 
                                           placeholder="Brief description of the expense" required>
                                    <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        <option value="equipment" <?php echo e(old('category') == 'equipment' ? 'selected' : ''); ?>>Equipment</option>
                                        <option value="utilities" <?php echo e(old('category') == 'utilities' ? 'selected' : ''); ?>>Utilities</option>
                                        <option value="rent" <?php echo e(old('category') == 'rent' ? 'selected' : ''); ?>>Rent</option>
                                        <option value="marketing" <?php echo e(old('category') == 'marketing' ? 'selected' : ''); ?>>Marketing</option>
                                        <option value="travel" <?php echo e(old('category') == 'travel' ? 'selected' : ''); ?>>Travel</option>
                                        <option value="office_supplies" <?php echo e(old('category') == 'office_supplies' ? 'selected' : ''); ?>>Office Supplies</option>
                                        <option value="software" <?php echo e(old('category') == 'software' ? 'selected' : ''); ?>>Software</option>
                                        <option value="maintenance" <?php echo e(old('category') == 'maintenance' ? 'selected' : ''); ?>>Maintenance</option>
                                        <option value="other" <?php echo e(old('category') == 'other' ? 'selected' : ''); ?>>Other</option>
                                    </select>
                                    <?php $__errorArgs = ['category'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="amount" class="form-label">Amount ($) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   id="amount" name="amount" value="<?php echo e(old('amount')); ?>" 
                                                   min="0" step="0.01" required>
                                            <?php $__errorArgs = ['amount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="expense_date" class="form-label">Expense Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control <?php $__errorArgs = ['expense_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                                   id="expense_date" name="expense_date" value="<?php echo e(old('expense_date', date('Y-m-d'))); ?>" required>
                                            <?php $__errorArgs = ['expense_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="vendor" class="form-label">Vendor/Supplier</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['vendor'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="vendor" name="vendor" value="<?php echo e(old('vendor')); ?>" 
                                           placeholder="Name of vendor or supplier">
                                    <?php $__errorArgs = ['vendor'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="payment_method" name="payment_method" required>
                                        <option value="">Select Payment Method</option>
                                        <option value="cash" <?php echo e(old('payment_method') == 'cash' ? 'selected' : ''); ?>>Cash</option>
                                        <option value="check" <?php echo e(old('payment_method') == 'check' ? 'selected' : ''); ?>>Check</option>
                                        <option value="bank_transfer" <?php echo e(old('payment_method') == 'bank_transfer' ? 'selected' : ''); ?>>Bank Transfer</option>
                                        <option value="credit_card" <?php echo e(old('payment_method') == 'credit_card' ? 'selected' : ''); ?>>Credit Card</option>
                                        <option value="debit_card" <?php echo e(old('payment_method') == 'debit_card' ? 'selected' : ''); ?>>Debit Card</option>
                                        <option value="paypal" <?php echo e(old('payment_method') == 'paypal' ? 'selected' : ''); ?>>PayPal</option>
                                        <option value="other" <?php echo e(old('payment_method') == 'other' ? 'selected' : ''); ?>>Other</option>
                                    </select>
                                    <?php $__errorArgs = ['payment_method'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="reference_number" class="form-label">Reference Number</label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['reference_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="reference_number" name="reference_number" value="<?php echo e(old('reference_number')); ?>" 
                                           placeholder="Invoice number, check number, etc.">
                                    <?php $__errorArgs = ['reference_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <!-- Additional Information -->
                            <div class="col-md-6">
                                <h5 class="mb-3">Additional Information</h5>
                                
                                <div class="mb-3">
                                    <label for="project_id" class="form-label">Related Project</label>
                                    <select class="form-select <?php $__errorArgs = ['project_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="project_id" name="project_id">
                                        <option value="">Select Project (Optional)</option>
                                        <?php $__currentLoopData = $projects ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($project->id); ?>" <?php echo e(old('project_id') == $project->id ? 'selected' : ''); ?>>
                                            <?php echo e($project->name); ?>

                                        </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <?php $__errorArgs = ['project_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="receipt" class="form-label">Receipt/Invoice</label>
                                    <input type="file" class="form-control <?php $__errorArgs = ['receipt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                           id="receipt" name="receipt" accept="image/*,.pdf">
                                    <div class="form-text">Upload receipt or invoice (PDF, JPG, PNG - Max 5MB)</div>
                                    <?php $__errorArgs = ['receipt'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes</label>
                                    <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                              id="notes" name="notes" rows="4" 
                                              placeholder="Additional notes about this expense"><?php echo e(old('notes')); ?></textarea>
                                    <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_recurring" 
                                               name="is_recurring" value="1" <?php echo e(old('is_recurring') ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_recurring">
                                            Recurring Expense
                                        </label>
                                        <div class="form-text">Check if this is a recurring monthly expense</div>
                                    </div>
                                </div>

                                <div id="recurring-options" style="display: none;">
                                    <div class="mb-3">
                                        <label for="recurring_frequency" class="form-label">Frequency</label>
                                        <select class="form-select" id="recurring_frequency" name="recurring_frequency">
                                            <option value="monthly" <?php echo e(old('recurring_frequency') == 'monthly' ? 'selected' : ''); ?>>Monthly</option>
                                            <option value="quarterly" <?php echo e(old('recurring_frequency') == 'quarterly' ? 'selected' : ''); ?>>Quarterly</option>
                                            <option value="yearly" <?php echo e(old('recurring_frequency') == 'yearly' ? 'selected' : ''); ?>>Yearly</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="recurring_end_date" class="form-label">End Date (Optional)</label>
                                        <input type="date" class="form-control" id="recurring_end_date" 
                                               name="recurring_end_date" value="<?php echo e(old('recurring_end_date')); ?>">
                                        <div class="form-text">Leave blank for indefinite recurring</div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_tax_deductible" 
                                               name="is_tax_deductible" value="1" <?php echo e(old('is_tax_deductible') ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="is_tax_deductible">
                                            Tax Deductible
                                        </label>
                                        <div class="form-text">Check if this expense is tax deductible</div>
                                    </div>
                                </div>

                                <!-- Receipt Preview -->
                                <div id="receipt-preview" style="display: none;">
                                    <h6>Receipt Preview</h6>
                                    <div class="border rounded p-2 mb-3">
                                        <img id="receipt-image" src="" alt="Receipt Preview" class="img-fluid" style="max-height: 200px;">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?php echo e(route('accounting.expenses.index')); ?>" class="btn btn-secondary">Cancel</a>
                                    <button type="submit" class="btn btn-primary">Add Expense</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Toggle recurring options
document.getElementById('is_recurring').addEventListener('change', function() {
    const recurringOptions = document.getElementById('recurring-options');
    if (this.checked) {
        recurringOptions.style.display = 'block';
    } else {
        recurringOptions.style.display = 'none';
    }
});

// Receipt preview
document.getElementById('receipt').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('receipt-preview');
    const image = document.getElementById('receipt-image');
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            if (file.type.startsWith('image/')) {
                image.src = e.target.result;
                preview.style.display = 'block';
            } else {
                preview.style.display = 'none';
            }
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});

// Form validation
document.getElementById('expenseForm').addEventListener('submit', function(e) {
    const description = document.getElementById('description').value.trim();
    const category = document.getElementById('category').value;
    const amount = parseFloat(document.getElementById('amount').value);
    const expenseDate = document.getElementById('expense_date').value;
    const paymentMethod = document.getElementById('payment_method').value;
    
    if (!description || !category || !amount || !expenseDate || !paymentMethod) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    if (amount <= 0) {
        e.preventDefault();
        alert('Amount must be greater than zero.');
        return false;
    }
    
    // Check file size if receipt is uploaded
    const receiptFile = document.getElementById('receipt').files[0];
    if (receiptFile && receiptFile.size > 5 * 1024 * 1024) { // 5MB
        e.preventDefault();
        alert('Receipt file size must be less than 5MB.');
        return false;
    }
});

// Auto-fill vendor based on category
document.getElementById('category').addEventListener('change', function() {
    const category = this.value;
    const vendorField = document.getElementById('vendor');
    
    // Suggest common vendors based on category
    const suggestions = {
        'utilities': 'Electric Company',
        'rent': 'Property Management',
        'software': 'Software Vendor',
        'equipment': 'Equipment Supplier'
    };
    
    if (suggestions[category] && !vendorField.value) {
        vendorField.placeholder = `e.g., ${suggestions[category]}`;
    }
});

// Initialize recurring options visibility
document.addEventListener('DOMContentLoaded', function() {
    const isRecurring = document.getElementById('is_recurring');
    if (isRecurring.checked) {
        document.getElementById('recurring-options').style.display = 'block';
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\workspace\.php\glitchafrica\resources\views/accounting/expenses/create.blade.php ENDPATH**/ ?>