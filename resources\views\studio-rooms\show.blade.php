@extends('layouts.app')

@section('title', $room->name)

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">{{ $room->name }}</h1>
                <div class="btn-group">
                    <a href="{{ route('studio-rooms.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Rooms
                    </a>
                    <a href="{{ route('studio-rooms.edit', $room) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Room
                    </a>
                    <a href="{{ route('bookings.create', ['room' => $room->id]) }}" class="btn btn-success">
                        <i class="fas fa-calendar-plus"></i> Book Room
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Room Details -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Room Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Type:</strong></td>
                                            <td><span class="badge bg-info">{{ ucfirst($room->type) }}</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Capacity:</strong></td>
                                            <td>{{ $room->capacity }} people</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Hourly Rate:</strong></td>
                                            <td class="text-primary"><strong>${{ number_format($room->hourly_rate, 2) }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status:</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $room->is_available ? 'success' : 'danger' }}">
                                                    {{ $room->is_available ? 'Available' : 'Unavailable' }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Engineer Required:</strong></td>
                                            <td>
                                                @if($room->requires_engineer)
                                                <span class="badge bg-warning">Yes</span>
                                                @else
                                                <span class="badge bg-secondary">No</span>
                                                @endif
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    @if($room->description)
                                    <h6>Description</h6>
                                    <p class="text-muted">{{ $room->description }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Equipment -->
                    @if($room->equipment && count($room->equipment) > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Equipment</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($room->equipment as $equipment)
                                <div class="col-md-6 mb-2">
                                    <span class="badge bg-light text-dark">
                                        <i class="fas fa-microphone me-1"></i>{{ $equipment }}
                                    </span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Features -->
                    @if($room->features && count($room->features) > 0)
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Features</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($room->features as $feature)
                                <div class="col-md-6 mb-2">
                                    <span class="badge bg-primary">
                                        <i class="fas fa-star me-1"></i>{{ $feature }}
                                    </span>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Upcoming Bookings -->
                    <div class="card shadow mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Upcoming Bookings</h5>
                            <a href="{{ route('studio-rooms.schedule', $room) }}" class="btn btn-sm btn-outline-primary">
                                View Full Schedule
                            </a>
                        </div>
                        <div class="card-body">
                            @if($room->bookings && $room->bookings->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Client</th>
                                            <th>Engineer</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($room->bookings->take(5) as $booking)
                                        <tr>
                                            <td>{{ $booking->start_time->format('M d, Y') }}</td>
                                            <td>{{ $booking->start_time->format('g:i A') }} - {{ $booking->end_time->format('g:i A') }}</td>
                                            <td>
                                                @if($booking->client)
                                                <a href="{{ route('clients.show', $booking->client) }}">{{ $booking->client->name }}</a>
                                                @else
                                                <span class="text-muted">No client</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($booking->engineer)
                                                {{ $booking->engineer->name }}
                                                @else
                                                <span class="text-muted">No engineer</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ $booking->status == 'confirmed' ? 'success' : ($booking->status == 'pending' ? 'warning' : 'secondary') }}">
                                                    {{ ucfirst($booking->status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @else
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-2x text-muted mb-3"></i>
                                <p class="text-muted">No upcoming bookings</p>
                                <a href="{{ route('bookings.create', ['room' => $room->id]) }}" class="btn btn-primary">
                                    Create First Booking
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Quick Actions -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('bookings.create', ['room' => $room->id]) }}" class="btn btn-success">
                                    <i class="fas fa-calendar-plus"></i> Book This Room
                                </a>
                                <a href="{{ route('studio-rooms.availability', $room) }}" class="btn btn-info">
                                    <i class="fas fa-clock"></i> Check Availability
                                </a>
                                <a href="{{ route('studio-rooms.stats', $room) }}" class="btn btn-warning">
                                    <i class="fas fa-chart-bar"></i> View Statistics
                                </a>
                                <a href="{{ route('studio-rooms.schedule', $room) }}" class="btn btn-primary">
                                    <i class="fas fa-calendar"></i> View Schedule
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Availability Schedule -->
                    @if($room->availability_schedule)
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Operating Hours</h5>
                        </div>
                        <div class="card-body">
                            @foreach($room->availability_schedule as $day => $schedule)
                            @if(isset($schedule['enabled']) && $schedule['enabled'])
                            <div class="d-flex justify-content-between mb-2">
                                <span class="fw-bold">{{ ucfirst($day) }}:</span>
                                <span>{{ $schedule['start'] ?? '09:00' }} - {{ $schedule['end'] ?? '18:00' }}</span>
                            </div>
                            @endif
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- Room Stats -->
                    <div class="card shadow mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Room Statistics</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary">{{ $room->bookings()->where('start_time', '>=', now()->subDays(30))->count() }}</h4>
                                        <small class="text-muted">Bookings (30d)</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success">${{ number_format($room->bookings()->where('start_time', '>=', now()->subDays(30))->sum('total_cost'), 2) }}</h4>
                                    <small class="text-muted">Revenue (30d)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
