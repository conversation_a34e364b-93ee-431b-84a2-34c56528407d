<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('departments', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('color_code', 7)->default('#3B82F6'); // hex color for UI
            $table->string('icon')->nullable(); // icon class or svg
            $table->boolean('is_active')->default(true);
            $table->json('settings')->nullable(); // dept specific settings
            $table->timestamps();
            
            $table->index(['is_active', 'slug']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('departments');
    }
};
