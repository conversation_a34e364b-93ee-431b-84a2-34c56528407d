@extends('layouts.dashboard')

@section('title', 'Clients')

@section('dashboard-content')
<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Clients</h1>
    <button onclick="openCreateClientModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
        <i class="fas fa-plus"></i>
        <span>Add Client</span>
    </button>
</div>

<!-- Filters -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="statusFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Statuses</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="vip">VIP</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Client Type</label>
                <select id="typeFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">All Types</option>
                    <option value="individual">Individual</option>
                    <option value="band">Band</option>
                    <option value="label">Record Label</option>
                    <option value="company">Company</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <input type="text" id="searchFilter" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500" placeholder="Name, email, or phone">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">&nbsp;</label>
                <div class="flex space-x-2">
                    <button onclick="applyClientFilters()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">Filter</button>
                    <button onclick="clearClientFilters()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Clear</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-users text-blue-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Clients</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $summary['total'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-user-check text-green-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Active Clients</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $summary['active'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-user-plus text-yellow-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">New This Month</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $summary['new_this_month'] ?? 0 }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-dollar-sign text-purple-500 text-2xl"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
                        <dd class="text-lg font-medium text-gray-900">₦{{ number_format($summary['total_revenue'] ?? 0, 2) }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clients Grid -->
<div id="clientsContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Clients will be loaded here via AJAX -->
</div>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="flex justify-center items-center py-12 hidden">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
</div>

<!-- Pagination -->
<div id="paginationContainer" class="flex justify-center mt-8">
    <!-- Pagination will be loaded here -->
</div>

<!-- Create/Edit Client Modal -->
<div id="clientModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900" id="clientModalTitle">Create Client</h3>
            <button onclick="closeClientModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <form id="clientForm" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input type="text" name="name" id="clientName" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                    <input type="email" name="email" id="clientEmail" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input type="tel" name="phone" id="clientPhone" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Client Type</label>
                    <select name="type" id="clientType" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="individual">Individual</option>
                        <option value="band">Band</option>
                        <option value="label">Record Label</option>
                        <option value="company">Company</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" id="clientStatus" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="vip">VIP</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Company</label>
                    <input type="text" name="company" id="clientCompany" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <textarea name="address" id="clientAddress" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea name="notes" id="clientNotes" rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
            </div>

            <div class="flex justify-end space-x-4">
                <button type="button" onclick="closeClientModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg">Cancel</button>
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
                    <span id="clientSubmitButtonText">Create Client</span>
                </button>
            </div>
        </form>
    </div>
</div>

<!-- View Client Modal -->
<div id="viewClientModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Client Details</h3>
            <button onclick="closeViewClientModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div id="clientDetails">
            <!-- Client details will be loaded here -->
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Client</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Are you sure you want to delete this client?</p>
                <p class="text-sm text-red-600 mt-2"><strong>Warning:</strong> This will also delete all associated projects, bookings, and data.</p>
            </div>
            <div class="flex justify-center space-x-4 mt-4">
                <button type="button" onclick="closeDeleteModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded">Cancel</button>
                <button type="button" onclick="confirmDeleteClient()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded">Delete Client</button>
            </div>
        </div>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
let currentClientPage = 1;
let currentClientId = null;

// Setup CSRF token for AJAX requests
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

// Load clients on page load
$(document).ready(function() {
    console.log('Document ready, loading clients...');
    loadClients();

    // Auto-apply filters on change
    $('#statusFilter, #typeFilter').change(function() {
        currentClientPage = 1;
        loadClients();
    });

    // Search with debounce
    let searchTimeout;
    $('#searchFilter').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            currentClientPage = 1;
            loadClients();
        }, 500);
    });
});

// Load clients via AJAX
function loadClients() {
    console.log('Loading clients...');
    $('#loadingSpinner').removeClass('hidden');
    $('#clientsContainer').addClass('opacity-50');

    const filters = {
        status: $('#statusFilter').val(),
        type: $('#typeFilter').val(),
        search: $('#searchFilter').val(),
        page: currentClientPage
    };

    console.log('Client filters:', filters);

    $.ajax({
        url: '{{ route("clients.index") }}',
        method: 'GET',
        data: filters,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .done(function(response) {
        console.log('Clients response:', response);
        if (response.success) {
            renderClients(response.data.clients);
            renderClientPagination(response.data.pagination);
            updateClientSummaryCards(response.data.summary);
        } else {
            console.error('Clients response error:', response);
            showClientNotification('Error loading clients: ' + response.message, 'error');
        }
    })
    .fail(function(xhr) {
        console.error('Clients AJAX Error:', xhr);
        console.error('Response Text:', xhr.responseText);
        let errorMessage = 'Error loading clients';
        if (xhr.responseJSON && xhr.responseJSON.message) {
            errorMessage += ': ' + xhr.responseJSON.message;
        } else if (xhr.status) {
            errorMessage += ' (Status: ' + xhr.status + ')';
        }
        showClientNotification(errorMessage, 'error');
    })
    .always(function() {
        $('#loadingSpinner').addClass('hidden');
        $('#clientsContainer').removeClass('opacity-50');
    });
}

// Render clients grid
function renderClients(clients) {
    const container = $('#clientsContainer');

    if (clients.length === 0) {
        container.html(`
            <div class="col-span-full text-center py-12">
                <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
                <p class="text-gray-500 mb-4">Add your first client to get started.</p>
                <button onclick="openCreateClientModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">Add Client</button>
            </div>
        `);
        return;
    }

    let html = '';
    clients.forEach(client => {
        const statusColor = getClientStatusColor(client.status);

        html += `
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-12 w-12">
                                ${client.profile_picture ?
                                    `<img src="${client.profile_picture}" alt="${client.name}" class="h-12 w-12 rounded-full object-cover">` :
                                    `<div class="h-12 w-12 rounded-full bg-indigo-500 flex items-center justify-center text-white font-semibold text-lg">
                                        ${client.name.substring(0, 2).toUpperCase()}
                                    </div>`
                                }
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">${client.name}</h3>
                                <p class="text-sm text-gray-500">${client.type ? client.type.charAt(0).toUpperCase() + client.type.slice(1) : 'Individual'}</p>
                            </div>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColor}">
                            ${client.status ? client.status.charAt(0).toUpperCase() + client.status.slice(1) : 'Active'}
                        </span>
                    </div>

                    <div class="space-y-3 mb-4">
                        <div class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-envelope text-gray-400 mr-2"></i>
                            <span>${client.email}</span>
                        </div>
                        ${client.phone ? `
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-phone text-gray-400 mr-2"></i>
                                <span>${client.phone}</span>
                            </div>
                        ` : ''}
                        ${client.company ? `
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-building text-gray-400 mr-2"></i>
                                <span>${client.company}</span>
                            </div>
                        ` : ''}
                    </div>

                    <div class="grid grid-cols-3 gap-4 text-center border-t border-gray-200 pt-4">
                        <div>
                            <div class="text-lg font-semibold text-blue-600">${client.projects_count || 0}</div>
                            <div class="text-xs text-gray-500">Projects</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-purple-600">${client.bookings_count || 0}</div>
                            <div class="text-xs text-gray-500">Bookings</div>
                        </div>
                        <div>
                            <div class="text-lg font-semibold text-green-600">₦${formatNumber(client.total_spent || 0)}</div>
                            <div class="text-xs text-gray-500">Spent</div>
                        </div>
                    </div>

                    ${client.last_booking_date ? `
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="text-xs text-gray-500">Last booking:</div>
                            <div class="text-sm font-medium text-gray-900">${formatDate(client.last_booking_date)}</div>
                        </div>
                    ` : ''}

                    ${client.notes ? `
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">${client.notes.substring(0, 80)}${client.notes.length > 80 ? '...' : ''}</p>
                        </div>
                    ` : ''}

                    <div class="mt-6 flex space-x-2">
                        <button onclick="viewClient(${client.id})" class="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white text-center py-2 px-3 rounded text-sm">View</button>
                        <button onclick="editClient(${client.id})" class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 text-center py-2 px-3 rounded text-sm">Edit</button>
                        <button onclick="deleteClient(${client.id})" class="bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded text-sm">Delete</button>
                    </div>
                </div>
            </div>
        `;
    });

    container.html(html);
}

// Helper functions
function getClientStatusColor(status) {
    const colors = {
        'active': 'bg-green-100 text-green-800',
        'inactive': 'bg-gray-100 text-gray-800',
        'vip': 'bg-yellow-100 text-yellow-800'
    };
    return colors[status] || 'bg-green-100 text-green-800';
}

function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Modal functions
function openCreateClientModal() {
    $('#clientModalTitle').text('Create Client');
    $('#clientSubmitButtonText').text('Create Client');
    $('#clientForm')[0].reset();
    currentClientId = null;
    $('#clientModal').removeClass('hidden');
}

function editClient(clientId) {
    currentClientId = clientId;
    $('#clientModalTitle').text('Edit Client');
    $('#clientSubmitButtonText').text('Update Client');

    // Load client data
    $.get(`/clients/${clientId}`, {
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .done(function(response) {
            if (response.success) {
                const client = response.data.client || response.data;
                $('#clientName').val(client.name || '');
                $('#clientEmail').val(client.email || '');
                $('#clientPhone').val(client.phone || '');
                $('#clientType').val(client.type || '');
                $('#clientStatus').val(client.status || '');
                $('#clientCompany').val(client.company || '');
                $('#clientAddress').val(client.address || '');
                $('#clientNotes').val(client.notes || '');
                $('#clientModal').removeClass('hidden');
            } else {
                showClientNotification('Error loading client data: ' + (response.message || 'Unknown error'), 'error');
            }
        })
        .fail(function() {
            showClientNotification('Error loading client data', 'error');
        });
}

function closeClientModal() {
    $('#clientModal').addClass('hidden');
    currentClientId = null;
}

function viewClient(clientId) {
    $.get(`/clients/${clientId}`, {
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
        .done(function(response) {
            if (response.success) {
                renderClientDetails(response.data.client);
                $('#viewClientModal').removeClass('hidden');
            }
        })
        .fail(function() {
            showClientNotification('Error loading client details', 'error');
        });
}

function closeViewClientModal() {
    $('#viewClientModal').addClass('hidden');
}

function deleteClient(clientId) {
    currentClientId = clientId;
    $('#deleteModal').removeClass('hidden');
}

function closeDeleteModal() {
    $('#deleteModal').addClass('hidden');
    currentClientId = null;
}

function confirmDeleteClient() {
    if (!currentClientId) return;

    $.ajax({
        url: `/clients/${currentClientId}`,
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            showClientNotification('Client deleted successfully', 'success');
            closeDeleteModal();
            loadClients();
        }
    })
    .fail(function() {
        showClientNotification('Error deleting client', 'error');
    });
}

// Form submissions
$('#clientForm').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const url = currentClientId ? `/clients/${currentClientId}` : '/clients';
    const method = currentClientId ? 'PUT' : 'POST';

    if (currentClientId) {
        formData.append('_method', 'PUT');
    }

    $.ajax({
        url: url,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            showClientNotification(currentClientId ? 'Client updated successfully' : 'Client created successfully', 'success');
            closeClientModal();
            loadClients();
        }
    })
    .fail(function(xhr) {
        const errors = xhr.responseJSON?.errors;
        if (errors) {
            let errorMessage = 'Please fix the following errors:\n';
            Object.values(errors).forEach(error => {
                errorMessage += '- ' + error[0] + '\n';
            });
            showClientNotification(errorMessage, 'error');
        } else {
            showClientNotification('Error saving client', 'error');
        }
    });
});

// Utility functions
function applyClientFilters() {
    currentClientPage = 1;
    loadClients();
}

function clearClientFilters() {
    $('#statusFilter').val('');
    $('#typeFilter').val('');
    $('#searchFilter').val('');
    currentClientPage = 1;
    loadClients();
}

function renderClientPagination(pagination) {
    // Implementation for pagination rendering
    $('#paginationContainer').html(''); // Simplified for now
}

function updateClientSummaryCards(summary) {
    // Update summary cards if they exist
    // This would update the summary statistics
}

function renderClientDetails(client) {
    $('#clientDetails').html(`
        <div class="space-y-6">
            <!-- Basic Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-xl font-semibold text-gray-900 mb-4">${client.name || 'N/A'}</h4>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <p class="text-sm text-gray-900">${client.email || 'N/A'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Phone</label>
                            <p class="text-sm text-gray-900">${client.phone || 'N/A'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Company</label>
                            <p class="text-sm text-gray-900">${client.company || 'N/A'}</p>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Type</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                ${client.type ? client.type.charAt(0).toUpperCase() + client.type.slice(1) : 'N/A'}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getClientStatusColor(client.status)}">
                                ${client.status ? client.status.charAt(0).toUpperCase() + client.status.slice(1) : 'N/A'}
                            </span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Spent</label>
                            <p class="text-sm text-gray-900">₦${client.total_spent || '0.00'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Projects</label>
                            <p class="text-sm text-gray-900">${client.total_projects || 0}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bio/Notes -->
            ${client.bio ? `
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                    <p class="text-sm text-gray-900">${client.bio}</p>
                </div>
            ` : ''}

            <!-- Address -->
            ${client.address ? `
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                    <p class="text-sm text-gray-900">${client.address}</p>
                </div>
            ` : ''}

            <!-- Timestamps -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-500 border-t pt-4">
                <div>
                    <p>Created: ${formatDate(client.created_at)}</p>
                    <p>Updated: ${formatDate(client.updated_at)}</p>
                </div>
                <div>
                    ${client.last_project_at ? `<p>Last Project: ${formatDate(client.last_project_at)}</p>` : ''}
                </div>
            </div>
        </div>
    `);
}

function getClientStatusColor(status) {
    switch(status) {
        case 'active': return 'bg-green-100 text-green-800';
        case 'inactive': return 'bg-yellow-100 text-yellow-800';
        case 'blacklisted': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function showClientNotification(message, type) {
    // Simple notification system
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const notification = $(`
        <div class="fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50">
            ${message}
        </div>
    `);

    $('body').append(notification);
    setTimeout(() => notification.remove(), 3000);
}
</script>
@endpush
