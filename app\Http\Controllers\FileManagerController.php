<?php

namespace App\Http\Controllers;

use App\Models\FileUpload;
use App\Models\Project;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use ZipArchive;

class FileManagerController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        
        $query = FileUpload::with(['uploadedBy', 'uploadable'])
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('project_id')) {
            $query->where('uploadable_type', 'App\Models\Project')
                  ->where('uploadable_id', $request->project_id);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where('original_name', 'like', "%{$search}%");
        }

        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter by user's accessible files if not admin
        if (!$user->hasPermission('*')) {
            $query->where(function ($q) use ($user) {
                $q->where('uploaded_by', $user->id)
                  ->orWhereHasMorph('uploadable', ['App\Models\Project'], function ($projectQuery) use ($user) {
                      $projectQuery->where('project_manager_id', $user->id)
                               ->orWhereHas('teamMembers', function ($teamQuery) use ($user) {
                                   $teamQuery->where('user_id', $user->id);
                               });
                  });
            });
        }

        $files = $query->paginate($request->get('per_page', 20));

        return view('file-manager.index', [
            'files' => [
                'data' => $files->items(),
                'current_page' => $files->currentPage(),
                'last_page' => $files->lastPage(),
                'per_page' => $files->perPage(),
                'total' => $files->total()
            ],
            'storage_stats' => $this->getStorageStats(),
            'filters' => [
                'type' => $request->get('type'),
                'search' => $request->get('search'),
                'project_id' => $request->get('project_id')
            ]
        ]);
    }

    public function uploadMultiple(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'files' => 'required|array|max:10',
            'files.*' => 'file|max:512000', // 500MB max per file
            'uploadable_type' => 'required|string',
            'uploadable_id' => 'required|integer',
            'folder' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $uploadedFiles = [];
        $errors = [];

        foreach ($request->file('files') as $file) {
            try {
                $uploadedFile = $this->processFileUpload($file, $request);
                $uploadedFiles[] = $uploadedFile;
            } catch (\Exception $e) {
                $errors[] = [
                    'file' => $file->getClientOriginalName(),
                    'error' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => count($errors) === 0,
            'message' => count($uploadedFiles) . ' files uploaded successfully',
            'data' => [
                'uploaded_files' => $uploadedFiles,
                'errors' => $errors
            ]
        ], count($errors) > 0 ? 207 : 201); // 207 Multi-Status if there are errors
    }

    public function createFolder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'parent_path' => 'nullable|string',
            'uploadable_type' => 'required|string',
            'uploadable_id' => 'required|integer'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $folderPath = $this->buildFolderPath($request->parent_path, $request->name);
        $disk = config('filesystems.default');

        if (Storage::disk($disk)->exists($folderPath)) {
            return response()->json([
                'success' => false,
                'message' => 'Folder already exists'
            ], 422);
        }

        Storage::disk($disk)->makeDirectory($folderPath);

        // Create a folder record
        $folder = FileUpload::create([
            'filename' => '.folder',
            'original_name' => $request->name,
            'path' => $folderPath,
            'disk' => $disk,
            'mime_type' => 'folder',
            'size' => 0,
            'uploadable_type' => $request->uploadable_type,
            'uploadable_id' => $request->uploadable_id,
            'uploaded_by' => $request->user()->id,
            'type' => 'folder'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Folder created successfully',
            'data' => ['folder' => $folder]
        ], 201);
    }

    public function move(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file_ids' => 'required|array',
            'file_ids.*' => 'exists:file_uploads,id',
            'destination_path' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $movedFiles = [];
        $errors = [];

        foreach ($request->file_ids as $fileId) {
            try {
                $file = FileUpload::find($fileId);
                $newPath = $this->moveFile($file, $request->destination_path);
                $file->update(['path' => $newPath]);
                $movedFiles[] = $file->fresh();
            } catch (\Exception $e) {
                $errors[] = [
                    'file_id' => $fileId,
                    'error' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => count($errors) === 0,
            'message' => count($movedFiles) . ' files moved successfully',
            'data' => [
                'moved_files' => $movedFiles,
                'errors' => $errors
            ]
        ]);
    }

    public function copy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file_ids' => 'required|array',
            'file_ids.*' => 'exists:file_uploads,id',
            'destination_path' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $copiedFiles = [];
        $errors = [];

        foreach ($request->file_ids as $fileId) {
            try {
                $originalFile = FileUpload::find($fileId);
                $copiedFile = $this->copyFile($originalFile, $request->destination_path);
                $copiedFiles[] = $copiedFile;
            } catch (\Exception $e) {
                $errors[] = [
                    'file_id' => $fileId,
                    'error' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => count($errors) === 0,
            'message' => count($copiedFiles) . ' files copied successfully',
            'data' => [
                'copied_files' => $copiedFiles,
                'errors' => $errors
            ]
        ]);
    }

    public function bulkDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file_ids' => 'required|array',
            'file_ids.*' => 'exists:file_uploads,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $deletedCount = 0;
        $errors = [];

        foreach ($request->file_ids as $fileId) {
            try {
                $file = FileUpload::find($fileId);
                
                if (!$this->canUserDeleteFile($request->user(), $file)) {
                    $errors[] = [
                        'file_id' => $fileId,
                        'error' => 'Access denied'
                    ];
                    continue;
                }

                $file->delete();
                $deletedCount++;
            } catch (\Exception $e) {
                $errors[] = [
                    'file_id' => $fileId,
                    'error' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => count($errors) === 0,
            'message' => "{$deletedCount} files deleted successfully",
            'data' => ['errors' => $errors]
        ]);
    }

    public function downloadZip(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file_ids' => 'required|array',
            'file_ids.*' => 'exists:file_uploads,id',
            'archive_name' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $files = FileUpload::whereIn('id', $request->file_ids)->get();
        $archiveName = $request->archive_name ?: 'files_' . now()->format('Y-m-d_H-i-s') . '.zip';
        $tempPath = storage_path('app/temp/' . $archiveName);

        // Ensure temp directory exists
        if (!file_exists(dirname($tempPath))) {
            mkdir(dirname($tempPath), 0755, true);
        }

        $zip = new ZipArchive();
        if ($zip->open($tempPath, ZipArchive::CREATE) !== TRUE) {
            return response()->json([
                'success' => false,
                'message' => 'Could not create archive'
            ], 500);
        }

        foreach ($files as $file) {
            if ($file->type !== 'folder' && Storage::disk($file->disk)->exists($file->path)) {
                $filePath = Storage::disk($file->disk)->path($file->path);
                $zip->addFile($filePath, $file->original_name);
            }
        }

        $zip->close();

        // Track download event
        AnalyticsEvent::track('bulk_download', 'file_manager', null, [
            'file_count' => count($files),
            'archive_size' => filesize($tempPath)
        ]);

        return response()->download($tempPath, $archiveName)->deleteFileAfterSend();
    }

    public function getStorageStats()
    {
        $stats = [
            'total_files' => FileUpload::where('type', '!=', 'folder')->count(),
            'total_size' => FileUpload::where('type', '!=', 'folder')->sum('size'),
            'by_type' => FileUpload::where('type', '!=', 'folder')
                ->groupBy('type')
                ->selectRaw('type, COUNT(*) as count, SUM(size) as total_size')
                ->get()
                ->keyBy('type'),
            'recent_uploads' => FileUpload::where('created_at', '>=', now()->subDays(7))->count(),
            'storage_usage' => $this->getStorageUsage()
        ];

        return $stats;
    }

    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:2',
            'type' => 'nullable|string',
            'project_id' => 'nullable|exists:projects,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $query = FileUpload::with(['uploadedBy', 'uploadable'])
            ->where('original_name', 'like', '%' . $request->query . '%');

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('project_id')) {
            $query->where('uploadable_type', 'App\Models\Project')
                  ->where('uploadable_id', $request->project_id);
        }

        $results = $query->limit(50)->get();

        return response()->json([
            'success' => true,
            'data' => ['results' => $results]
        ]);
    }

    // Private helper methods
    private function processFileUpload($file, $request): FileUpload
    {
        $mimeType = $file->getMimeType();
        $fileType = $this->determineFileType($mimeType);
        $originalName = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $filename = Str::uuid() . '.' . $extension;

        $path = $this->getStoragePath($fileType, $request->uploadable_type, $request->folder);
        $fullPath = $path . '/' . $filename;

        $disk = config('filesystems.default');
        $file->storeAs($path, $filename, $disk);

        return FileUpload::create([
            'filename' => $filename,
            'original_name' => $originalName,
            'path' => $fullPath,
            'disk' => $disk,
            'mime_type' => $mimeType,
            'size' => $file->getSize(),
            'uploadable_type' => $request->uploadable_type,
            'uploadable_id' => $request->uploadable_id,
            'uploaded_by' => $request->user()->id,
            'type' => $fileType,
            'metadata' => $this->extractMetadata($file, $fileType)
        ]);
    }

    private function determineFileType(string $mimeType): string
    {
        if (str_starts_with($mimeType, 'audio/')) {
            return 'audio';
        } elseif (str_starts_with($mimeType, 'video/')) {
            return 'video';
        } elseif (str_starts_with($mimeType, 'image/')) {
            return 'image';
        } elseif (in_array($mimeType, [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        ])) {
            return 'document';
        }

        return 'other';
    }

    private function getStoragePath(string $fileType, string $uploadableType, ?string $folder = null): string
    {
        $baseType = class_basename($uploadableType);
        $basePath = "uploads/{$baseType}/{$fileType}/" . date('Y/m');
        
        return $folder ? $basePath . '/' . $folder : $basePath;
    }

    private function buildFolderPath(?string $parentPath, string $folderName): string
    {
        return $parentPath ? $parentPath . '/' . $folderName : 'uploads/folders/' . $folderName;
    }

    private function extractMetadata($file, string $fileType): array
    {
        $metadata = [];

        if ($fileType === 'image') {
            $imageInfo = getimagesize($file->getPathname());
            if ($imageInfo) {
                $metadata['width'] = $imageInfo[0];
                $metadata['height'] = $imageInfo[1];
            }
        }

        return $metadata;
    }

    private function canUserDeleteFile($user, FileUpload $file): bool
    {
        if ($user->hasPermission('*')) {
            return true;
        }

        return $file->uploaded_by === $user->id;
    }

    private function getStorageUsage(): array
    {
        $totalSize = FileUpload::sum('size');
        $maxStorage = 10 * 1024 * 1024 * 1024; // 10GB limit

        return [
            'used' => $totalSize,
            'total' => $maxStorage,
            'percentage' => ($totalSize / $maxStorage) * 100,
            'remaining' => $maxStorage - $totalSize
        ];
    }

    private function moveFile(FileUpload $file, string $destinationPath): string
    {
        $oldPath = $file->path;
        $newPath = $destinationPath . '/' . $file->filename;

        Storage::disk($file->disk)->move($oldPath, $newPath);

        return $newPath;
    }

    private function copyFile(FileUpload $originalFile, string $destinationPath): FileUpload
    {
        $newFilename = Str::uuid() . '.' . pathinfo($originalFile->filename, PATHINFO_EXTENSION);
        $newPath = $destinationPath . '/' . $newFilename;

        Storage::disk($originalFile->disk)->copy($originalFile->path, $newPath);

        return FileUpload::create([
            'filename' => $newFilename,
            'original_name' => 'Copy of ' . $originalFile->original_name,
            'path' => $newPath,
            'disk' => $originalFile->disk,
            'mime_type' => $originalFile->mime_type,
            'size' => $originalFile->size,
            'uploadable_type' => $originalFile->uploadable_type,
            'uploadable_id' => $originalFile->uploadable_id,
            'uploaded_by' => auth()->id(),
            'type' => $originalFile->type,
            'metadata' => $originalFile->metadata
        ]);
    }
}
