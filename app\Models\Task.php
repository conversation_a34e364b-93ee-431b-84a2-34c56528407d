<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'description',
        'project_id',
        'workflow_stage_id',
        'assigned_to',
        'assigned_by',
        'department_id',
        'status',
        'priority',
        'due_date',
        'started_at',
        'completed_at',
        'estimated_hours',
        'actual_hours',
        'progress_percentage',
        'requirements',
        'deliverables',
        'metadata'
    ];

    protected $casts = [
        'due_date' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'requirements' => 'array',
        'deliverables' => 'array',
        'metadata' => 'array'
    ];

    // relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function dependencies(): BelongsToMany
    {
        return $this->belongsToMany(Task::class, 'task_dependencies', 'task_id', 'depends_on_task_id')
                    ->withPivot('type')
                    ->withTimestamps();
    }

    public function dependentTasks(): BelongsToMany
    {
        return $this->belongsToMany(Task::class, 'task_dependencies', 'depends_on_task_id', 'task_id')
                    ->withPivot('type')
                    ->withTimestamps();
    }

    public function fileUploads(): MorphMany
    {
        return $this->morphMany(FileUpload::class, 'uploadable');
    }

    public function analyticsEvents(): MorphMany
    {
        return $this->morphMany(AnalyticsEvent::class, 'trackable');
    }

    // scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->whereNotIn('status', ['completed', 'cancelled']);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    public function scopeByDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    // methods
    public function start(): void
    {
        $this->update([
            'status' => 'in_progress',
            'started_at' => now()
        ]);

        // track analytics
        $this->analyticsEvents()->create([
            'event_type' => 'task_started',
            'event_category' => 'task',
            'user_id' => $this->assigned_to,
            'occurred_at' => now()
        ]);
    }

    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'progress_percentage' => 100
        ]);

        // update project progress
        $this->project->updateProgress();

        // track analytics
        $this->analyticsEvents()->create([
            'event_type' => 'task_completed',
            'event_category' => 'task',
            'user_id' => $this->assigned_to,
            'occurred_at' => now()
        ]);
    }

    public function isOverdue(): bool
    {
        return $this->due_date && 
               $this->due_date->isPast() && 
               !in_array($this->status, ['completed', 'cancelled']);
    }

    public function canStart(): bool
    {
        // check if all dependencies are completed
        return $this->dependencies()
                   ->whereNotIn('status', ['completed'])
                   ->count() === 0;
    }

    public function getDurationInHours(): ?float
    {
        if (!$this->started_at || !$this->completed_at) {
            return null;
        }

        return $this->started_at->diffInHours($this->completed_at);
    }

    public function updateProgress(int $percentage): void
    {
        $this->update(['progress_percentage' => min(100, max(0, $percentage))]);

        if ($percentage === 100) {
            $this->complete();
        }
    }

    public function addDependency(Task $task, string $type = 'finish_to_start'): void
    {
        $this->dependencies()->attach($task->id, ['type' => $type]);
    }
}
