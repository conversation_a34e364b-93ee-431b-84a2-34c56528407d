<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\StudioRoomController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\FinancialController;
use App\Http\Controllers\AccountingController;
use App\Http\Controllers\FileManagerController;
use App\Http\Controllers\FileUploadController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\AutomationController;
use App\Http\Controllers\WorkflowController;
use App\Http\Controllers\ProjectWorkflowController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\DepartmentController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('dashboard');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'web'])->name('dashboard');

    // Projects - Complete CRUD
    Route::resource('projects', ProjectController::class);
    Route::post('projects/{project}/team-members', [ProjectController::class, 'addTeamMember'])->name('projects.add-team-member');
    Route::delete('projects/{project}/team-members/{user}', [ProjectController::class, 'removeTeamMember'])->name('projects.remove-team-member');
    Route::post('projects/{project}/update-progress', [ProjectController::class, 'updateProgress'])->name('projects.update-progress');

    // Tasks - Complete CRUD
    Route::resource('tasks', TaskController::class);
    Route::post('tasks/{task}/start', [TaskController::class, 'start'])->name('tasks.start');
    Route::post('tasks/{task}/complete', [TaskController::class, 'complete'])->name('tasks.complete');
    Route::put('tasks/{task}/progress', [TaskController::class, 'updateProgress'])->name('tasks.update-progress');

    // Bookings - Complete CRUD
    Route::resource('bookings', BookingController::class);
    Route::post('bookings/{booking}/confirm', [BookingController::class, 'confirm'])->name('bookings.confirm');
    Route::post('bookings/{booking}/check-in', [BookingController::class, 'checkIn'])->name('bookings.check-in');
    Route::post('bookings/{booking}/check-out', [BookingController::class, 'checkOut'])->name('bookings.check-out');
    Route::post('bookings/{booking}/cancel', [BookingController::class, 'cancel'])->name('bookings.cancel');
    Route::get('bookings/availability/check', [BookingController::class, 'availability'])->name('bookings.availability');

    // Clients - Complete CRUD
    Route::resource('clients', ClientController::class);
    Route::get('clients/search', [ClientController::class, 'search'])->name('clients.search');
    Route::get('clients/{client}/stats', [ClientController::class, 'stats'])->name('clients.stats');
    Route::get('clients/{client}/projects', [ClientController::class, 'projects'])->name('clients.projects');
    Route::get('clients/{client}/bookings', [ClientController::class, 'bookings'])->name('clients.bookings');
    Route::post('clients/{client}/update-stats', [ClientController::class, 'updateStats'])->name('clients.update-stats');

    // Studio Rooms - Complete CRUD
    Route::resource('studio-rooms', StudioRoomController::class);
    Route::get('studio-rooms/{room}/availability', [StudioRoomController::class, 'availability'])->name('studio-rooms.availability');
    Route::get('studio-rooms/{room}/stats', [StudioRoomController::class, 'stats'])->name('studio-rooms.stats');
    Route::get('studio-rooms/{room}/schedule', [StudioRoomController::class, 'schedule'])->name('studio-rooms.schedule');

    // Chat & Communication
    Route::get('chat', [ChatController::class, 'index'])->name('chat.index');
    Route::get('chat/channels', [ChatController::class, 'channels'])->name('chat.channels');
    Route::post('chat/channels', [ChatController::class, 'createChannel'])->name('chat.create-channel');
    Route::get('chat/channels/{channel}', [ChatController::class, 'showChannel'])->name('chat.show-channel');
    Route::get('chat/channels/{channel}/messages', [ChatController::class, 'messages'])->name('chat.messages');
    Route::post('chat/channels/{channel}/messages', [ChatController::class, 'sendMessage'])->name('chat.send-message');

    // Analytics & Reporting
    Route::get('analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
    Route::get('analytics/overview', [AnalyticsController::class, 'overview'])->name('analytics.overview');
    Route::get('analytics/departments', [AnalyticsController::class, 'departmentPerformance'])->name('analytics.departments');
    Route::get('analytics/users', [AnalyticsController::class, 'userPerformance'])->name('analytics.users');
    Route::get('analytics/projects', [AnalyticsController::class, 'projectAnalytics'])->name('analytics.projects');
    Route::get('analytics/revenue', [AnalyticsController::class, 'revenueAnalytics'])->name('analytics.revenue');

    // File Management & Upload
    Route::get('file-manager', [FileManagerController::class, 'index'])->name('file-manager.index');
    Route::post('file-manager/upload-multiple', [FileManagerController::class, 'uploadMultiple'])->name('file-manager.upload-multiple');
    Route::post('file-manager/create-folder', [FileManagerController::class, 'createFolder'])->name('file-manager.create-folder');
    Route::post('file-manager/move', [FileManagerController::class, 'move'])->name('file-manager.move');
    Route::post('file-manager/copy', [FileManagerController::class, 'copy'])->name('file-manager.copy');
    Route::post('file-manager/bulk-delete', [FileManagerController::class, 'bulkDelete'])->name('file-manager.bulk-delete');
    Route::get('file-manager/search', [FileManagerController::class, 'search'])->name('file-manager.search');

    // File Upload Management
    Route::get('files', [FileUploadController::class, 'index'])->name('files.index');
    Route::post('files/upload', [FileUploadController::class, 'upload'])->name('files.upload');
    Route::get('files/{file}/download', [FileUploadController::class, 'download'])->name('files.download');
    Route::get('files/{file}/stream', [FileUploadController::class, 'stream'])->name('files.stream');
    Route::delete('files/{file}', [FileUploadController::class, 'delete'])->name('files.delete');
    Route::post('files/{file}/versions', [FileUploadController::class, 'createVersion'])->name('files.create-version');

    // Workflow Automation
    Route::resource('auto-workflows', WorkflowController::class);
    Route::post('auto-workflows/{workflow}/toggle', [WorkflowController::class, 'toggle'])->name('auto-workflows.toggle');
    Route::post('auto-workflows/{workflow}/execute', [WorkflowController::class, 'execute'])->name('auto-workflows.execute');
    Route::get('auto-workflows/templates/list', [WorkflowController::class, 'templates'])->name('auto-workflows.templates');
    Route::post('auto-workflows/from-template', [WorkflowController::class, 'createFromTemplate'])->name('auto-workflows.from-template');

    Route::resource('workflows', ProjectWorkflowController::class);

    Route::resource('automation', AutomationController::class);
    Route::post('automation/{workflow}/toggle', [AutomationController::class, 'toggle'])->name('automation.toggle');
    Route::post('automation/{workflow}/execute', [AutomationController::class, 'execute'])->name('automation.execute');

    // Financial Management
    Route::get('financial', [FinancialController::class, 'dashboard'])->name('financial.dashboard');
    Route::get('financial/revenue-analysis', [FinancialController::class, 'revenueAnalysis'])->name('financial.revenue-analysis');
    Route::get('financial/expense-tracking', [FinancialController::class, 'expenseTracking'])->name('financial.expense-tracking');
    Route::get('financial/profitability', [FinancialController::class, 'profitabilityAnalysis'])->name('financial.profitability');
    Route::get('financial/cash-flow', [FinancialController::class, 'cashFlowAnalysis'])->name('financial.cash-flow');
    Route::get('financial/clients/{client}/profile', [FinancialController::class, 'clientFinancialProfile'])->name('financial.client-profile');

    // Accounting Management
    Route::get('accounting', [AccountingController::class, 'dashboard'])->name('accounting.dashboard');
    Route::resource('accounting/invoices', AccountingController::class, ['names' => [
        'index' => 'accounting.invoices.index',
        'create' => 'accounting.invoices.create',
        'store' => 'accounting.invoices.store',
        'show' => 'accounting.invoices.show',
        'edit' => 'accounting.invoices.edit',
        'update' => 'accounting.invoices.update',
        'destroy' => 'accounting.invoices.destroy'
    ]]);
    Route::post('accounting/invoices/{invoice}/send', [AccountingController::class, 'sendInvoice'])->name('accounting.invoices.send');
    Route::post('accounting/invoices/{invoice}/payments', [AccountingController::class, 'recordPayment'])->name('accounting.invoices.record-payment');

    Route::get('accounting/expenses', [AccountingController::class, 'expensesIndex'])->name('accounting.expenses.index');
    Route::get('accounting/expenses/create', [AccountingController::class, 'expensesCreate'])->name('accounting.expenses.create');
    Route::post('accounting/expenses', [AccountingController::class, 'expensesStore'])->name('accounting.expenses.store');
    Route::get('accounting/expenses/{expense}', [AccountingController::class, 'expensesShow'])->name('accounting.expenses.show');
    Route::get('accounting/expenses/{expense}/edit', [AccountingController::class, 'expensesEdit'])->name('accounting.expenses.edit');
    Route::put('accounting/expenses/{expense}', [AccountingController::class, 'expensesUpdate'])->name('accounting.expenses.update');
    Route::delete('accounting/expenses/{expense}', [AccountingController::class, 'expensesDestroy'])->name('accounting.expenses.destroy');

    Route::get('accounting/transactions', [AccountingController::class, 'transactionsIndex'])->name('accounting.transactions.index');

    Route::get('accounting/reports', [AccountingController::class, 'reportsIndex'])->name('accounting.reports.index');
    Route::get('accounting/reports/export', [AccountingController::class, 'exportReport'])->name('accounting.reports.export');
    Route::get('accounting/reports/profit-loss', [AccountingController::class, 'profitLossReport'])->name('accounting.reports.profit-loss');
    Route::get('accounting/reports/balance-sheet', [AccountingController::class, 'balanceSheet'])->name('accounting.reports.balance-sheet');

    // User & Role Management
    Route::resource('roles', RoleController::class);
    Route::resource('users', UserController::class);
    Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::resource('departments', DepartmentController::class);
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
