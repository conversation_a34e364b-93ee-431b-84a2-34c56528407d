<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Task;
use App\Models\Booking;
use App\Models\User;
use App\Models\Client;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        // get dashboard stats based on user role
        $stats = $this->getDashboardStats($user);

        // get recent activities
        $recentActivities = $this->getRecentActivities($user);

        // get upcoming tasks and bookings
        $upcomingTasks = $this->getUpcomingTasks($user);
        $upcomingBookings = $this->getUpcomingBookings($user);

        // get department performance if user has access
        $departmentStats = $this->getDepartmentStats($user);

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'recent_activities' => $recentActivities,
                'upcoming_tasks' => $upcomingTasks,
                'upcoming_bookings' => $upcomingBookings,
                'department_stats' => $departmentStats
            ]
        ]);
    }

    public function web(Request $request)
    {
        $user = $request->user();

        // get dashboard data
        $stats = $this->getDashboardStats($user);
        $recentActivities = $this->getRecentActivities($user);
        $upcomingTasks = $this->getUpcomingTasks($user);
        $upcomingBookings = $this->getUpcomingBookings($user);
        $departmentStats = $this->getDepartmentStats($user);

        return view('dashboard', [
            'stats' => $stats,
            'recent_activities' => $recentActivities,
            'upcoming_tasks' => $upcomingTasks,
            'upcoming_bookings' => $upcomingBookings,
            'department_stats' => $departmentStats
        ]);
    }

    private function getDashboardStats($user): array
    {
        $stats = [];

        if ($user->hasPermission('*') || $user->hasPermission('view_analytics')) {
            // admin/manager stats
            $stats = [
                'total_projects' => Project::count(),
                'active_projects' => Project::where('status', 'active')->count(),
                'completed_projects' => Project::where('status', 'completed')->count(),
                'overdue_projects' => Project::overdue()->count(),
                'total_tasks' => Task::count(),
                'pending_tasks' => Task::where('status', 'pending')->count(),
                'in_progress_tasks' => Task::where('status', 'in_progress')->count(),
                'completed_tasks' => Task::where('status', 'completed')->count(),
                'overdue_tasks' => Task::overdue()->count(),
                'total_bookings' => Booking::count(),
                'todays_bookings' => Booking::today()->count(),
                'upcoming_bookings' => Booking::upcoming()->count(),
                'total_clients' => Client::count(),
                'active_users' => User::active()->count(),
                'revenue_this_month' => $this->getMonthlyRevenue(),
                'utilization_rate' => $this->getStudioUtilization()
            ];
        } else {
            // regular user stats
            $stats = [
                'my_projects' => $user->projects()->count(),
                'my_active_tasks' => $user->assignedTasks()->whereIn('status', ['pending', 'in_progress'])->count(),
                'my_completed_tasks' => $user->assignedTasks()->where('status', 'completed')->count(),
                'my_overdue_tasks' => $user->assignedTasks()->overdue()->count(),
                'my_bookings' => $user->bookings()->upcoming()->count()
            ];
        }

        return $stats;
    }

    private function getRecentActivities($user): array
    {
        $query = AnalyticsEvent::with(['user', 'trackable'])
            ->orderBy('occurred_at', 'desc')
            ->limit(10);

        // filter based on user permissions
        if (!$user->hasPermission('*')) {
            $query->where('user_id', $user->id);
        }

        return $query->get()->map(function ($event) {
            return [
                'id' => $event->id,
                'type' => $event->event_type,
                'category' => $event->event_category,
                'user' => $event->user ? [
                    'id' => $event->user->id,
                    'name' => $event->user->full_name
                ] : null,
                'description' => $this->formatActivityDescription($event),
                'occurred_at' => $event->occurred_at,
                'properties' => $event->properties
            ];
        })->toArray();
    }

    private function getUpcomingTasks($user): array
    {
        $query = Task::with(['project', 'assignedUser', 'department'])
            ->where('due_date', '>=', now())
            ->where('due_date', '<=', now()->addDays(7))
            ->whereIn('status', ['pending', 'in_progress'])
            ->orderBy('due_date');

        // filter based on user permissions
        if (!$user->hasPermission('*')) {
            $query->where(function ($q) use ($user) {
                $q->where('assigned_to', $user->id)
                  ->orWhere('department_id', $user->department_id);
            });
        }

        return $query->limit(10)->get()->map(function ($task) {
            return [
                'id' => $task->id,
                'title' => $task->title,
                'project' => $task->project->title,
                'assigned_to' => $task->assignedUser?->full_name,
                'department' => $task->department?->name,
                'due_date' => $task->due_date,
                'priority' => $task->priority,
                'status' => $task->status,
                'progress' => $task->progress_percentage
            ];
        })->toArray();
    }

    private function getUpcomingBookings($user): array
    {
        $query = Booking::with(['client', 'studioRoom', 'engineer'])
            ->upcoming(7)
            ->orderBy('start_time');

        // filter based on user permissions
        if (!$user->hasPermission('*')) {
            $query->where('engineer_id', $user->id);
        }

        return $query->limit(10)->get()->map(function ($booking) {
            return [
                'id' => $booking->id,
                'client' => $booking->client->name,
                'studio_room' => $booking->studioRoom->name,
                'engineer' => $booking->engineer?->full_name,
                'start_time' => $booking->start_time,
                'end_time' => $booking->end_time,
                'status' => $booking->status,
                'total_cost' => $booking->total_cost
            ];
        })->toArray();
    }

    private function getDepartmentStats($user): array
    {
        if (!$user->hasPermission('*') && !$user->hasPermission('view_department_analytics')) {
            return [];
        }

        return DB::table('departments')
            ->leftJoin('users', 'departments.id', '=', 'users.department_id')
            ->leftJoin('tasks', 'departments.id', '=', 'tasks.department_id')
            ->select([
                'departments.id',
                'departments.name',
                'departments.color_code',
                DB::raw('COUNT(DISTINCT users.id) as user_count'),
                DB::raw('COUNT(DISTINCT CASE WHEN tasks.status IN ("pending", "in_progress") THEN tasks.id END) as active_tasks'),
                DB::raw('COUNT(DISTINCT CASE WHEN tasks.status = "completed" THEN tasks.id END) as completed_tasks'),
                DB::raw('COUNT(DISTINCT CASE WHEN tasks.due_date < NOW() AND tasks.status NOT IN ("completed", "cancelled") THEN tasks.id END) as overdue_tasks')
            ])
            ->where('departments.is_active', true)
            ->groupBy('departments.id', 'departments.name', 'departments.color_code')
            ->get()
            ->toArray();
    }

    private function getMonthlyRevenue(): float
    {
        return Booking::where('start_time', '>=', now()->startOfMonth())
            ->where('start_time', '<=', now()->endOfMonth())
            ->whereIn('status', ['confirmed', 'completed'])
            ->sum('total_cost');
    }

    private function getStudioUtilization(): float
    {
        // calculate average studio utilization for the current month
        $totalRooms = DB::table('studio_rooms')->where('is_available', true)->count();
        
        if ($totalRooms === 0) {
            return 0;
        }

        $daysInMonth = now()->daysInMonth;
        $totalPossibleHours = $totalRooms * $daysInMonth * 24;

        $bookedHours = Booking::where('start_time', '>=', now()->startOfMonth())
            ->where('start_time', '<=', now()->endOfMonth())
            ->whereIn('status', ['confirmed', 'completed'])
            ->get()
            ->sum(function ($booking) {
                return $booking->start_time->diffInHours($booking->end_time);
            });

        return $totalPossibleHours > 0 ? ($bookedHours / $totalPossibleHours) * 100 : 0;
    }

    private function formatActivityDescription($event): string
    {
        $descriptions = [
            'login_success' => 'logged in',
            'project_created' => 'created a new project',
            'project_completed' => 'completed a project',
            'task_started' => 'started working on a task',
            'task_completed' => 'completed a task',
            'booking_created' => 'made a new booking',
            'booking_completed' => 'completed a booking session'
        ];

        $action = $descriptions[$event->event_type] ?? $event->event_type;
        $userName = $event->user?->full_name ?? 'Someone';

        return "{$userName} {$action}";
    }
}
