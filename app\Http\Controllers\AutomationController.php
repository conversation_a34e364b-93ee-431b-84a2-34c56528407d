<?php

namespace App\Http\Controllers;

use App\Models\AutomationWorkflow;
use App\Models\Project;
use App\Models\Task;
use App\Models\User;
use App\Models\Department;
use App\Services\WorkflowEngine;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class AutomationController extends Controller
{
    protected $workflowEngine;

    public function __construct(WorkflowEngine $workflowEngine)
    {
        $this->workflowEngine = $workflowEngine;
    }

    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_workflows')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('dashboard')->with('error', 'Access denied');
        }

        $query = AutomationWorkflow::with(['creator'])
            ->orderBy('created_at', 'desc');

        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->has('trigger_type')) {
            $query->where('trigger_type', $request->trigger_type);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->expectsJson()) {
            $workflows = $query->paginate($request->get('per_page', 15));
            return response()->json([
                'success' => true,
                'data' => [
                    'workflows' => $workflows->items(),
                    'pagination' => [
                        'current_page' => $workflows->currentPage(),
                        'last_page' => $workflows->lastPage(),
                        'per_page' => $workflows->perPage(),
                        'total' => $workflows->total()
                    ]
                ]
            ]);
        }

        $workflows = $query->get();
        $triggerTypes = $this->getTriggerTypes();

        return Inertia::render('Automation/Index', [
            'workflows' => $workflows,
            'triggerTypes' => $triggerTypes,
            'filters' => $request->only(['status', 'trigger_type', 'search'])
        ]);
    }

    public function create(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows')) {
            return redirect()->route('automation.index')->with('error', 'Access denied');
        }

        $triggerTypes = $this->getTriggerTypes();
        $actionTypes = $this->getActionTypes();
        $departments = Department::active()->get();
        $users = User::where('status', 'active')->get();

        return Inertia::render('Automation/Create', [
            'triggerTypes' => $triggerTypes,
            'actionTypes' => $actionTypes,
            'departments' => $departments,
            'users' => $users
        ]);
    }

    public function store(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'trigger_type' => 'required|in:project_created,project_status_changed,task_created,task_completed,booking_confirmed,deadline_approaching',
            'trigger_conditions' => 'nullable|array',
            'actions' => 'required|array|min:1',
            'actions.*.type' => 'required|in:create_task,send_notification,assign_user,update_status,send_email',
            'actions.*.config' => 'required|array',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $data['created_by'] = $user->id;

        $workflow = AutomationWorkflow::create($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Automation workflow created successfully',
                'data' => ['workflow' => $workflow->load('creator')]
            ], 201);
        }

        return redirect()->route('automation.index')->with('success', 'Automation workflow created successfully');
    }

    public function show(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_workflows')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('automation.index')->with('error', 'Access denied');
        }

        $workflow->load(['creator', 'executions' => function ($query) {
            $query->latest()->limit(10);
        }]);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => ['workflow' => $workflow]
            ]);
        }

        return Inertia::render('Automation/Show', [
            'workflow' => $workflow
        ]);
    }

    public function edit(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $workflow->created_by !== $user->id) {
            return redirect()->route('automation.index')->with('error', 'Access denied');
        }

        $triggerTypes = $this->getTriggerTypes();
        $actionTypes = $this->getActionTypes();
        $departments = Department::active()->get();
        $users = User::where('status', 'active')->get();

        return Inertia::render('Automation/Edit', [
            'workflow' => $workflow,
            'triggerTypes' => $triggerTypes,
            'actionTypes' => $actionTypes,
            'departments' => $departments,
            'users' => $users
        ]);
    }

    public function update(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'trigger_conditions' => 'nullable|array',
            'actions' => 'sometimes|array|min:1',
            'actions.*.type' => 'required_with:actions|in:create_task,send_notification,assign_user,update_status,send_email',
            'actions.*.config' => 'required_with:actions|array',
            'is_active' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $workflow->update($validator->validated());

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Automation workflow updated successfully',
                'data' => ['workflow' => $workflow->fresh(['creator'])]
            ]);
        }

        return redirect()->route('automation.index')->with('success', 'Automation workflow updated successfully');
    }

    public function destroy(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $workflow->delete();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Automation workflow deleted successfully'
            ]);
        }

        return redirect()->route('automation.index')->with('success', 'Automation workflow deleted successfully');
    }

    public function toggle(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $workflow->update(['is_active' => !$workflow->is_active]);

        return response()->json([
            'success' => true,
            'message' => $workflow->is_active ? 'Workflow activated' : 'Workflow deactivated',
            'data' => ['workflow' => $workflow->fresh()]
        ]);
    }

    public function execute(Request $request, AutomationWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('execute_workflows')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'trigger_data' => 'required|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->workflowEngine->execute($workflow, $request->trigger_data);

            return response()->json([
                'success' => true,
                'message' => 'Workflow executed successfully',
                'data' => ['result' => $result]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Workflow execution failed: ' . $e->getMessage()
            ], 500);
        }
    }

    protected function getTriggerTypes(): array
    {
        return [
            'project_created' => 'Project Created',
            'project_status_changed' => 'Project Status Changed',
            'task_created' => 'Task Created',
            'task_completed' => 'Task Completed',
            'booking_confirmed' => 'Booking Confirmed',
            'deadline_approaching' => 'Deadline Approaching'
        ];
    }

    protected function getActionTypes(): array
    {
        return [
            'create_task' => 'Create Task',
            'send_notification' => 'Send Notification',
            'assign_user' => 'Assign User',
            'update_status' => 'Update Status',
            'send_email' => 'Send Email'
        ];
    }
}
