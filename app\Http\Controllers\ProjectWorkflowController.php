<?php

namespace App\Http\Controllers;

use App\Models\ProjectWorkflow;
use App\Models\Department;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Inertia\Inertia;

class ProjectWorkflowController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_workflows')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('dashboard')->with('error', 'Access denied');
        }

        $query = ProjectWorkflow::with(['creator'])
            ->orderBy('created_at', 'desc');

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->has('project_type')) {
            $query->where('project_type', $request->project_type);
        }

        if ($request->has('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        if ($request->expectsJson()) {
            $workflows = $query->paginate($request->get('per_page', 15));
            return response()->json([
                'success' => true,
                'data' => [
                    'workflows' => $workflows->items(),
                    'pagination' => [
                        'current_page' => $workflows->currentPage(),
                        'last_page' => $workflows->lastPage(),
                        'per_page' => $workflows->perPage(),
                        'total' => $workflows->total()
                    ]
                ]
            ]);
        }

        $workflows = $query->get();

        return view('workflows.index', [
            'workflows' => $workflows,
            'filters' => $request->only(['search', 'project_type', 'status'])
        ]);
    }

    public function create(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows')) {
            return redirect()->route('workflows.index')->with('error', 'Access denied');
        }

        $departments = Department::active()->get();

        return view('workflows.create', [
            'departments' => $departments,
            'template' => $request->get('template')
        ]);
    }

    public function store(Request $request)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows')) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'project_type' => 'required|in:music_production,video_production,podcast,mixing_mastering,voice_over,sound_design,live_recording',
            'stages' => 'required|array|min:1',
            'stages.*.name' => 'required|string|max:255',
            'stages.*.description' => 'nullable|string',
            'stages.*.order' => 'required|integer|min:1',
            'stages.*.estimated_duration' => 'nullable|integer|min:1',
            'stages.*.required_skills' => 'nullable|string',
            'stages.*.department_id' => 'nullable|exists:departments,id',
            'automation_rules' => 'nullable|array',
            'automation_rules.*.trigger' => 'required_with:automation_rules|string',
            'automation_rules.*.action' => 'required_with:automation_rules|string',
            'automation_rules.*.config' => 'nullable|string',
            'is_default' => 'boolean',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();
        $data['slug'] = Str::slug($data['name']);
        $data['created_by'] = $user->id;

        // Process stages
        $stages = [];
        foreach ($data['stages'] as $stage) {
            $stageData = [
                'name' => $stage['name'],
                'description' => $stage['description'] ?? null,
                'order' => (int) $stage['order'],
                'estimated_duration' => isset($stage['estimated_duration']) ? (int) $stage['estimated_duration'] : null,
                'required_skills' => isset($stage['required_skills']) ? array_map('trim', explode(',', $stage['required_skills'])) : [],
                'department_id' => $stage['department_id'] ?? null
            ];
            $stages[] = $stageData;
        }
        $data['stages'] = $stages;

        // Process automation rules
        if (isset($data['automation_rules'])) {
            $automationRules = [];
            foreach ($data['automation_rules'] as $rule) {
                $ruleData = [
                    'trigger' => $rule['trigger'],
                    'action' => $rule['action'],
                    'config' => isset($rule['config']) ? json_decode($rule['config'], true) : []
                ];
                $automationRules[] = $ruleData;
            }
            $data['automation_rules'] = $automationRules;
        }

        // If this is set as default, unset other defaults for this project type
        if ($data['is_default'] ?? false) {
            ProjectWorkflow::where('project_type', $data['project_type'])
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $workflow = ProjectWorkflow::create($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Workflow created successfully',
                'data' => ['workflow' => $workflow->load('creator')]
            ], 201);
        }

        return redirect()->route('workflows.index')->with('success', 'Workflow created successfully');
    }

    public function show(Request $request, ProjectWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('view_workflows')) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }
            return redirect()->route('workflows.index')->with('error', 'Access denied');
        }

        $workflow->load(['creator']);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'data' => ['workflow' => $workflow]
            ]);
        }

        return view('workflows.show', [
            'workflow' => $workflow
        ]);
    }

    public function edit(Request $request, ProjectWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows') && $workflow->created_by !== $user->id) {
            return redirect()->route('workflows.index')->with('error', 'Access denied');
        }

        $departments = Department::active()->get();

        return view('workflows.edit', [
            'workflow' => $workflow,
            'departments' => $departments
        ]);
    }

    public function update(Request $request, ProjectWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'project_type' => 'sometimes|in:music_production,video_production,podcast,mixing_mastering,voice_over,sound_design,live_recording',
            'stages' => 'sometimes|array|min:1',
            'stages.*.name' => 'required_with:stages|string|max:255',
            'stages.*.description' => 'nullable|string',
            'stages.*.order' => 'required_with:stages|integer|min:1',
            'stages.*.estimated_duration' => 'nullable|integer|min:1',
            'stages.*.required_skills' => 'nullable|string',
            'stages.*.department_id' => 'nullable|exists:departments,id',
            'automation_rules' => 'nullable|array',
            'automation_rules.*.trigger' => 'required_with:automation_rules|string',
            'automation_rules.*.action' => 'required_with:automation_rules|string',
            'automation_rules.*.config' => 'nullable|string',
            'is_default' => 'sometimes|boolean',
            'is_active' => 'sometimes|boolean'
        ]);

        if ($validator->fails()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        $data = $validator->validated();

        if (isset($data['name'])) {
            $data['slug'] = Str::slug($data['name']);
        }

        // Process stages if provided
        if (isset($data['stages'])) {
            $stages = [];
            foreach ($data['stages'] as $stage) {
                $stageData = [
                    'name' => $stage['name'],
                    'description' => $stage['description'] ?? null,
                    'order' => (int) $stage['order'],
                    'estimated_duration' => isset($stage['estimated_duration']) ? (int) $stage['estimated_duration'] : null,
                    'required_skills' => isset($stage['required_skills']) ? array_map('trim', explode(',', $stage['required_skills'])) : [],
                    'department_id' => $stage['department_id'] ?? null
                ];
                $stages[] = $stageData;
            }
            $data['stages'] = $stages;
        }

        // Process automation rules if provided
        if (isset($data['automation_rules'])) {
            $automationRules = [];
            foreach ($data['automation_rules'] as $rule) {
                $ruleData = [
                    'trigger' => $rule['trigger'],
                    'action' => $rule['action'],
                    'config' => isset($rule['config']) ? json_decode($rule['config'], true) : []
                ];
                $automationRules[] = $ruleData;
            }
            $data['automation_rules'] = $automationRules;
        }

        // If this is set as default, unset other defaults for this project type
        if (($data['is_default'] ?? false) && isset($data['project_type'])) {
            ProjectWorkflow::where('project_type', $data['project_type'])
                ->where('id', '!=', $workflow->id)
                ->where('is_default', true)
                ->update(['is_default' => false]);
        }

        $workflow->update($data);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Workflow updated successfully',
                'data' => ['workflow' => $workflow->fresh(['creator'])]
            ]);
        }

        return redirect()->route('workflows.index')->with('success', 'Workflow updated successfully');
    }

    public function destroy(Request $request, ProjectWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $workflow->delete();

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Workflow deleted successfully'
            ]);
        }

        return redirect()->route('workflows.index')->with('success', 'Workflow deleted successfully');
    }

    public function toggle(Request $request, ProjectWorkflow $workflow)
    {
        $user = $request->user();

        if (!$user->hasPermission('*') && !$user->hasPermission('manage_workflows') && $workflow->created_by !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied'
            ], 403);
        }

        $workflow->update(['is_active' => !$workflow->is_active]);

        return response()->json([
            'success' => true,
            'message' => $workflow->is_active ? 'Workflow activated' : 'Workflow deactivated',
            'data' => ['workflow' => $workflow->fresh()]
        ]);
    }
}
