@extends('layouts.app')

@section('title', 'Accounting Dashboard')

@section('dashboard-content')
<div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Accounting Dashboard</h1>
    <div class="flex space-x-3">
        <button onclick="openCreateInvoiceModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-plus"></i>
            <span>New Invoice</span>
        </button>
        <button onclick="openCreateExpenseModal()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <i class="fas fa-receipt"></i>
            <span>Add Expense</span>
        </button>
    </div>
</div>

<!-- Summary Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
                <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                <p id="totalRevenue" class="text-2xl font-bold text-gray-900">₦0.00</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-red-100 rounded-lg">
                <i class="fas fa-credit-card text-red-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Total Expenses</p>
                <p id="totalExpenses" class="text-2xl font-bold text-gray-900">₦0.00</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
                <i class="fas fa-chart-line text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Net Profit</p>
                <p id="netProfit" class="text-2xl font-bold text-gray-900">₦0.00</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
                <i class="fas fa-clock text-yellow-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-600">Outstanding</p>
                <p id="outstanding" class="text-2xl font-bold text-gray-900">₦0.00</p>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 gap-4">
            <button onclick="window.location.href='/accounting/invoices'" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                <i class="fas fa-file-invoice text-indigo-600 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">Invoices</p>
            </button>
            <button onclick="window.location.href='/accounting/expenses'" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                <i class="fas fa-receipt text-green-600 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">Expenses</p>
            </button>
            <button onclick="window.location.href='/accounting/reports'" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                <i class="fas fa-chart-bar text-blue-600 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">Reports</p>
            </button>
            <button onclick="window.location.href='/accounting/transactions'" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 text-center">
                <i class="fas fa-exchange-alt text-purple-600 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">Transactions</p>
            </button>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div id="recentActivity" class="space-y-3">
            <!-- Recent activity will be loaded here -->
        </div>
    </div>
</div>

<!-- Create Invoice Modal -->
<div id="createInvoiceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Create Invoice</h3>
            <button onclick="closeCreateInvoiceModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="invoiceFormContainer">
            <!-- Invoice form will be loaded here -->
        </div>
    </div>
</div>

<!-- Create Expense Modal -->
<div id="createExpenseModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Add Expense</h3>
            <button onclick="closeCreateExpenseModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div id="expenseFormContainer">
            <!-- Expense form will be loaded here -->
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadDashboardData();
    loadRecentActivity();
});

function loadDashboardData() {
    $.ajax({
        url: '/api/accounting/dashboard',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    })
    .done(function(response) {
        if (response.success) {
            updateDashboardCards(response.data);
        }
    })
    .fail(function() {
        showNotification('Error loading dashboard data', 'error');
    });
}

function updateDashboardCards(data) {
    $('#totalRevenue').text('₦' + formatNumber(data.monthly_summary.revenue));
    $('#totalExpenses').text('₦' + formatNumber(data.monthly_summary.expenses));
    $('#netProfit').text('₦' + formatNumber(data.monthly_summary.revenue - data.monthly_summary.expenses));
    $('#outstanding').text('₦' + formatNumber(data.accounts_receivable.total));
}

function loadRecentActivity() {
    $.ajax({
        url: '/accounting/transactions',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .done(function(response) {
        if (response.success) {
            renderRecentActivity(response.data.transactions.slice(0, 5));
        }
    })
    .fail(function() {
        $('#recentActivity').html('<p class="text-gray-500 text-sm">Unable to load recent activity</p>');
    });
}

function renderRecentActivity(transactions) {
    const container = $('#recentActivity');
    
    if (transactions.length === 0) {
        container.html('<p class="text-gray-500 text-sm">No recent activity</p>');
        return;
    }

    const html = transactions.map(transaction => `
        <div class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
            <div class="flex items-center">
                <div class="p-2 ${transaction.type === 'payment' ? 'bg-green-100' : 'bg-red-100'} rounded-lg mr-3">
                    <i class="fas ${transaction.type === 'payment' ? 'fa-arrow-down text-green-600' : 'fa-arrow-up text-red-600'}"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-900">${transaction.description}</p>
                    <p class="text-xs text-gray-500">${formatDate(transaction.date)}</p>
                </div>
            </div>
            <span class="text-sm font-medium ${transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}">
                ₦${formatNumber(Math.abs(transaction.amount))}
            </span>
        </div>
    `).join('');

    container.html(html);
}

function openCreateInvoiceModal() {
    $('#createInvoiceModal').removeClass('hidden');
    loadInvoiceForm();
}

function closeCreateInvoiceModal() {
    $('#createInvoiceModal').addClass('hidden');
}

function openCreateExpenseModal() {
    $('#createExpenseModal').removeClass('hidden');
    loadExpenseForm();
}

function closeCreateExpenseModal() {
    $('#createExpenseModal').addClass('hidden');
}

function loadInvoiceForm() {
    $('#invoiceFormContainer').html('<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-gray-400"></i></div>');
    
    $.ajax({
        url: '/accounting/create',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .done(function(response) {
        if (response.success) {
            renderInvoiceForm(response.data);
        }
    })
    .fail(function() {
        $('#invoiceFormContainer').html('<p class="text-red-500 text-center">Error loading form</p>');
    });
}

function loadExpenseForm() {
    $('#expenseFormContainer').html('<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-gray-400"></i></div>');
    
    $.ajax({
        url: '/accounting/expenses/create',
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .done(function(response) {
        if (response.success) {
            renderExpenseForm(response.data);
        }
    })
    .fail(function() {
        $('#expenseFormContainer').html('<p class="text-red-500 text-center">Error loading form</p>');
    });
}

function renderInvoiceForm(data) {
    // This would render a complete invoice form
    $('#invoiceFormContainer').html('<p class="text-center">Invoice form would be rendered here</p>');
}

function renderExpenseForm(data) {
    // This would render a complete expense form
    $('#expenseFormContainer').html('<p class="text-center">Expense form would be rendered here</p>');
}

function formatNumber(num) {
    return new Intl.NumberFormat('en-NG').format(num || 0);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-NG');
}
</script>
@endsection
