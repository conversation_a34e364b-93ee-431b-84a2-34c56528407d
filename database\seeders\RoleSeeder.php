<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        $roles = [
            // Admin roles
            [
                'name' => 'Super Admin',
                'slug' => 'super-admin',
                'description' => 'Full system access',
                'department_id' => 1, // Administration
                'level' => 10,
                'is_system_role' => true,
                'permissions' => json_encode(['*'])
            ],
            [
                'name' => 'Studio Manager',
                'slug' => 'studio-manager',
                'description' => 'Overall studio operations management',
                'department_id' => 1,
                'level' => 9,
                'is_system_role' => true,
                'permissions' => json_encode(['manage_projects', 'manage_bookings', 'view_analytics', 'manage_users'])
            ],
            
            // Project Management
            [
                'name' => 'Project Manager',
                'slug' => 'project-manager',
                'description' => 'Manages projects and coordinates teams',
                'department_id' => 2,
                'level' => 7,
                'is_system_role' => true,
                'permissions' => json_encode(['create_projects', 'assign_tasks', 'view_project_analytics'])
            ],
            [
                'name' => 'Project Coordinator',
                'slug' => 'project-coordinator',
                'description' => 'Assists in project coordination',
                'department_id' => 2,
                'level' => 5,
                'is_system_role' => true,
                'permissions' => json_encode(['update_tasks', 'communicate_with_clients'])
            ],
            
            // Audio Engineering
            [
                'name' => 'Lead Audio Engineer',
                'slug' => 'lead-audio-engineer',
                'description' => 'Senior audio engineering role',
                'department_id' => 3,
                'level' => 8,
                'is_system_role' => true,
                'permissions' => json_encode(['manage_audio_equipment', 'lead_sessions', 'mentor_juniors'])
            ],
            [
                'name' => 'Audio Engineer',
                'slug' => 'audio-engineer',
                'description' => 'Recording and mixing engineer',
                'department_id' => 3,
                'level' => 6,
                'is_system_role' => true,
                'permissions' => json_encode(['operate_equipment', 'record_sessions', 'mix_tracks'])
            ],
            [
                'name' => 'Assistant Engineer',
                'slug' => 'assistant-engineer',
                'description' => 'Junior audio engineering role',
                'department_id' => 3,
                'level' => 3,
                'is_system_role' => true,
                'permissions' => json_encode(['setup_equipment', 'assist_sessions'])
            ],
            
            // Music Production
            [
                'name' => 'Music Producer',
                'slug' => 'music-producer',
                'description' => 'Creates and produces music',
                'department_id' => 4,
                'level' => 7,
                'is_system_role' => true,
                'permissions' => json_encode(['create_beats', 'arrange_music', 'direct_sessions'])
            ],
            [
                'name' => 'Beat Maker',
                'slug' => 'beat-maker',
                'description' => 'Specializes in beat creation',
                'department_id' => 4,
                'level' => 5,
                'is_system_role' => true,
                'permissions' => json_encode(['create_beats', 'use_production_software'])
            ],
            
            // Video Production
            [
                'name' => 'Video Director',
                'slug' => 'video-director',
                'description' => 'Directs video productions',
                'department_id' => 5,
                'level' => 8,
                'is_system_role' => true,
                'permissions' => json_encode(['direct_videos', 'manage_video_crew'])
            ],
            [
                'name' => 'Camera Operator',
                'slug' => 'camera-operator',
                'description' => 'Operates cameras and video equipment',
                'department_id' => 5,
                'level' => 5,
                'is_system_role' => true,
                'permissions' => json_encode(['operate_cameras', 'setup_lighting'])
            ],
            
            // Client Relations
            [
                'name' => 'Client Manager',
                'slug' => 'client-manager',
                'description' => 'Manages client relationships',
                'department_id' => 7,
                'level' => 6,
                'is_system_role' => true,
                'permissions' => json_encode(['manage_clients', 'handle_bookings', 'process_payments'])
            ],
            
            // General
            [
                'name' => 'Client',
                'slug' => 'client',
                'description' => 'External client access',
                'department_id' => null,
                'level' => 1,
                'is_system_role' => true,
                'permissions' => json_encode(['view_own_projects', 'make_bookings', 'upload_files'])
            ],
            [
                'name' => 'Developer',
                'slug' => 'developer',
                'description' => 'System developer access',
                'department_id' => 1,
                'level' => 9,
                'is_system_role' => true,
                'permissions' => json_encode(['system_maintenance', 'debug_access', 'manage_integrations'])
            ]
        ];

        foreach ($roles as $role) {
            DB::table('roles')->insert(array_merge($role, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}
